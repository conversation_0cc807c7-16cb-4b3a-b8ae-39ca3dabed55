﻿using AutoMapper;
using MediatR;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestById;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetExcelReport
{
    public class GetExcelReportHandler : IRequestHandler<GetExcelReportQuery, BaseReponseGeneric<ExportExcelReportDto>>
    {
        
        

        private readonly IUnitOfWork _unitOfWork;

        private readonly IMapper _mapper;

        public GetExcelReportHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
           
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }


        public async Task<BaseReponseGeneric<ExportExcelReportDto>> Handle(GetExcelReportQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<ExportExcelReportDto>();

            try
            {
                
                var excelExportReport = await _unitOfWork.GitPullRequestsRepository.GetExcelReportAsync(request.Uuid);

                if (excelExportReport is not null)
                {
                    response.Data = _mapper.Map<ExportExcelReportDto>(excelExportReport);
                    response.Succcess = true;
                    response.Message = "Excel Export File fetched successfully.";

                }
                else
                {
                    response.Succcess = false;
                    response.Message = "Excel Export File not found.";
                }
            }
            catch (Exception ex)
            {
                response.Succcess = false;
                response.Message = $"Error occurred: {ex.Message}";
            }

            return response;
        }
    }
}
