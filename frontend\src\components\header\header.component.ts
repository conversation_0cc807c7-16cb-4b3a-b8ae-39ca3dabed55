import { Component, OnInit, ViewChild, On<PERSON><PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { RouterModule, Router, NavigationEnd } from '@angular/router';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';
import { MatSidenav } from '@angular/material/sidenav';
import { filter, takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { PrDataService } from 'src/services/pr-data.service';
import { UserService } from '../../services/user.service';

@Component({
  selector: 'app-header',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    RouterModule,
    MatSidenavModule,
    MatListModule,
    MatToolbarModule,
    MatTooltipModule,
    MatDividerModule,
  ],
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss',
  animations: [
    trigger('slideInOut', [
      state('in', style({
        transform: 'translateX(0)',
        opacity: 1
      })),
      state('out', style({
        transform: 'translateX(-100%)',
        opacity: 0
      })),
      transition('in => out', animate('300ms ease-out')),
      transition('out => in', animate('300ms ease-in'))
    ])
  ]
})
export class HeaderComponent implements OnInit, OnDestroy {
  isLoggedIn = false;
  currentRoute = '';
  userDisplayName = 'DevOps User';
  userEmail = '';

  @ViewChild('sidenav') sidenav!: MatSidenav;

  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private prDataService: PrDataService,
    private userService: UserService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit() {
    // Subscribe to credentials to determine login status
    this.prDataService.credentials$
      .pipe(takeUntil(this.destroy$))
      .subscribe((credentials) => {
        this.isLoggedIn = !!credentials;
        if (credentials && credentials.patToken) {
          this.loadUserInfo(credentials.patToken);
        }
        this.cdr.markForCheck();
      });

    // Track current route for highlighting active nav item and close sidebar on navigation
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.currentRoute = this.router.url;

        // Close the sidenav when a page is navigated to
        if (this.sidenav && this.sidenav.opened) {
          this.sidenav.close();
        }
        this.cdr.markForCheck();
      });
  }

  private loadUserInfo(patToken: string): void {
    this.userService.getCurrentUser(patToken)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (user) => {
          if (user) {
            this.userDisplayName = user.displayName || 'DevOps User';
            this.userEmail = user.emailAddress || '';
          }
          this.cdr.markForCheck();
        },
        error: (error) => {
          console.warn('Failed to load user info:', error);
          this.userDisplayName = 'DevOps User';
          this.userEmail = '';
          this.cdr.markForCheck();
        }
      });
  }

  logout() {
    // Clear credentials
    this.prDataService.clearCredentials();
    // Navigate to login page
    setTimeout(() => {
      this.router.navigate(['/'], { replaceUrl: true });
    }, 100);
  }

  toggleSidenav() {
    if (this.sidenav) {
      this.sidenav.toggle();

      // Force a reflow to ensure animation works properly
      setTimeout(() => {
        // Trigger any layout adjustments needed after toggle
        window.dispatchEvent(new Event('resize'));
      }, 300); // Match this timing with the animation duration
    }
  }

  get sideNavState() {
    return this.sidenav?.opened ? 'in' : 'out';
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
