import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { RouterModule, Router, NavigationEnd } from '@angular/router';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSidenav } from '@angular/material/sidenav';
import { filter } from 'rxjs/operators';
import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { PrDataService } from 'src/services/pr-data.service';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    RouterModule,
    MatSidenavModule,
    MatListModule,
    MatToolbarModule,
    MatTooltipModule,
  ],
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss',
  // animations: [
  //   trigger('slideInOut', [
  //     state('in', style({
  //       transform: 'translateX(0)',
  //       opacity: 1
  //     })),
  //     state('out', style({
  //       transform: 'translateX(-100%)',
  //       opacity: 0
  //     })),
  //     transition('in => out', animate('300ms ease-out')),
  //     transition('out => in', animate('300ms ease-in'))
  //   ])
  // ]
})
export class HeaderComponent implements OnInit {
  isLoggedIn = false;
  currentRoute = '';
  @ViewChild('sidenav') sidenav!: MatSidenav;

  constructor(private router: Router, private prDataService: PrDataService) {}

  ngOnInit() {
    // Subscribe to credentials to determine login status
    this.prDataService.credentials$.subscribe((credentials) => {
      this.isLoggedIn = !!credentials;
    });

    // Track current route for highlighting active nav item and close sidebar on navigation
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentRoute = this.router.url;

        // Close the sidenav when a page is navigated to
        if (this.sidenav && this.sidenav.opened) {
          this.sidenav.close();
        }
      });
  }

  logout() {
    // Clear credentials
    this.prDataService.clearCredentials();
    // Navigate to login page
    setTimeout(() => {
      this.router.navigate(['/'], { replaceUrl: true });
    }, 100);
  }

  toggleSidenav() {
    if (this.sidenav) {
      this.sidenav.toggle();

      // Force a reflow to ensure animation works properly
      setTimeout(() => {
        // Trigger any layout adjustments needed after toggle
        window.dispatchEvent(new Event('resize'));
      }, 300); // Match this timing with the animation duration
    }
  }

  get sideNavState() {
    return this.sidenav?.opened ? 'in' : 'out';
  }
}
