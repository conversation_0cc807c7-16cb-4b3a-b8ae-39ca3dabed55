[33m❯[39m Building...
[32m✔[39m Building...
[37m[1mInitial chunk files[22m  [2m | [22m[1mNames[22m        [2m | [22m[1mRaw size[22m[2m | [22m[1mEstimated transfer size[22m[39m
[37m[32mmain-74CEJQUO.js[39m[37m     [2m | [22m[2mmain[22m         [2m | [22m [36m1.55 MB[39m[37m[2m | [22m              [36m328.36 kB[39m[37m[39m
[37m[32mpolyfills-FFHMD2TL.js[39m[37m[2m | [22m[2mpolyfills[22m    [2m | [22m[36m34.52 kB[39m[37m[2m | [22m               [36m11.28 kB[39m[37m[39m
[37m[32mstyles-NYH7KW7P.css[39m[37m  [2m | [22m[2mstyles[22m       [2m | [22m [36m3.54 kB[39m[37m[2m | [22m              [36m770 bytes[39m[37m[39m
[37m[39m
[37m[1m [22m                    [2m | [22m[1mInitial total[22m[2m | [22m [1m[93m1.58 MB[39m[37m[22m[2m | [22m              [1m340.41 kB[22m[39m
[37m[39m
[37mApplication bundle generation complete. [12.911 seconds][39m
[37m[39m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1m7 repetitive deprecation warnings omitted.[39m[22m
[1m[33mRun in verbose mode to see all warnings.[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m  null[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/header/header.component.scss:2:8:[39m[22m
[1m[33m[37m      2 │ @import [32m[37m'../../styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pat-login/pat-login.component.scss:2:8:[39m[22m
[1m[33m[37m      2 │ @import [32m[37m'../../styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mTS-998113: HeaderComponent is not used within the template of PatLoginComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pat-login/pat-login.component.ts:37:4:[39m[22m
[1m[33m[37m      37 │     [32mHeaderComponent[37m,[39m[22m
[1m[33m         ╵     [32m~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-data/pr-data.component.scss:2:8:[39m[22m
[1m[33m[37m      2 │ @import [32m[37m'../../styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8107: The left side of this optional chain operation does not include 'null' or 'undefined' in its type, therefore the '?.' operator can be replaced with the '.' operator.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-de[39m[22m[1m[33mtails.component.html:43:70:[39m[22m
[1m[33m[37m      43 │ ...lass="author-name">{{ pullRequest.createdBy?.[32mdisplayName[37m }}</span>[39m[22m
[1m[33m         ╵                                                 [32m~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  Error occurs in the template of component PrDetailsComponent.[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.ts:50:15:[39m[22m
[1m[33m[37m      50 │   templateUrl: [32m'./pr-details.component.html'[37m,[39m[22m
[1m[33m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8107: The left side of this optional chain operation does not include 'null' or 'undefined' in its type, therefore the '?.' operator can be replaced with the '.' operator.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.html:233:78:[39m[22m
[1m[33m[37m      233 │ ...n class="comment-author">{{ comment.author?.[32mdisplayName[37m }}</span>[39m[22m
[1m[33m          ╵                                                [32m~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  Error occurs in the template of component PrDetailsComponent.[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.ts:50:15:[39m[22m
[1m[33m[37m      50 │   templateUrl: [32m'./pr-details.component.html'[37m,[39m[22m
[1m[33m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:2:8:[39m[22m
[1m[33m[37m      2 │ @import [32m[37m'../../styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:3:8:[39m[22m
[1m[33m[37m      3 │ @import [32m[37m'../../styles/components.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and[39m[22m[1m[33m automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:4:8:[39m[22m
[1m[33m[37m      4 │ @import [32m[37m'../../styles/pr-components.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:5:8:[39m[22m
[1m[33m[37m      5 │ @import [32m[37m'../../styles/pr-variables.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:213:68:[39m[22m
[1m[33m[37m      213 │ ...gradient(135deg, $primary-color, [32m[37mdarken($primary-color, 10%)); }[39m[22m
[1m[33m          ╵                                     [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Global built-in functions are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  Use color.adjust instead.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:213:68:[39m[22m
[1m[33m[37m      213 │ ...gradient(135deg, $primary-color, [32m[37mdarken($primary-color, 10%)); }[39m[22m
[1m[33m          ╵                                     [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  darken() is deprecated. Suggestions:[39m[22m
[1m[33m  [39m[22m
[1m[33m  color.scale($color, $lightness: -28.4916201117%)[39m[22m
[1m[33m  color.adjust($color, $lightness: -10%)[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info: [4mhttps://sass-lang.com/d/color-functions[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1[39m[22m[1m[33mm[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:214:68:[39m[22m
[1m[33m[37m      214 │ ...r-gradient(135deg, $active-color, [32m[37mdarken($active-color, 10%)); }[39m[22m
[1m[33m          ╵                                      [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Global built-in functions are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  Use color.adjust instead.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:214:68:[39m[22m
[1m[33m[37m      214 │ ...r-gradient(135deg, $active-color, [32m[37mdarken($active-color, 10%)); }[39m[22m
[1m[33m          ╵                                      [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  darken() is deprecated. Suggestions:[39m[22m
[1m[33m  [39m[22m
[1m[33m  color.scale($color, $lightness: -20%)[39m[22m
[1m[33m  color.adjust($color, $lightness: -10%)[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info: [4mhttps://sass-lang.com/d/color-functions[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:215:69:[39m[22m
[1m[33m[37m      215 │ ...ear-gradient(135deg, $fixed-color, [32m[37mdarken($fixed-color, 10%)); }[39m[22m
[1m[33m          ╵                                       [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Global built-in functions are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  Use color.adjust instead.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:215:69:[39m[22m
[1m[33m[37m      215 │ ...ear-gradient(135deg, $fixed-color, [32m[37mdarken($fixed-color, 10%)); }[39m[22m
[1m[33m          ╵                                       [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  darken() is deprecated. Suggestions:[39m[22m
[1m[33m  [39m[22m
[1m[33m  color.scale($color, $lightness: -20.3187250996%)[39m[22m
[1m[33m  color.adjust($color, $lightness: -10%)[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info: [4mhttps://sass-lang.com/d/color-functions[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[3[39m[22m[1m[33m3m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:376:24:[39m[22m
[1m[33m[37m      376 │       background-color: [32m[37mmap-get($thread-status-colors, active);[39m[22m
[1m[33m          ╵                         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Global built-in functions are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  Use map.get instead.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:377:13:[39m[22m
[1m[33m[37m      377 │       color: [32m[37mmap-get($thread-status-text-colors, active);[39m[22m
[1m[33m          ╵              [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Global built-in functions are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  Use map.get instead.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:593:4:[39m[22m
[1m[33m[37m      593 │     [32m[37mgrid-template-columns: repeat(auto-fill, minmax(350px, 1fr));[39m[22m
[1m[33m          ╵     [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass's behavior for declarations that appear after nested[39m[22m
[1m[33m  rules will be changing to match the behavior specified by CSS in an upcoming[39m[22m
[1m[33m  version. To keep the existing behavior, move the declaration above the nested[39m[22m
[1m[33m  rule. To opt into the new behavior, wrap the declaration in `& {}`.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info: [4mhttps://sass-lang.com/d/mixed-decls[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:785:4:[39m[22m
[1m[33m[37m      785 │     [32m[37mgrid-template-columns: repeat(auto-fill, minmax(200px, 1fr));[39m[22m
[1m[33m          ╵     [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass's behavior for declarations that appear after nested[39m[22m
[1m[33m  rules will be changing to match the behavior spec[39m[22m[1m[33mified by CSS in an upcoming[39m[22m
[1m[33m  version. To keep the existing behavior, move the declaration above the nested[39m[22m
[1m[33m  rule. To opt into the new behavior, wrap the declaration in `& {}`.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info: [4mhttps://sass-lang.com/d/mixed-decls[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/styles/pr-components.scss:4:8:[39m[22m
[1m[33m[37m      4 │ @import [32m[37m'./components.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mbundle initial exceeded maximum budget. Budget 1.00 MB was not met by 583.31 kB with a total of 1.58 MB.[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1msrc/components/pat-login/pat-login.component.scss exceeded maximum budget. Budget 8.00 kB was not met by 1.14 kB with a total of 9.14 kB.[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1msrc/components/pr-details/pr-details.component.scss exceeded maximum budget. Budget 8.00 kB was not met by 14.65 kB with a total of 22.65 kB.[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1msrc/components/pr-data/pr-data.component.scss exceeded maximum budget. Budget 8.00 kB was not met by 11.74 kB with a total of 19.75 kB.[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mModule 'ansi-to-html' used by 'src/components/dialogs/build-logs-dialog.component.ts' is not ESM[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  CommonJS or AMD dependencies can cause optimization bailouts.[39m[22m
[1m[33m  For more information see: [4mhttps://angular.dev/tools/cli/build#configuring-commonjs-dependencies[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[37mOutput location: D:\iTwo4.0_CodeBase\tools-git-analytics\frontend\dist\frontend[39m
[37m[39m
