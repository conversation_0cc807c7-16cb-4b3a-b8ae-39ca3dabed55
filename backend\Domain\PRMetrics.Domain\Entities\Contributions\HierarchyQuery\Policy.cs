﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Newtonsoft.Json;

namespace RIB.PRMetrics.Domain.Entities.Contributions.HierarchyQuery
{
    /// <summary>
    /// Represents the configuration for pull request policies such as squash merge, rebase, approvals, etc.
    /// </summary>
    public class Policy
    {
        /// <summary>
        /// Gets or sets a value indicating whether squash merge is allowed.
        /// </summary>
        [JsonProperty("useSquashMerge")] 
        public bool UseSquashMerge { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether no-fast-forward merges are allowed.
        /// </summary>
        [JsonProperty("allowNoFastForward")] 
        public bool AllowNoFastForward { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether squash is allowed for merging pull requests.
        /// </summary>
        [JsonProperty("allowSquash")] 
        public bool AllowSquash { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether rebasing is allowed for merging pull requests.
        /// </summary>
        [Json<PERSON>roperty("AllowRebase")] 
        public bool allowRebase { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether rebase merges are allowed.
        /// </summary>
        [JsonProperty("allowRebaseMerge")] 
        public bool AllowRebaseMerge { get; set; }

        /// <summary>
        /// Gets or sets the evaluation identifier for the policy.
        /// </summary>
        [JsonProperty("evaluationId")] 
        public string EvaluationId { get; set; }

        /// <summary>
        /// Gets or sets the configuration identifier associated with the policy.
        /// </summary>
        [JsonProperty("configurationId")] 
        public int ConfigurationId { get; set; }

        /// <summary>
        /// Gets or sets the type of policy.
        /// </summary>
        [JsonProperty("policyType")] 
        public PolicyType PolicyType { get; set; }

        /// <summary>
        /// Gets or sets the status of the policy.
        /// </summary>
        [JsonProperty("status")] 
        public int Status { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this policy is blocking.
        /// </summary>
        [JsonProperty("isBlocking")] 
        public bool IsBlocking { get; set; }

        /// <summary>
        /// Gets or sets the display name for the policy.
        /// </summary>
        [JsonProperty("displayName")] 
        public string DisplayName { get; set; }

        /// <summary>
        /// Gets or sets the display text for the policy.
        /// </summary>
        [JsonProperty("displayText")] 
        public string DisplayText { get; set; }

        /// <summary>
        /// Gets or sets the display status for the policy.
        /// </summary>
        [JsonProperty("displayStatus")] 
        public int DisplayStatus { get; set; }

        /// <summary>
        /// Gets or sets the minimum number of approvers required for the policy to be satisfied.
        /// </summary>
        [JsonProperty("minimumApproverCount")] 
        public int? MinimumApproverCount { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the creator's vote counts towards the approval count.
        /// </summary>
        [JsonProperty("creatorVoteCounts")] 
        public bool? CreatorVoteCounts { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether a vote is required on the last iteration of the pull request.
        /// </summary>
        [JsonProperty("needsVoteOnLastIteration")] 
        public bool? NeedsVoteOnLastIteration { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the minimum approver count requirement has been satisfied.
        /// </summary>
        [JsonProperty("isMinimumApproverCountSatisfied")] 
        public bool? IsMinimumApproverCountSatisfied { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether there are iterations that have not been approved.
        /// </summary>
        [JsonProperty("hasNotApprovedIterations")] 
        public bool? HasNotApprovedIterations { get; set; }

        /// <summary>
        /// Gets or sets the build identifier associated with the policy.
        /// </summary>
        [JsonProperty("buildId")] 
        public int? BuildId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether a valid build exists for the policy.
        /// </summary>
        [JsonProperty("validBuildExists")] 
        public bool? ValidBuildExists { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the build is expired.
        /// </summary>
        [JsonProperty("buildIsExpired")] 
        public bool? BuildIsExpired { get; set; }

        /// <summary>
        /// Gets or sets the expiration date for the policy, if applicable.
        /// </summary>
        [JsonProperty("expirationDate")] 
        public object ExpirationDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the server will queue a build for the policy.
        /// </summary>
        [JsonProperty("serverWillQueueBuild")] 
        public bool? ServerWillQueueBuild { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether only manual queueing is allowed for the policy.
        /// </summary>
        [JsonProperty("manualQueueOnly")] 
        public bool? ManualQueueOnly { get; set; }

        /// <summary>
        /// Gets or sets a preview of the build output for the policy.
        /// </summary>
        [JsonProperty("buildOutputPreview")] 
        public object BuildOutputPreview { get; set; }

        /// <summary>
        /// Gets or sets the list of required reviewer IDs for the policy.
        /// </summary>
        [JsonProperty("requiredReviewerIds")] 
        public List<string> RequiredReviewerIds { get; set; }

        /// <summary>
        /// Gets or sets the message associated with the policy.
        /// </summary>
        [JsonProperty("message")] 
        public string Message { get; set; }
    }
}
