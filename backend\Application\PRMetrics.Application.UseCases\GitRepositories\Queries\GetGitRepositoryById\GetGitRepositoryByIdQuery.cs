﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using   RIB.PRMetrics.Application.Dto;
using   RIB.PRMetrics.Application.UseCases.Commons.Bases;
using   RIB.PRMetrics.Domain.Entities;

/// <summary>
/// Query to retrieve a specific Git repository by its ID.
/// Inherits from GitCommon to include common properties like project, repository details, etc.
/// </summary>
public class GetGitRepositoryByIdQuery : GitCommon, IRequest<BaseReponseGeneric<GitRepositoryDto>>
{
    // No additional properties are required here because it's inheriting from GitCommon,
    // which likely includes necessary details like the project or repository identifier.
}

