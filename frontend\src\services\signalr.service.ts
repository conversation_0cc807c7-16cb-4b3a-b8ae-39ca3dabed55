import { Injectable } from '@angular/core';
import { HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { ConfigService } from './config.service';

export interface ProcessProgress {
  uuid: string;
  percentage: number;
  status: string;
  fileName: string;
  jobStatus: string;
  fileUrl?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SignalRService {
  private hubConnection: HubConnection | null = null;
  private connectionState = new BehaviorSubject<string>('Disconnected');
  private progressUpdates = new Subject<ProcessProgress>();

  // Connection state observable
  public connectionState$ = this.connectionState.asObservable();
  
  // Progress updates observable
  public progressUpdates$ = this.progressUpdates.asObservable();

  constructor(private configService: ConfigService) {
    this.initializeConnection();
  }

  /**
   * Initialize SignalR connection
   */
  private initializeConnection(): void {
    const hubUrl = `${this.configService.API_BASE_URL}/progressHub`;
    
    this.hubConnection = new HubConnectionBuilder()
      .withUrl(hubUrl, {
        withCredentials: false
      })
      .withAutomaticReconnect()
      .configureLogging(LogLevel.Information)
      .build();

    this.setupEventHandlers();
  }

  /**
   * Setup SignalR event handlers
   */
  private setupEventHandlers(): void {
    if (!this.hubConnection) return;

    // Handle connection state changes
    this.hubConnection.onclose(() => {
      this.connectionState.next('Disconnected');
      console.log('SignalR connection closed');
    });

    this.hubConnection.onreconnecting(() => {
      this.connectionState.next('Reconnecting');
      console.log('SignalR reconnecting...');
    });

    this.hubConnection.onreconnected(() => {
      this.connectionState.next('Connected');
      console.log('SignalR reconnected');
    });

    // Handle progress updates from server
    this.hubConnection.on('ReceiveProgress', (progress: ProcessProgress) => {
      console.log('Received progress update:', progress);
      this.progressUpdates.next(progress);
    });
  }

  /**
   * Start SignalR connection
   */
  public async startConnection(): Promise<void> {
    if (!this.hubConnection) {
      this.initializeConnection();
    }

    if (this.hubConnection?.state === 'Disconnected') {
      try {
        await this.hubConnection.start();
        this.connectionState.next('Connected');
        console.log('SignalR connection started successfully');
      } catch (error) {
        this.connectionState.next('Failed');
        console.error('Error starting SignalR connection:', error);
        throw error;
      }
    }
  }

  /**
   * Stop SignalR connection
   */
  public async stopConnection(): Promise<void> {
    if (this.hubConnection && this.hubConnection.state === 'Connected') {
      try {
        await this.hubConnection.stop();
        this.connectionState.next('Disconnected');
        console.log('SignalR connection stopped');
      } catch (error) {
        console.error('Error stopping SignalR connection:', error);
        throw error;
      }
    }
  }

  /**
   * Get connection state
   */
  public getConnectionState(): string {
    return this.hubConnection?.state || 'Disconnected';
  }

  /**
   * Check if connected
   */
  public isConnected(): boolean {
    return this.hubConnection?.state === 'Connected';
  }

  /**
   * Get progress updates for a specific job UUID
   */
  public getProgressUpdatesForJob(uuid: string): Observable<ProcessProgress> {
    return new Observable(observer => {
      const subscription = this.progressUpdates$.subscribe(progress => {
        if (progress.uuid === uuid) {
          observer.next(progress);
        }
      });

      return () => subscription.unsubscribe();
    });
  }

  /**
   * Ensure connection is established before performing operations
   */
  public async ensureConnection(): Promise<void> {
    if (!this.isConnected()) {
      await this.startConnection();
    }
  }

  /**
   * Cleanup resources
   */
  public ngOnDestroy(): void {
    this.stopConnection();
    this.connectionState.complete();
    this.progressUpdates.complete();
  }
}
