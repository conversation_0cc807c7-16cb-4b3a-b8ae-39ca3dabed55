﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Microsoft.AspNetCore.Mvc;

namespace RIB.PRMetrics.Domain.Entities
{
    /// <summary>
    /// Represents search criteria for querying pull requests in a Git repository.
    /// Inherits from <see cref="GitCommon"/> which includes basic Git project and repository properties.
    /// </summary>
    public class PullRequestSearchCriteria : GitCommon
    {
       /// <summary>
        /// Gets or sets the maximum time for the pull request (filters PRs before this time).
        /// </summary>
        [FromQuery(Name = "maxTime")]
        public DateTime? MaxTime { get; set; }

        /// <summary>
        /// Gets or sets the minimum time for the pull request (filters PRs after this time).
        /// </summary>
        [FromQuery(Name = "minTime")]
        public DateTime? MinTime { get; set; }

        /// <summary>
        /// Gets or sets the status of the pull request (e.g., active, completed, or all).
        /// </summary>
        [FromQuery(Name = "status")]
        public string? Status { get; set; }

        /// <summary>
        /// Gets or sets the ID of the reviewer for filtering the pull requests.
        /// </summary>
        [FromQuery(Name = "reviewerId")]
        public string? ReviewerId { get; set; }

        /// <summary>
        /// Gets or sets the source reference name (e.g., branch name) to filter pull requests.
        /// </summary>
        [FromQuery(Name = "sourceRefName")]
        public string? SourceRefName { get; set; }

        /// <summary>
        /// Gets or sets the ID of the source repository for filtering the pull requests.
        /// </summary>
        [FromQuery(Name = "sourceRepositoryId")]
        public string? SourceRepositoryId { get; set; }

        /// <summary>
        /// Gets or sets the ID of the creator of the pull request to filter by.
        /// </summary>
        [FromQuery(Name = "creatorId")]
        public string? CreatorId { get; set; }

        /// <summary>
        /// Gets or sets the ID of the assignee for filtering the pull requests.
        /// </summary>
        [FromQuery(Name = "assigneeId")]
        public string? AssigneeId { get; set; }

        /// <summary>
        /// Gets or sets the target reference name (e.g., branch name) to filter pull requests.
        /// </summary>
        [FromQuery(Name = "targetRefName")]
        public string? TargetRefName { get; set; }

        /// <summary>
        /// Gets or sets the number of pull requests to fetch (used for pagination).
        /// </summary>
        [FromQuery(Name = "top")]
        public int? Top { get; set; }

        /// <summary>
        /// Gets or sets the number of pull requests to skip (used for pagination).
        /// </summary>
        [FromQuery(Name = "skip")]
        public int? Skip { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to include links in the search results.
        /// </summary>
        [FromQuery(Name = "includeLinks")]
        public bool? IncludeLinks { get; set; }
    }
}
