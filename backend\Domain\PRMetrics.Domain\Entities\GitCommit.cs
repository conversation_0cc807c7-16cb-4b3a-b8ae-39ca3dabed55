﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities
{
    /// <summary>
    /// Represents a Git commit with details such as commit ID and URL.
    /// This class can be used to track specific commits within a Git repository.
    /// </summary>
    public class GitCommit
    {
        /// <summary>
        /// Gets or sets the unique identifier for the commit.
        /// </summary>
        public string CommitId { get; set; }

        /// <summary>
        /// Gets or sets the URL pointing to the specific commit in the Git repository.
        /// </summary>
        public string Url { get; set; }
    }
}
