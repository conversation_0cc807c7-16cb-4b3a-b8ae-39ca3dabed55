﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using RIB.PRMetrics.Domain.Entities;

namespace  RIB.PRMetrics.Application.Interface.Persistence
{
    /// <summary>
    /// Defines the contract for repository operations related to Git projects.
    /// This interface extends the generic repository interface to provide project-specific functionality.
    /// </summary>
    public interface IGitProjectRepository : IGenericRepository<GitRepository>
    {
        /// <summary>
        /// Retrieves a list of Git projects.
        /// </summary>
        /// <param name="pATToken">The Personal Access Token (PAT) used to authenticate with Azure DevOps.</param>
        /// <returns>A list of Git projects.</returns>
        Task<List<GitProject>> GetGitProjectsAsync(string pATToken);

        /// <summary>
        /// Retrieves detailed information about a specific Git project by its ID.
        /// </summary>
        /// <param name="projectId">The unique identifier of the Git project.</param>
        /// <param name="pATToken">The Personal Access Token (PAT) used to authenticate with Azure DevOps.</param>
        /// <returns>A Git project object with detailed information.</returns>
        Task<GitProject> GetGitProjectDetailsAsync(string projectId, string pATToken);
    }
}
