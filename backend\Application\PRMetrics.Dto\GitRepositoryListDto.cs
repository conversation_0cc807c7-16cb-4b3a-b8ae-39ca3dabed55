﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace  RIB.PRMetrics.Application.Dto
{
    /// <summary>
    /// Represents a list of Git repositories retrieved from Azure DevOps.
    /// </summary>
    public class GitRepositoryListDto
    {
        /// <summary>
        /// The total number of repositories in the list.
        /// </summary>
        
        public int Count { get; set; }

        /// <summary>
        /// A collection of Git repository objects.
        /// </summary>
        
        public List<GitRepositoryDto> Repositories { get; set; }
    }
}
