﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities.IdentityPicker
{
    /// <summary>
    /// Represents an individual identity item returned from the Identity Picker API.
    /// </summary>
    public class IdentitiesItem
    {
        /// <summary>
        /// The local unique identifier for the identity.
        /// </summary>
        public string LocalId { get; set; }

        /// <summary>
        /// The display name of the identity (e.g., user's full name).
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// The SAM account name, typically the user's login name in Active Directory.
        /// </summary>
        public string SamAccountName { get; set; }

        /// <summary>
        /// Indicates whether the identity is currently active.
        /// </summary>
        public bool Active { get; set; }

        /// <summary>
        /// The email address associated with the identity.
        /// </summary>
        public string Mail { get; set; }
    }
}
