﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace  RIB.PRMetrics.Application.Dto
{
    /// <summary>
    /// Represents a Git project retrieved from Azure DevOps.
    /// </summary>
    public class GitProjectDto
    {
        /// <summary>
        /// The unique identifier of the Git project.
        /// </summary>
        
        public string Id { get; set; }

        /// <summary>
        /// The name of the Git project.
        /// </summary>
        
        public string Name { get; set; }

        /// <summary>
        /// The description of the Git project.
        /// </summary>
        
        public string Description { get; set; }

        /// <summary>
        /// The URL reference to the Git project.
        /// </summary>
        
        public string Url { get; set; }

        /// <summary>
        /// The current state of the Git project (e.g., wellFormed, deleting).
        /// </summary>
        
        public string State { get; set; }
    }
}
