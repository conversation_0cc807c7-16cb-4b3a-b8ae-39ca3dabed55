﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;

namespace  RIB.PRMetrics.Application.UseCases.GitRepositories.Queries.GetGitRepositoryById
{
    /// <summary>
    /// Validator for GetGitRepositoryByIdQuery.
    /// Ensures that necessary properties like Project and Repositories are not empty or null.
    /// </summary>
    public class GetGitRepositoryByIdValidator : AbstractValidator<GetGitRepositoryByIdQuery>
    {
        public GetGitRepositoryByIdValidator()
        {
            // Validate that the 'Project' property is neither empty nor null.
            RuleFor(x => x.Project)
                .NotEmpty()
                .NotNull()
                .WithMessage("Project is required.");

            // Validate that the 'Repositories' property is neither empty nor null.
            RuleFor(x => x.Repositories)
                .NotEmpty()
                .NotNull()
                .WithMessage("Repositories are required.");
        }
    }
}
