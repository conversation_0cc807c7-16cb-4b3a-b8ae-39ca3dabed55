import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ConfigService } from './config.service';

@Injectable({
  providedIn: 'root',
})
export class ApiUrlService {
  private apiBaseUrl: string;

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    // Use environment for API base URL as it might differ between environments
    this.apiBaseUrl = environment.apiUrl || this.configService.API_BASE_URL;
  }

  /**
   * Gets the base API URL from the environment configuration
   */
  getApiBaseUrl(): string {
    return this.apiBaseUrl;
  }

  /**
   * Builds a full API URL for the specified endpoint
   * @param endpoint The API endpoint path
   * @returns The complete API URL
   */
  buildUrl(endpoint: string): string {
    // Remove leading slash if present to avoid double slashes
    if (endpoint.startsWith('/')) {
      endpoint = endpoint.substring(1);
    }
    return `${this.apiBaseUrl}/${endpoint}`;
  }
  /**
   * Get the URL for the GitProjects API
   * @param patToken The PAT token for authentication
   * @returns The complete API URL
   */
  getGitProjectsUrl(patToken: string): string {
    return this.buildUrl(`GitProjects/GetGitProjects?pATToken=${patToken}`);
  }
  
  /**
   * Get the URL for the Current User API
   * @param patToken The PAT token for authentication
   * @returns The complete API URL
   */
  getCurrentUserUrl(patToken: string): string {
    return this.buildUrl(`User/GetCurrentUser?pATToken=${patToken}`);
  }

  /**
   * Get the URL for the GitRepositories API
   * @param project The project name or ID
   * @param patToken The PAT token for authentication
   * @returns The complete API URL
   */
  getGitRepositoriesUrl(project: string, patToken: string): string {
    return this.buildUrl(`GitRepositories/GetGitRepositories?Project=${project}&PATToken=${patToken}`);
  }

  /**
   * Get the URL for the GitPullRequests API
   * @param top Number of records to retrieve
   * @param skip Number of records to skip
   * @param project The project name or ID
   * @param repositories The repository name or ID
   * @param patToken The PAT token for authentication
   * @param filterParam Optional filter parameter
   * @param orderBy Optional order by parameter
   * @param status Optional status filter (all, active, completed, abandoned, etc)
   * @returns The complete API URL
   */  getPullRequestsUrl(
    top: number, 
    skip: number, 
    project: string, 
    repositories: string, 
    patToken: string,
    filterParam?: string,
    orderBy?: string,
    status: string = 'all',
    minTime?: string,
    maxTime?: string
  ): string {
    let url = this.buildUrl(`GitPullRequests/GetGitPullRequest?status=${status}&Top=${top}&Skip=${skip}&Project=${project}&Repositories=${repositories}&PATToken=${patToken}`);
    
    if (filterParam) {
      url += `&$filter=${encodeURIComponent(filterParam)}`;
    }
    
    if (orderBy) {
      url += `&$orderby=${encodeURIComponent(orderBy)}`;
    }
    
    if (minTime) {
      url += `&minTime=${encodeURIComponent(minTime)}`;
    }
    
    if (maxTime) {
      url += `&maxTime=${encodeURIComponent(maxTime)}`;
    }
    
    return url;
  }

  /**
   * Get the URL for the GitPullRequestThreads API
   * @param pullRequestId The pull request ID
   * @param project The project name or ID
   * @param repositories The repository name or ID
   * @param patToken The PAT token for authentication
   * @returns The complete API URL
   */
  getPullRequestThreadsUrl(pullRequestId: string | number, project: string, repositories: string, patToken: string): string {
    return this.buildUrl(`GitPullRequestThreads/GetGitPullRequestThreads?pullRequestId=${pullRequestId}&project=${project}&repositories=${repositories}&patToken=${patToken}`);
  }

  /**
   * Get the URL for the GitPullRequests by ID API
   * @param pullRequestId The pull request ID
   * @param project The project name or ID
   * @param repositories The repository name or ID
   * @param patToken The PAT token for authentication
   * @returns The complete API URL
   */
  getPullRequestByIdUrl(pullRequestId: string | number, project: string, repositories: string, patToken: string): string {
    return this.buildUrl(`GitPullRequests/GetGitPullRequestById?PullRequestId=${pullRequestId}&Project=${project}&Repositories=${repositories}&PATToken=${patToken}`);
  }
  /**
   * Get the URL for the ContributionsHierarchyQuery API
   * @param pullRequestId The pull request ID
   * @param repositoryId The repository ID
   * @param projectId The project ID
   * @param patToken The PAT token for authentication
   * @returns The complete API URL
   */  getContributionsHierarchyQueryUrl(pullRequestId: string | number, repositoryId: string, projectId: string, patToken: string): string {
    return this.buildUrl(`ContributionsHierarchyQuery/GetContributionsHierarchyQuery?pullRequestId=${pullRequestId}&repositoryId=${repositoryId}&projectId=${projectId}&patToken=${patToken}`);
  }

  /**
   * Get the URL for the GitPullRequestBuildTimeLine API
   * @param patToken The PAT token for authentication
   * @param projectId The project ID
   * @param buildId The build ID
   * @returns The complete API URL
   */
  getGitPullRequestBuildTimeLineUrl(patToken: string, projectId: string, buildId: number): string {
    return this.buildUrl(`GitPullRequestBuildTimeLine/GetGitPullRequestBuildTimeLine?patToken=${patToken}&projectId=${projectId}&buildId=${buildId}`);
  }

  /**
   * Get a batch of pull requests with optional continuation token and status filter
   * @param top Number of records to retrieve
   * @param continuationToken Continuation token for pagination
   * @param project The project name or ID
   * @param repo The repository name or ID
   * @param patToken The PAT token for authentication
   * @param status Optional status filter for pull requests
   * @returns An observable with PRs and the next continuation token
   */
  getPullRequestsBatch(
    top: number,
    continuationToken: string | null,
    project: string,
    repo: string,
    patToken: string,
    status: string = ''
  ): Observable<{ data: any[]; continuationToken?: string | null }> {
    // Use environment-based API URL for flexibility
    let url = this.buildUrl(`GitPullRequests/GetGitPullRequestsBatch?top=${top}&project=${encodeURIComponent(project)}&repositories=${encodeURIComponent(repo)}&patToken=${encodeURIComponent(patToken)}`);
    if (status) url += `&status=${encodeURIComponent(status)}`;
    if (continuationToken) url += `&continuationToken=${encodeURIComponent(continuationToken)}`;
    return this.http.get<any>(url).pipe(
      map((resp: any) => ({
        data: resp.data || [],
        continuationToken: resp.continuationToken || null
      }))
    );
  }

  /**
   * Get the URL for the PullRequest status count API
   * @param status The status to count (active, completed, abandoned)
   * @param project The project name or ID
   * @param repositories The repository name or ID
   * @param patToken The PAT token for authentication
   * @returns The complete API URL
   */
  getPullRequestStatusCountUrl(status: string, project: string, repositories: string, patToken: string): string {
    return this.buildUrl(`GitPullRequests/GetGitPullRequestStatusCount?prStatus=${status}&project=${project}&repositories=${repositories}&patToken=${patToken}`);
  }
}
