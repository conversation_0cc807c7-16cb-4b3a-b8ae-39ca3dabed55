﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestStatusCount
{
    /// <summary>
    /// Validator for <see cref="GetGitPullRequestStatusCountQuery"/>.
    /// Ensures required properties are provided and valid.
    /// </summary>
    public class GetGitPullRequestStatusCountValidator : AbstractValidator<GetGitPullRequestStatusCountQuery>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="GetGitPullRequestStatusCountValidator"/> class.
        /// </summary>
        public GetGitPullRequestStatusCountValidator()
        {
            // Validate that Project is not null or empty
            RuleFor(x => x.Project)
                .NotEmpty()
                .WithMessage("Project is required.");

            // Validate that Repositories is not null or empty
            RuleFor(x => x.Repositories)
                .NotEmpty()
                .WithMessage("Repositories are required.");

            // Validate that PATToken is not null or empty
            RuleFor(x => x.PATToken)
                .NotEmpty()
                .WithMessage("PAT Token is required.");

            // Optional: Validate Status if needed (based on your query requirements)
            RuleFor(x => x.Status)
                .NotEmpty()
                .WithMessage("Status is required.");
        }
    }
}
