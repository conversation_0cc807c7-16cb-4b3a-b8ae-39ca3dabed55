{"version": 3, "file": "Utils.js", "sourceRoot": "", "sources": ["../../src/Utils.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE;AAGvE,OAAO,EAAW,QAAQ,EAAE,MAAM,WAAW,CAAC;AAC9C,OAAO,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AAKvC,6DAA6D;AAC7D,yCAAyC;AAEzC,MAAM,CAAC,MAAM,OAAO,GAAW,iBAAiB,CAAC;AACjD,eAAe;AACf,MAAM,OAAO,GAAG;IACL,MAAM,CAAC,UAAU,CAAC,GAAQ,EAAE,IAAY;QAC3C,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;YACnC,MAAM,IAAI,KAAK,CAAC,QAAQ,IAAI,yBAAyB,CAAC,CAAC;SAC1D;IACL,CAAC;IACM,MAAM,CAAC,UAAU,CAAC,GAAW,EAAE,IAAY;QAC9C,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,QAAQ,IAAI,iCAAiC,CAAC,CAAC;SAClE;IACL,CAAC;IAEM,MAAM,CAAC,IAAI,CAAC,GAAQ,EAAE,MAAW,EAAE,IAAY;QAClD,yGAAyG;QACzG,IAAI,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,WAAW,IAAI,WAAW,GAAG,GAAG,CAAC,CAAC;SACrD;IACL,CAAC;CACJ;AAED,eAAe;AACf,MAAM,OAAO,QAAQ;IACjB,oEAAoE;IAC7D,MAAM,KAAK,SAAS;QACvB,OAAO,CAAC,QAAQ,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAAC;IACjG,CAAC;IAED,0EAA0E;IACnE,MAAM,KAAK,WAAW;QACzB,OAAO,CAAC,QAAQ,CAAC,MAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,eAAe,IAAI,IAAI,CAAC;IACnF,CAAC;IAED,4CAA4C;IAC5C,MAAM,KAAK,aAAa;QACpB,OAAO,CAAC,QAAQ,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,CAAC;IACpG,CAAC;IAED,wEAAwE;IACxE,oDAAoD;IAC7C,MAAM,KAAK,MAAM;QACpB,OAAO,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC;IAChG,CAAC;CACJ;AAED,eAAe;AACf,MAAM,UAAU,aAAa,CAAC,IAAS,EAAE,cAAuB;IAC5D,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;QACrB,MAAM,GAAG,yBAAyB,IAAI,CAAC,UAAU,EAAE,CAAC;QACpD,IAAI,cAAc,EAAE;YAChB,MAAM,IAAI,eAAe,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC;SACvD;KACJ;SAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QACjC,MAAM,GAAG,yBAAyB,IAAI,CAAC,MAAM,EAAE,CAAC;QAChD,IAAI,cAAc,EAAE;YAChB,MAAM,IAAI,eAAe,IAAI,GAAG,CAAC;SACpC;KACJ;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,eAAe;AACf,MAAM,UAAU,iBAAiB,CAAC,IAAiB;IAC/C,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;IAElC,6DAA6D;IAC7D,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;QACjB,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAChC,GAAG,IAAI,KAAK,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,0BAA0B;IAC1B,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACzC,CAAC;AAED,4CAA4C;AAC5C,eAAe;AACf,MAAM,UAAU,aAAa,CAAC,GAAQ;IAClC,OAAO,GAAG,IAAI,OAAO,WAAW,KAAK,WAAW;QAC5C,CAAC,GAAG,YAAY,WAAW;YACvB,kEAAkE;YAClE,CAAC,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,WAAW,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC,CAAC;AACzE,CAAC;AAED,eAAe;AACf,MAAM,CAAC,KAAK,UAAU,WAAW,CAAC,MAAe,EAAE,aAAqB,EAAE,UAAsB,EAAE,GAAW,EAC3E,OAA6B,EAAE,OAA+B;IAC5F,MAAM,OAAO,GAA0B,EAAE,CAAC;IAE1C,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,kBAAkB,EAAE,CAAC;IAC3C,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;IAEtB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,aAAa,6BAA6B,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,iBAAkB,CAAC,GAAG,CAAC,CAAC;IAEhI,MAAM,YAAY,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC;IACrE,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE;QACxC,OAAO;QACP,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,GAAG,OAAO,CAAC,OAAO,EAAC;QAC1C,YAAY;QACZ,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,eAAe,EAAE,OAAO,CAAC,eAAe;KAC3C,CAAC,CAAC;IAEH,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,aAAa,kDAAkD,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC;AAC1H,CAAC;AAED,eAAe;AACf,MAAM,UAAU,YAAY,CAAC,MAA2B;IACpD,IAAI,MAAM,KAAK,SAAS,EAAE;QACtB,OAAO,IAAI,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;KAClD;IAED,IAAI,MAAM,KAAK,IAAI,EAAE;QACjB,OAAO,UAAU,CAAC,QAAQ,CAAC;KAC9B;IAED,IAAK,MAAkB,CAAC,GAAG,KAAK,SAAS,EAAE;QACvC,OAAO,MAAiB,CAAC;KAC5B;IAED,OAAO,IAAI,aAAa,CAAC,MAAkB,CAAC,CAAC;AACjD,CAAC;AAED,eAAe;AACf,MAAM,OAAO,mBAAmB;IAI5B,YAAY,OAAmB,EAAE,QAA8B;QAC3D,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC9B,CAAC;IAEM,OAAO;QACV,MAAM,KAAK,GAAW,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACtE,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACZ,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC5C;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;YACtE,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;SACpD;IACL,CAAC;CACJ;AAED,eAAe;AACf,MAAM,OAAO,aAAa;IAWtB,YAAY,eAAyB;QACjC,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;QACjC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC;IACvB,CAAC;IAEM,GAAG,CAAC,QAAkB,EAAE,OAAe;QAC1C,IAAI,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YAC5B,MAAM,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE,CAAC;YAC9E,QAAQ,QAAQ,EAAE;gBACd,KAAK,QAAQ,CAAC,QAAQ,CAAC;gBACvB,KAAK,QAAQ,CAAC,KAAK;oBACf,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBACpB,MAAM;gBACV,KAAK,QAAQ,CAAC,OAAO;oBACjB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACnB,MAAM;gBACV,KAAK,QAAQ,CAAC,WAAW;oBACrB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACnB,MAAM;gBACV;oBACI,mGAAmG;oBACnG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBAClB,MAAM;aACb;SACJ;IACL,CAAC;CACJ;AAED,eAAe;AACf,MAAM,UAAU,kBAAkB;IAC9B,IAAI,mBAAmB,GAAG,sBAAsB,CAAC;IACjD,IAAI,QAAQ,CAAC,MAAM,EAAE;QACjB,mBAAmB,GAAG,YAAY,CAAC;KACtC;IACD,OAAO,CAAE,mBAAmB,EAAE,kBAAkB,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,EAAE,EAAE,iBAAiB,EAAE,CAAC,CAAE,CAAC;AAChH,CAAC;AAED,eAAe;AACf,MAAM,UAAU,kBAAkB,CAAC,OAAe,EAAE,EAAU,EAAE,OAAe,EAAE,cAAkC;IAC/G,qGAAqG;IACrG,IAAI,SAAS,GAAW,oBAAoB,CAAC;IAE7C,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACzC,SAAS,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;IACvD,SAAS,IAAI,KAAK,OAAO,IAAI,CAAC;IAE9B,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;QACjB,SAAS,IAAI,GAAG,EAAE,IAAI,CAAC;KAC1B;SAAM;QACH,SAAS,IAAI,cAAc,CAAC;KAC/B;IAED,SAAS,IAAI,GAAG,OAAO,EAAE,CAAC;IAE1B,IAAI,cAAc,EAAE;QAChB,SAAS,IAAI,KAAK,cAAc,EAAE,CAAC;KACtC;SAAM;QACH,SAAS,IAAI,2BAA2B,CAAC;KAC5C;IAED,SAAS,IAAI,GAAG,CAAC;IACjB,OAAO,SAAS,CAAC;AACrB,CAAC;AAED,0CAA0C;AAC1C,aAAa,CAAC,SAAS,SAAS;IAC5B,IAAI,QAAQ,CAAC,MAAM,EAAE;QACjB,QAAQ,OAAO,CAAC,QAAQ,EAAE;YACtB,KAAK,OAAO;gBACR,OAAO,YAAY,CAAC;YACxB,KAAK,QAAQ;gBACT,OAAO,OAAO,CAAC;YACnB,KAAK,OAAO;gBACR,OAAO,OAAO,CAAC;YACnB;gBACI,OAAO,OAAO,CAAC,QAAQ,CAAC;SAC/B;KACJ;SAAM;QACH,OAAO,EAAE,CAAC;KACb;AACL,CAAC;AAED,0CAA0C;AAC1C,aAAa,CAAC,SAAS,iBAAiB;IACpC,IAAI,QAAQ,CAAC,MAAM,EAAE;QACjB,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;KAChC;IACD,OAAO,SAAS,CAAC;AACrB,CAAC;AAED,SAAS,UAAU;IACf,IAAI,QAAQ,CAAC,MAAM,EAAE;QACjB,OAAO,QAAQ,CAAC;KACnB;SAAM;QACH,OAAO,SAAS,CAAC;KACpB;AACL,CAAC;AAED,eAAe;AACf,MAAM,UAAU,cAAc,CAAC,CAAM;IACjC,IAAI,CAAC,CAAC,KAAK,EAAE;QACT,OAAO,CAAC,CAAC,KAAK,CAAC;KAClB;SAAM,IAAI,CAAC,CAAC,OAAO,EAAE;QAClB,OAAO,CAAC,CAAC,OAAO,CAAC;KACpB;IACD,OAAO,GAAG,CAAC,EAAE,CAAC;AAClB,CAAC;AAED,eAAe;AACf,MAAM,UAAU,aAAa;IACzB,6DAA6D;IAC7D,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;QACnC,OAAO,UAAU,CAAC;KACrB;IACD,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;QAC7B,OAAO,IAAI,CAAC;KACf;IACD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QAC/B,OAAO,MAAM,CAAC;KACjB;IACD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QAC/B,OAAO,MAAM,CAAC;KACjB;IACD,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;AAC7C,CAAC", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { IStreamSubscriber, ISubscription } from \"./Stream\";\r\nimport { Subject } from \"./Subject\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n// Version token that will be replaced by the prepack command\r\n/** The version of the SignalR client. */\r\n\r\nexport const VERSION: string = \"0.0.0-DEV_BUILD\";\r\n/** @private */\r\nexport class Arg {\r\n    public static isRequired(val: any, name: string): void {\r\n        if (val === null || val === undefined) {\r\n            throw new Error(`The '${name}' argument is required.`);\r\n        }\r\n    }\r\n    public static isNotEmpty(val: string, name: string): void {\r\n        if (!val || val.match(/^\\s*$/)) {\r\n            throw new Error(`The '${name}' argument should not be empty.`);\r\n        }\r\n    }\r\n\r\n    public static isIn(val: any, values: any, name: string): void {\r\n        // TypeScript enums have keys for **both** the name and the value of each enum member on the type itself.\r\n        if (!(val in values)) {\r\n            throw new Error(`Unknown ${name} value: ${val}.`);\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport class Platform {\r\n    // react-native has a window but no document so we should check both\r\n    public static get isBrowser(): boolean {\r\n        return !Platform.isNode && typeof window === \"object\" && typeof window.document === \"object\";\r\n    }\r\n\r\n    // WebWorkers don't have a window object so the isBrowser check would fail\r\n    public static get isWebWorker(): boolean {\r\n        return !Platform.isNode && typeof self === \"object\" && \"importScripts\" in self;\r\n    }\r\n\r\n    // react-native has a window but no document\r\n    static get isReactNative(): boolean {\r\n        return !Platform.isNode && typeof window === \"object\" && typeof window.document === \"undefined\";\r\n    }\r\n\r\n    // Node apps shouldn't have a window object, but WebWorkers don't either\r\n    // so we need to check for both WebWorker and window\r\n    public static get isNode(): boolean {\r\n        return typeof process !== \"undefined\" && process.release && process.release.name === \"node\";\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getDataDetail(data: any, includeContent: boolean): string {\r\n    let detail = \"\";\r\n    if (isArrayBuffer(data)) {\r\n        detail = `Binary data of length ${data.byteLength}`;\r\n        if (includeContent) {\r\n            detail += `. Content: '${formatArrayBuffer(data)}'`;\r\n        }\r\n    } else if (typeof data === \"string\") {\r\n        detail = `String data of length ${data.length}`;\r\n        if (includeContent) {\r\n            detail += `. Content: '${data}'`;\r\n        }\r\n    }\r\n    return detail;\r\n}\r\n\r\n/** @private */\r\nexport function formatArrayBuffer(data: ArrayBuffer): string {\r\n    const view = new Uint8Array(data);\r\n\r\n    // Uint8Array.map only supports returning another Uint8Array?\r\n    let str = \"\";\r\n    view.forEach((num) => {\r\n        const pad = num < 16 ? \"0\" : \"\";\r\n        str += `0x${pad}${num.toString(16)} `;\r\n    });\r\n\r\n    // Trim of trailing space.\r\n    return str.substr(0, str.length - 1);\r\n}\r\n\r\n// Also in signalr-protocol-msgpack/Utils.ts\r\n/** @private */\r\nexport function isArrayBuffer(val: any): val is ArrayBuffer {\r\n    return val && typeof ArrayBuffer !== \"undefined\" &&\r\n        (val instanceof ArrayBuffer ||\r\n            // Sometimes we get an ArrayBuffer that doesn't satisfy instanceof\r\n            (val.constructor && val.constructor.name === \"ArrayBuffer\"));\r\n}\r\n\r\n/** @private */\r\nexport async function sendMessage(logger: ILogger, transportName: string, httpClient: HttpClient, url: string,\r\n                                  content: string | ArrayBuffer, options: IHttpConnectionOptions): Promise<void> {\r\n    const headers: {[k: string]: string} = {};\r\n\r\n    const [name, value] = getUserAgentHeader();\r\n    headers[name] = value;\r\n\r\n    logger.log(LogLevel.Trace, `(${transportName} transport) sending data. ${getDataDetail(content, options.logMessageContent!)}.`);\r\n\r\n    const responseType = isArrayBuffer(content) ? \"arraybuffer\" : \"text\";\r\n    const response = await httpClient.post(url, {\r\n        content,\r\n        headers: { ...headers, ...options.headers},\r\n        responseType,\r\n        timeout: options.timeout,\r\n        withCredentials: options.withCredentials,\r\n    });\r\n\r\n    logger.log(LogLevel.Trace, `(${transportName} transport) request complete. Response status: ${response.statusCode}.`);\r\n}\r\n\r\n/** @private */\r\nexport function createLogger(logger?: ILogger | LogLevel): ILogger {\r\n    if (logger === undefined) {\r\n        return new ConsoleLogger(LogLevel.Information);\r\n    }\r\n\r\n    if (logger === null) {\r\n        return NullLogger.instance;\r\n    }\r\n\r\n    if ((logger as ILogger).log !== undefined) {\r\n        return logger as ILogger;\r\n    }\r\n\r\n    return new ConsoleLogger(logger as LogLevel);\r\n}\r\n\r\n/** @private */\r\nexport class SubjectSubscription<T> implements ISubscription<T> {\r\n    private _subject: Subject<T>;\r\n    private _observer: IStreamSubscriber<T>;\r\n\r\n    constructor(subject: Subject<T>, observer: IStreamSubscriber<T>) {\r\n        this._subject = subject;\r\n        this._observer = observer;\r\n    }\r\n\r\n    public dispose(): void {\r\n        const index: number = this._subject.observers.indexOf(this._observer);\r\n        if (index > -1) {\r\n            this._subject.observers.splice(index, 1);\r\n        }\r\n\r\n        if (this._subject.observers.length === 0 && this._subject.cancelCallback) {\r\n            this._subject.cancelCallback().catch((_) => { });\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport class ConsoleLogger implements ILogger {\r\n    private readonly _minLevel: LogLevel;\r\n\r\n    // Public for testing purposes.\r\n    public out: {\r\n        error(message: any): void,\r\n        warn(message: any): void,\r\n        info(message: any): void,\r\n        log(message: any): void,\r\n    };\r\n\r\n    constructor(minimumLogLevel: LogLevel) {\r\n        this._minLevel = minimumLogLevel;\r\n        this.out = console;\r\n    }\r\n\r\n    public log(logLevel: LogLevel, message: string): void {\r\n        if (logLevel >= this._minLevel) {\r\n            const msg = `[${new Date().toISOString()}] ${LogLevel[logLevel]}: ${message}`;\r\n            switch (logLevel) {\r\n                case LogLevel.Critical:\r\n                case LogLevel.Error:\r\n                    this.out.error(msg);\r\n                    break;\r\n                case LogLevel.Warning:\r\n                    this.out.warn(msg);\r\n                    break;\r\n                case LogLevel.Information:\r\n                    this.out.info(msg);\r\n                    break;\r\n                default:\r\n                    // console.debug only goes to attached debuggers in Node, so we use console.log for Trace and Debug\r\n                    this.out.log(msg);\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getUserAgentHeader(): [string, string] {\r\n    let userAgentHeaderName = \"X-SignalR-User-Agent\";\r\n    if (Platform.isNode) {\r\n        userAgentHeaderName = \"User-Agent\";\r\n    }\r\n    return [ userAgentHeaderName, constructUserAgent(VERSION, getOsName(), getRuntime(), getRuntimeVersion()) ];\r\n}\r\n\r\n/** @private */\r\nexport function constructUserAgent(version: string, os: string, runtime: string, runtimeVersion: string | undefined): string {\r\n    // Microsoft SignalR/[Version] ([Detailed Version]; [Operating System]; [Runtime]; [Runtime Version])\r\n    let userAgent: string = \"Microsoft SignalR/\";\r\n\r\n    const majorAndMinor = version.split(\".\");\r\n    userAgent += `${majorAndMinor[0]}.${majorAndMinor[1]}`;\r\n    userAgent += ` (${version}; `;\r\n\r\n    if (os && os !== \"\") {\r\n        userAgent += `${os}; `;\r\n    } else {\r\n        userAgent += \"Unknown OS; \";\r\n    }\r\n\r\n    userAgent += `${runtime}`;\r\n\r\n    if (runtimeVersion) {\r\n        userAgent += `; ${runtimeVersion}`;\r\n    } else {\r\n        userAgent += \"; Unknown Runtime Version\";\r\n    }\r\n\r\n    userAgent += \")\";\r\n    return userAgent;\r\n}\r\n\r\n// eslint-disable-next-line spaced-comment\r\n/*#__PURE__*/ function getOsName(): string {\r\n    if (Platform.isNode) {\r\n        switch (process.platform) {\r\n            case \"win32\":\r\n                return \"Windows NT\";\r\n            case \"darwin\":\r\n                return \"macOS\";\r\n            case \"linux\":\r\n                return \"Linux\";\r\n            default:\r\n                return process.platform;\r\n        }\r\n    } else {\r\n        return \"\";\r\n    }\r\n}\r\n\r\n// eslint-disable-next-line spaced-comment\r\n/*#__PURE__*/ function getRuntimeVersion(): string | undefined {\r\n    if (Platform.isNode) {\r\n        return process.versions.node;\r\n    }\r\n    return undefined;\r\n}\r\n\r\nfunction getRuntime(): string {\r\n    if (Platform.isNode) {\r\n        return \"NodeJS\";\r\n    } else {\r\n        return \"Browser\";\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getErrorString(e: any): string {\r\n    if (e.stack) {\r\n        return e.stack;\r\n    } else if (e.message) {\r\n        return e.message;\r\n    }\r\n    return `${e}`;\r\n}\r\n\r\n/** @private */\r\nexport function getGlobalThis(): unknown {\r\n    // globalThis is semi-new and not available in Node until v12\r\n    if (typeof globalThis !== \"undefined\") {\r\n        return globalThis;\r\n    }\r\n    if (typeof self !== \"undefined\") {\r\n        return self;\r\n    }\r\n    if (typeof window !== \"undefined\") {\r\n        return window;\r\n    }\r\n    if (typeof global !== \"undefined\") {\r\n        return global;\r\n    }\r\n    throw new Error(\"could not find global\");\r\n}\r\n"]}