﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;

using   RIB.PRMetrics.Application.UseCases.GitRepositories.Queries.GetGitRepositories;

/// <summary>
/// Validator to ensure the GetGitRepositoriesQuery is correctly validated before processing.
/// This includes ensuring the project name is provided.
/// </summary>
internal class GetGitRepositoryValidator : AbstractValidator<GetGitRepositoriesQuery>
{
    public GetGitRepositoryValidator()
    {
        // Ensures that the Project property is not empty or null
        RuleFor(x => x.Project)
            .NotEmpty()  // Project name must not be empty
            .NotNull();  // Project name must not be null
    }
}
