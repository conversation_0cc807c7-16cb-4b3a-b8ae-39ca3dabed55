﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities
{
    /// <summary>
    /// Represents a list of Git pull requests along with their total count.
    /// </summary>
    public class GitPullRequestList
    {
        /// <summary>
        /// Gets or sets the total count of pull requests in the list.
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// Gets or sets the list of pull requests.
        /// </summary>
        public List<GitPullRequest> Value { get; set; }
    }
}
