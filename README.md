# Azure DevOps PR Metrics Portal

This project is a backend application developed using .NET Core that interacts with the Azure DevOps REST API. The application calculates and processes pull request (PR) metrics, which are then displayed in a custom PR metrics portal. The application follows Clean Architecture principles and uses the CQRS design pattern for better separation of concerns, scalability, and maintainability.

## Table of Contents

1. [Project Overview](#project-overview)
2. [Architecture](#architecture)
3. [Features](#features)
4. [Technologies Used](#technologies-used)
5. [Setup Instructions](#setup-instructions)
6. [API Endpoints](#api-endpoints)
7. [Running the Application](#running-the-application)
8. [Testing](#testing)
9. [Contributing](#contributing)
10. [License](#license)

## Project Overview

This application interacts with the Azure DevOps REST API to retrieve and process pull request (PR) data. The key objectives are:

* **Fetching data from Azure DevOps:** The system fetches PR-related data using Azure DevOps REST APIs.
* **Metrics Calculation:** Various metrics like reviewer response times, author response delays, comment activity, etc., are calculated.
* **Custom Metrics Portal:** Processed data is presented on a custom PR metrics portal to track PR performance and collaboration.

## Architecture

This backend application follows **Clean Architecture** to ensure proper separation of concerns. It uses **CQRS (Command Query Responsibility Segregation)** to handle read and write operations independently.

* **Core Layer (Domain):** Contains all business logic and entities such as `Comment`, `CommentThread`, `GitPullRequest`, etc.
* **Application Layer:** Manages the application's use cases (queries and commands). It defines the services and handles the requests to interact with the domain.
* **Infrastructure Layer:** Handles communication with external systems (e.g., Azure DevOps API, database). Implements repositories and HTTP clients.
* **API Layer (Web API):** Exposes endpoints for the frontend or other consumers to interact with the application.

## Features

* **Azure DevOps Integration:** Seamlessly integrates with Azure DevOps REST APIs to fetch PR data.
* **Metrics Calculation:** Calculates key metrics such as:

  * Reviewer response time
  * Author response time
  * Active and resolved comment counts
  * Pull request creation-to-response time
* **CQRS Design:** Segregates read and write operations for better performance and scalability.
* **Custom PR Metrics Portal:** Displays processed metrics on a custom portal (frontend not included in this backend project).

## Technologies Used

* **.NET Core 5/6** for building the backend application.
* **Clean Architecture** pattern for scalable and maintainable code.
* **CQRS (Command Query Responsibility Segregation)** design pattern.
* **Azure DevOps REST API** for fetching pull request data.
* **Entity Framework Core** for data access (if applicable).
* **MediatR** for implementing CQRS.
* **AutoMapper** for object-to-object mapping.

## Setup Instructions

### Prerequisites

Before running the application, ensure you have the following installed:

* .NET Core SDK (5.x or 6.x)
* A valid Azure DevOps account with access to a project
* PAT (Personal Access Token) for Azure DevOps API access


### 1. Clone the Repository

```bash
git clone https://github.com/your-repository.git
cd your-repository
```

### 2. Set up Configuration

In the `appsettings.json` file, configure the following:

* **Azure DevOps API base URL**
* **Personal Access Token (PAT)** for authentication
* **Database connection string** (if applicable)

### 3. Install Dependencies

Run the following command to restore the NuGet packages:

```bash
dotnet restore
```

### 4. Build the Application

```bash
dotnet build
```

### 5. Run Migrations (If using Entity Framework)

If your application uses a database, run the migrations to set up the database schema:

```bash
dotnet ef database update
```

### 6. Start the Application

Run the application with:

```bash
dotnet run
```

The backend application should now be running, and you can access the API endpoints.

## API Endpoints

The following API endpoints are available:

### 1. Get Contributions Hierarchy Query

**Endpoint:** `GET /api/ContributionsHierarchyQuery/GetContributionsHierarchyQuery`

Fetches the contributions hierarchy data for a specified pull request.

**Query Parameters:**

* `PullRequestId` (required): The ID of the pull request.
* `RepositoryId` (required): The ID of the repository.
* `ProjectId` (required): The Azure DevOps project ID.

### 2. Get PR Build Timeline

**Endpoint:** `GET /api/GitPullRequestBuildTimeLine/GetGitPullRequestBuildTimeLine`

Fetches the build timeline data for a specific pull request build.

**Query Parameters:**

* `ProjectId` (required): The ID of the project.
* `BuildId` (required): The ID of the build.

### 3. Get PR Comment Threads

**Endpoint:** `GET /api/GitPullRequestThreads/GetGitPullRequestThreads`

Fetches the comment thread data for a specific pull request.

**Query Parameters:**

* `PullRequestId` (required): The ID of the pull request.

## Running the Application

Once the application is running, you can access the API using tools like **Postman** or **Swagger** (if integrated). The API will process requests and return the calculated PR metrics based on the data retrieved from Azure DevOps.

## Testing

To test the application:

1. **Unit Tests:** The project should contain unit tests for different components. You can run tests using:

```bash
dotnet test
```

2. **Integration Tests:** Test the integration of your application with Azure DevOps REST API and other services.

## Contributing

## License



---

