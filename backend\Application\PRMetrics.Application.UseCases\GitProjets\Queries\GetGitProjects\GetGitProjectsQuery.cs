﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using Microsoft.AspNetCore.Mvc;
using   RIB.PRMetrics.Application.Dto;
using   RIB.PRMetrics.Application.UseCases.Commons.Bases;
using System.ComponentModel.DataAnnotations;

namespace  RIB.PRMetrics.Application.UseCases.GitProjets.Queries.GetGitProjets
{
    /// <summary>
    /// Query object used to request the list of all Git projects.
    /// Implements MediatR's IRequest interface with a generic response type.
    /// </summary>
    public class GetGitProjectsQuery : IRequest<BaseReponseGeneric<List<GitProjectDto>>>
    {
        /// <summary>
        /// Personal Access Token required to authenticate with Azure DevOps REST API.
        /// Validation ensures this field is not null or empty.
        /// </summary>
        [Required(ErrorMessage = "Token is required.")]
        [FromQuery(Name = "patToken")]
        public string PATToken { get; set; }
    }
}
