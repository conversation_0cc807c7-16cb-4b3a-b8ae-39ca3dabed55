﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using RIB.PRMetrics.Domain.Entities.CommentThreads;

namespace RIB.PRMetrics.Domain.Entities
{
    /// <summary>
    /// Represents the response model that includes metadata about 
    /// Git pull request threads and the associated comment thread data.
    /// </summary>
    public class GitPullRequestThreadsResponse
    {
        /// <summary>
        /// Gets or sets the mode used to analyze or group the comment threads,
        /// such as by author, reviewer, or type.
        /// </summary>
        public GitPullRequestThreadsMode draftMode { get; set; }

        /// <summary>
        /// Gets or sets the detailed comment thread information for a Git pull request.
        /// </summary>
        public GitPullRequestCommentThread commentThread { get; set; }
    }
}
