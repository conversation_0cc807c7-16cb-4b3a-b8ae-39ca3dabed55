﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using Microsoft.AspNetCore.Mvc;
using   RIB.PRMetrics.Application.Dto;
using   RIB.PRMetrics.Application.UseCases.Commons.Bases;
using System.ComponentModel.DataAnnotations;

namespace  RIB.PRMetrics.Application.UseCases.GitRepositories.Queries.GetGitRepositories
{
    /// <summary>
    /// Represents a query to fetch a list of Git repositories.
    /// This query expects a project name and a personal access token (PATToken) for authentication.
    /// </summary>
    public class GetGitRepositoriesQuery : IRequest<BaseReponseGeneric<List<GitRepositoryDto>>>
    {
        /// <summary>
        /// The project name for which repositories are to be fetched.
        /// This field is required to identify the project.
        /// </summary>
        
        [Required(ErrorMessage = "Project is required.")]
        [FromQuery(Name = "project")]
        public string? Project { get; set; }

        /// <summary>
        /// The personal access token (PAT) used for authentication to access the repositories.
        /// This token is required to authenticate the request.
        /// </summary>
        
        [Required(ErrorMessage = "Token is required.")]
        [FromQuery(Name = "patToken")]
        public string? PATToken { get; set; }
    }
}
