﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Newtonsoft.Json;

namespace RIB.PRMetrics.Domain.Entities.Contributions.HierarchyQuery
{
    /// <summary>
    /// Represents the properties of a data provider context, which typically includes
    /// metadata related to a pull request, repository, and types associated with it.
    /// </summary>
    public class DataProviderContextProperties
    {
        /// <summary>
        /// Gets or sets the unique identifier for the pull request.
        /// </summary>
        [<PERSON><PERSON><PERSON>roper<PERSON>("pullRequestId")] // Ensures that the property is serialized/deserialized with the "pullRequestId" JSON key
        public string PullRequestId { get; set; }

        /// <summary>
        /// Gets or sets the unique identifier for the repository.
        /// </summary>
        [JsonProperty("repositoryId")] // Ensures that the property is serialized/deserialized with the "repositoryId" JSON key
        public string RepositoryId { get; set; }

        /// <summary>
        /// Gets or sets the type associated with the data provider context.
        /// This is represented as an integer.
        /// </summary>
        [JsonProperty("types")] // Ensures that the property is serialized/deserialized with the "types" JSON key
        public int Types { get; set; }
    }
}
