﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using System.Net.Http.Headers;
using System.Text;

namespace  RIB.PRMetrics.Persistence.Common
{
    /// <summary>
    /// A client class used to interact with the Azure DevOps API.
    /// This class is used to send GET requests to Azure DevOps services by adding the necessary
    /// authentication headers and processing the response.
    /// </summary>
    public class AZDevopsClientCommon
    {
        private readonly HttpClient httpClient;

        /// <summary>
        /// Initializes a new instance of the <see cref="AZDevopsClientCommon"/> class.
        /// </summary>
        /// <param name="httpClientFactory">The HTTP client factory used to create instances of <see cref="HttpClient"/>.</param>
        /// <exception cref="ArgumentNullException">Thrown when <paramref name="httpClientFactory"/> is null.</exception>
        public AZDevopsClientCommon(IHttpClientFactory httpClientFactory)
        {
            httpClient = httpClientFactory.CreateClient() ?? throw new ArgumentNullException(nameof(httpClient));
        }

        /// <summary>
        /// Sends an HTTP GET request to the specified URL and adds the necessary 
        /// Azure DevOps Personal Access Token (PAT) for authentication.
        /// </summary>
        /// <param name="url">The URL to send the GET request to.</param>
        /// <param name="PAT">The Personal Access Token (PAT) used for Azure DevOps authentication.</param>
        /// <returns>The HTTP response message from the request.</returns>
        /// <exception cref="ArgumentNullException">Thrown when <paramref name="url"/> or <paramref name="PAT"/> is null or empty.</exception>
        public async Task<HttpResponseMessage> GetAsync(string url, string PAT)
        {
            if (string.IsNullOrEmpty(url)) throw new ArgumentNullException(nameof(url));
            if (string.IsNullOrEmpty(PAT)) throw new ArgumentNullException(nameof(PAT));

            // Adding Basic Authentication header with Personal Access Token (PAT)
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes($":{PAT}")));

            // Sending the GET request and returning the response
            var response = await httpClient.GetAsync(url);
            return response;
        }

        public async Task<HttpResponseMessage> PostAsync(string url, string PAT,string jsonBody)
        {
            if (string.IsNullOrEmpty(url)) throw new ArgumentNullException(nameof(url));
            if (string.IsNullOrEmpty(PAT)) throw new ArgumentNullException(nameof(PAT));

            // Adding Basic Authentication header with Personal Access Token (PAT)
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes($":{PAT}")));

            StringContent content = new StringContent(jsonBody, Encoding.UTF8, "application/json");


            // Sending the GET request and returning the response
            var response = await httpClient.PostAsync(url, content);
            return response;
        }
    }
}
