﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace RIB.PRMetrics.Domain.Entities
{
    public class GitPullRequestThread
    {
        
        public int Id { get; set; }
        
        public List<GitPullRequestComment> Comments { get; set; }

        
        public DateTime PublishedDate { get; set; }
        
        public DateTime LastUpdatedDate { get; set; }

        public string Status { get; set; }
    }
}
