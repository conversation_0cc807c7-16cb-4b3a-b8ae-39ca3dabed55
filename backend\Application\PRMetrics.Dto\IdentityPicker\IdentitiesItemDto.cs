﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto.IdentityPicker
{
    /// <summary>
    /// Data Transfer Object (DTO) representing an identity item.
    /// Used to transfer identity information across application layers.
    /// </summary>
    public class IdentitiesItemDto
    {
        /// <summary>
        /// Gets or sets the local identifier of the identity.
        /// </summary>
        public string LocalId { get; set; }

        /// <summary>
        /// Gets or sets the display name of the identity.
        /// </summary>
        public string DisplayName { get; set; }

        

        /// <summary>
        /// Gets or sets a value indicating whether the identity is active.
        /// </summary>
        public bool Active { get; set; }

        /// <summary>
        /// Gets or sets the email address associated with the identity.
        /// </summary>
        public string Mail { get; set; }
    }
}
