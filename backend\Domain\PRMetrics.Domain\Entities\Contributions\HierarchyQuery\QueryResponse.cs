﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Newtonsoft.Json;

namespace RIB.PRMetrics.Domain.Entities.Contributions.HierarchyQuery
{
    /// <summary>
    /// Represents the response to a contributions hierarchy query.
    /// </summary>
    public class QueryResponse
    {
        /// <summary>
        /// Gets or sets the data providers associated with the query response.
        /// </summary>
        [JsonProperty("dataProviders")]
        public DataProviders DataProviders { get; set; }
    }
}
