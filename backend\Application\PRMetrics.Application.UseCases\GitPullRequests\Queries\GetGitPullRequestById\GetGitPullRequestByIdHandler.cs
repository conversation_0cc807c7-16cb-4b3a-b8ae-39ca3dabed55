﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using AutoMapper;
using   RIB.PRMetrics.Application.Dto;
using   RIB.PRMetrics.Application.Interface.Persistence;
using   RIB.PRMetrics.Application.UseCases.Commons.Bases;

namespace  RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestById
{
    /// <summary>
    /// Handler for processing the GetGitPullRequestByIdQuery.
    /// Fetches pull request details from Azure DevOps and maps them to a DTO.
    /// </summary>
    public class GetGitPullRequestByIdHandler : IRequestHandler<GetGitPullRequestByIdQuery, BaseReponseGeneric<GitPullRequestDto>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public GetGitPullRequestByIdHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        /// <summary>
        /// Handles the incoming request to retrieve a pull request by ID.
        /// </summary>
        /// <param name="request">The query containing necessary pull request info.</param>
        /// <param name="cancellationToken">Token for request cancellation.</param>
        /// <returns>A generic base response with mapped pull request details.</returns>
        public async Task<BaseReponseGeneric<GitPullRequestDto>> Handle(GetGitPullRequestByIdQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<GitPullRequestDto>();

            try
            {
                // Fetch pull request from the repository using provided query data
                var gitPullRequest = await _unitOfWork.GitPullRequestsRepository.GetGitPullRequestDetailsAsync(request, request.PullRequestId);

                if (gitPullRequest is not null)
                {
                    response.Data = _mapper.Map<GitPullRequestDto>(gitPullRequest);
                    response.Succcess = true;
                    response.Message = "Git Pull Request details fetched successfully.";
                }
                else
                {
                    response.Succcess = false;
                    response.Message = "Pull request not found.";
                }
            }
            catch (Exception ex)
            {
                response.Succcess = false;
                response.Message = $"Error occurred: {ex.Message}";
            }

            return response;
        }
    }
}


