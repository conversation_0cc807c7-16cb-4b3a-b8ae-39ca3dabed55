﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Newtonsoft.Json;

namespace RIB.PRMetrics.Domain.Entities.Contributions.HierarchyQuery
{
    /// <summary>
    /// Represents the type of a policy with its associated metadata.
    /// </summary>
    public class PolicyType
    {
        /// <summary>
        /// Gets or sets the unique identifier of the policy type.
        /// </summary>
        [JsonProperty("id")]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the URL associated with the policy type.
        /// </summary>
        [JsonProperty("url")]
        public string Url { get; set; }

        /// <summary>
        /// Gets or sets the display name of the policy type.
        /// </summary>
        [JsonProperty("displayName")]
        public string DisplayName { get; set; }
    }
}
