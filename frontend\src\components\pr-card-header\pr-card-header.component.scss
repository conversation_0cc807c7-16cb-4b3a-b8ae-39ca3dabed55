/* Card header content styling - extracted from pr-data.component.scss for reusability */
.card-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  /* Add styling for the card title and subtitle */
  ::ng-deep {
    .mat-mdc-card-title {
      font-size: 1.5rem;
      font-weight: 500;
      margin-bottom: 4px;
      color: var(--text-dark, #333);
      letter-spacing: 0.5px;
    }
    
    .mat-mdc-card-subtitle {
      font-size: 0.95rem;
      color: var(--text-light, #666);
      margin-bottom: 0;
    }
  }
}

/* PR counts styling */
.pr-counts-block {
  display: flex;
  gap: 16px;
  margin-left: 16px; /* Add some spacing between title and counts */
  
  @media (max-width: 640px) {
    flex-direction: column;
    gap: 8px;
    margin-left: 0; /* Remove margin on mobile */
  }
  .pr-count-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    transition: transform 0.2s ease, box-shadow 0.2s ease, border 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    cursor: pointer; /* Add pointer cursor to indicate clickable */
    border: 2px solid transparent; /* Add transparent border for selected state */
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.12);
    }
      &.selected {
      border-color: currentColor; /* Use the current text color for the border */
      border-width: 2px;
      border-style: solid;
      font-weight: 600; /* Make text slightly bolder */
      transform: translateY(-1px); /* Slight lift effect */
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); /* Enhanced shadow */
    }
      &.active {
      background-color: rgba(#0078d4, 0.1);
      mat-icon {
        color: #0078d4;
      }
      
      &.selected {
        border-color: #0078d4; /* Active blue border when selected */
        background-color: rgba(#0078d4, 0.2); /* Darker background when selected */
      }
    }
    
    &.completed {
      background-color: rgba(#28a745, 0.1);
      mat-icon {
        color: #28a745;
      }
      
      &.selected {
        border-color: #28a745; /* Completed green border when selected */
        background-color: rgba(#28a745, 0.2); /* Darker background when selected */
      }
    }
    
    &.abandoned {
      background-color: rgba(#f44336, 0.1);
      mat-icon {
        color: #f44336;
      }
      
      &.selected {
        border-color: #f44336; /* Abandoned red border when selected */
        background-color: rgba(#f44336, 0.2); /* Darker background when selected */
      }
    }
  }
    .loading-text {
    font-style: italic;
    opacity: 0.8;
    color: var(--text-light, #666);
    animation: pulse 1.5s infinite ease-in-out;
  }
  
  @keyframes pulse {
    0% { opacity: 0.5; }
    50% { opacity: 0.8; }
    100% { opacity: 0.5; }
  }
}
