﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto.Contributions.HierarchyQuery
{
    /// <summary>
    /// Represents the policy settings related to a contribution or pull request.
    /// This DTO contains various flags and configuration settings used for pull request approval and merging rules.
    /// </summary>
    public class PolicyDto
    {
        /// <summary>
        /// Gets or sets a value indicating whether squash merge is allowed.
        /// A squash merge combines all the changes of a pull request into one commit.
        /// </summary>
        public bool UseSquashMerge { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether no-fast-forward merges are allowed.
        /// A no-fast-forward merge creates a merge commit, even if the merge can be done as a fast-forward.
        /// </summary>
        public bool AllowNoFastForward { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether squash merges are allowed.
        /// A squash merge combines all the changes into a single commit.
        /// </summary>
        public bool AllowSquash { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether rebase merges are allowed.
        /// A rebase allows the changes to be applied on top of the target branch, rewriting history.
        /// </summary>
        public bool allowRebase { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether rebase merges are allowed.
        /// This type of merge re-applies the changes on top of the target branch, without a merge commit.
        /// </summary>
        public bool AllowRebaseMerge { get; set; }

        /// <summary>
        /// Gets or sets the evaluation ID for the policy.
        /// This could be used to track or uniquely identify a specific policy evaluation.
        /// </summary>
        public string EvaluationId { get; set; }

        /// <summary>
        /// Gets or sets the configuration ID for the policy.
        /// This ID is used to link this policy to a specific configuration setting.
        /// </summary>
        public int ConfigurationId { get; set; }

        /// <summary>
        /// Gets or sets the type of the policy.
        /// This field links the policy to its specific category or set of rules.
        /// </summary>
        public PolicyTypeDto PolicyType { get; set; }

        /// <summary>
        /// Gets or sets the status of the policy.
        /// This could indicate whether the policy is active, pending, or disabled.
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this policy is blocking further actions.
        /// If true, this policy might prevent merging or other actions until it's resolved.
        /// </summary>
        public bool IsBlocking { get; set; }

        /// <summary>
        /// Gets or sets the display name for the policy.
        /// This is typically used to show a human-readable name in a UI.
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// Gets or sets the display text for the policy.
        /// This text is shown alongside the display name and may provide additional context or explanation.
        /// </summary>
        public string DisplayText { get; set; }

        /// <summary>
        /// Gets or sets the display status for the policy.
        /// This could represent the visual status of the policy (e.g., approved, pending).
        /// </summary>
        public int DisplayStatus { get; set; }

        /// <summary>
        /// Gets or sets the minimum number of approvers required for this policy.
        /// This is used to enforce a minimum threshold for approval before proceeding.
        /// </summary>
        public int? MinimumApproverCount { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the creator's vote counts towards approval.
        /// This setting can be used to allow or disallow the creator from voting on their own pull request.
        /// </summary>
        public bool? CreatorVoteCounts { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether a vote is needed on the last iteration of the pull request.
        /// This may enforce a final approval vote before merging.
        /// </summary>
        public bool? NeedsVoteOnLastIteration { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the minimum number of approvers has been satisfied.
        /// This flag indicates whether the policy conditions for approvals have been met.
        /// </summary>
        public bool? IsMinimumApproverCountSatisfied { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether there are any iterations that have not been approved.
        /// This field can be used to track whether there are pending approvals for any iteration of the pull request.
        /// </summary>
        public bool? HasNotApprovedIterations { get; set; }

        /// <summary>
        /// Gets or sets the build ID associated with the policy.
        /// This is used to link the policy to a specific build or build process.
        /// </summary>
        public int? BuildId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether a valid build exists for the policy.
        /// This field indicates whether the build required by the policy is present and valid.
        /// </summary>
        public bool? ValidBuildExists { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the build has expired.
        /// This indicates whether the build associated with the policy is no longer valid.
        /// </summary>
        public bool? BuildIsExpired { get; set; }

        /// <summary>
        /// Gets or sets the expiration date for the policy.
        /// This could be used to set a time limit after which the policy is no longer valid.
        /// </summary>
        public object ExpirationDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the server will queue a build automatically.
        /// This setting controls whether a build will be triggered automatically based on the policy conditions.
        /// </summary>
        public bool? ServerWillQueueBuild { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether only manual queuing of builds is allowed.
        /// If true, builds must be queued manually, preventing automated triggers.
        /// </summary>
        public bool? ManualQueueOnly { get; set; }

        /// <summary>
        /// Gets or sets the build output preview.
        /// This might be a link or reference to the build output, used for review or inspection before approval.
        /// </summary>
        public object BuildOutputPreview { get; set; }

        /// <summary>
        /// Gets or sets the list of required reviewer IDs for this policy.
        /// This field contains the IDs of users who must approve the pull request before it can be merged.
        /// </summary>
        public List<string> RequiredReviewerIds { get; set; }

        /// <summary>
        /// Gets or sets the message associated with the policy.
        /// This message can provide additional information or instructions related to the policy.
        /// </summary>
        public string Message { get; set; }
    }
}

