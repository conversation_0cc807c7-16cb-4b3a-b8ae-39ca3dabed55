{"version": "6.0", "nxVersion": "21.0.0", "pathMappings": {}, "nxJsonPlugins": [], "fileMap": {"projectFileMap": {"frontend": [{"file": ".env", "hash": "17921810282844259268"}, {"file": ".giti<PERSON>re", "hash": "7007548610665544877"}, {"file": ".prettieri<PERSON>re", "hash": "12269743867629339573"}, {"file": ".prettier<PERSON>", "hash": "6820402234827505055"}, {"file": "README.md", "hash": "17801443504826488012"}, {"file": "eslint.config.mjs", "hash": "14070652578410166583", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "nx.json", "hash": "6740007138770367993"}, {"file": "package-lock.json", "hash": "1982658978866146732"}, {"file": "package.json", "hash": "9451819785834864444", "deps": ["npm:@angular-devkit/build-angular", "npm:@angular-devkit/core", "npm:@angular-devkit/schematics", "npm:@angular/cli", "npm:@angular/compiler-cli", "npm:@angular/language-service", "npm:@eslint/js", "npm:@nx/angular", "npm:@nx/eslint", "npm:@nx/eslint-plugin", "npm:@nx/js", "npm:@nx/web", "npm:@nx/workspace", "npm:@schematics/angular", "npm:@swc-node/register", "npm:@swc/core", "npm:@swc/helpers", "npm:@types/file-saver", "npm:@typescript-eslint/utils", "npm:angular-eslint", "npm:eslint", "npm:eslint-config-prettier", "npm:nx", "npm:prettier", "npm:tslib", "npm:typescript", "npm:typescript-eslint", "npm:@angular/animations", "npm:@angular/cdk", "npm:@angular/common", "npm:@angular/compiler", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/platform-browser", "npm:@angular/platform-browser-dynamic", "npm:@angular/router", "npm:@microsoft/signalr", "npm:@swimlane/ngx-charts", "npm:ansi-to-html", "npm:chart.js", "npm:file-saver", "npm:ng2-charts", "npm:rxjs", "npm:xlsx", "npm:zone.js"]}, {"file": "project.json", "hash": "7582022878483244999"}, {"file": "public/assets/rib-logo-text.svg", "hash": "14859851032181749274"}, {"file": "public/assets/rib-logo.ico", "hash": "3504895424892951950"}, {"file": "public/favicon.ico", "hash": "9303420814833116677"}, {"file": "src/app/app.component.html", "hash": "18078201111529177951"}, {"file": "src/app/app.component.scss", "hash": "5280100684454124900"}, {"file": "src/app/app.component.ts", "hash": "13500734449808823779", "deps": ["npm:@angular/core", "npm:@angular/router", "npm:@angular/common"]}, {"file": "src/app/app.config.ts", "hash": "81543414546470880", "deps": ["npm:@angular/core", "npm:@angular/router", "npm:@angular/common", "npm:@angular/platform-browser", "npm:ng2-charts"]}, {"file": "src/app/app.routes.ts", "hash": "17116386596621841254", "deps": ["npm:@angular/router"]}, {"file": "src/app/chart-config.ts", "hash": "13873851724719363444", "deps": ["npm:chart.js"]}, {"file": "src/components/dashboard/README.md", "hash": "5626339484701697386"}, {"file": "src/components/dashboard/dashboard.component.html", "hash": "17895542850805812874"}, {"file": "src/components/dashboard/dashboard.component.scss", "hash": "17203630292718218363"}, {"file": "src/components/dashboard/dashboard.component.ts", "hash": "17571797158127740123", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/material", "npm:ng2-charts", "npm:chart.js"]}, {"file": "src/components/dialogs/approver-dialog.component.ts", "hash": "11586657476473850394", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/material"]}, {"file": "src/components/dialogs/build-logs-dialog.component.ts", "hash": "10941998351047015085", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/material", "npm:rxjs", "npm:ansi-to-html"]}, {"file": "src/components/download-ui/download-ui.component.html", "hash": "17614740410250335964"}, {"file": "src/components/download-ui/download-ui.component.scss", "hash": "16045284288998803099"}, {"file": "src/components/download-ui/download-ui.component.ts", "hash": "3479687300386708636", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/material", "npm:rxjs"]}, {"file": "src/components/header/header.component.html", "hash": "2186507055555185085"}, {"file": "src/components/header/header.component.scss", "hash": "1510123906261923156"}, {"file": "src/components/header/header.component.spec.ts", "hash": "246408304213996698", "deps": ["npm:@angular/core"]}, {"file": "src/components/header/header.component.ts", "hash": "14464948175932720079", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/material", "npm:@angular/router", "npm:rxjs", "npm:@angular/animations"]}, {"file": "src/components/pat-login/pat-login.component.html", "hash": "10228953156417334279"}, {"file": "src/components/pat-login/pat-login.component.scss", "hash": "10277257023839573187"}, {"file": "src/components/pat-login/pat-login.component.ts", "hash": "5288162744153870885", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/router", "npm:@angular/forms", "npm:@angular/material"]}, {"file": "src/components/pr-card-header/pr-card-header.component.html", "hash": "8418225902436760544"}, {"file": "src/components/pr-card-header/pr-card-header.component.scss", "hash": "3614233697601373164"}, {"file": "src/components/pr-card-header/pr-card-header.component.ts", "hash": "11911026112911049176", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/material"]}, {"file": "src/components/pr-data/README.md", "hash": "10733259832269580175"}, {"file": "src/components/pr-data/pr-data-common.ts", "hash": "13132031114906496789", "deps": ["npm:@angular/core"]}, {"file": "src/components/pr-data/pr-data.component.html", "hash": "7320078944377059883"}, {"file": "src/components/pr-data/pr-data.component.scss", "hash": "1280570194986581414"}, {"file": "src/components/pr-data/pr-data.component.ts", "hash": "2170637998983807725", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/material", "npm:@angular/router", "npm:rxjs", "npm:@angular/cdk", "npm:@angular/forms"]}, {"file": "src/components/pr-details/guid-utils.ts", "hash": "18074990464972957876"}, {"file": "src/components/pr-details/pr-details.component.html", "hash": "*******************"}, {"file": "src/components/pr-details/pr-details.component.scss", "hash": "14538210031898703011"}, {"file": "src/components/pr-details/pr-details.component.spec.ts", "hash": "1366257028604023059", "deps": ["npm:@angular/core"]}, {"file": "src/components/pr-details/pr-details.component.ts", "hash": "11407064762808015310", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/router", "npm:@angular/material"]}, {"file": "src/components/pr-details/user-guid-to-name.pipe.ts", "hash": "11524551754425324268", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:rxjs"]}, {"file": "src/components/pr-filter/pr-filter.component.html", "hash": "6073080417577073816"}, {"file": "src/components/pr-filter/pr-filter.component.scss", "hash": "10009209460467119891"}, {"file": "src/components/pr-filter/pr-filter.component.ts", "hash": "14463486032240436400", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/forms", "npm:@angular/material", "npm:rxjs"]}, {"file": "src/components/threads/threads.component.html", "hash": "15478361919135203897"}, {"file": "src/components/threads/threads.component.scss", "hash": "11286398988590216444"}, {"file": "src/components/threads/threads.component.ts", "hash": "711482249689903753", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:@angular/material"]}, {"file": "src/environments/environment.prod.ts", "hash": "4434624932978841240"}, {"file": "src/environments/environment.staging.ts", "hash": "6087699791748081584"}, {"file": "src/environments/environment.ts", "hash": "16907379755844480643"}, {"file": "src/index.html", "hash": "529711144385761853"}, {"file": "src/main.ts", "hash": "5341088302223217096", "deps": ["npm:@angular/platform-browser"]}, {"file": "src/models/pr-interfaces.model.ts", "hash": "3802118093638378458"}, {"file": "src/models/review-cycle.model.ts", "hash": "8448098417810332432"}, {"file": "src/services/api-url.service.ts", "hash": "10643931178922755976", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "src/services/auth-guard.service.ts", "hash": "17838034549424925521", "deps": ["npm:@angular/core", "npm:@angular/router", "npm:rxjs"]}, {"file": "src/services/config.service.ts", "hash": "9913445298646456732", "deps": ["npm:@angular/core"]}, {"file": "src/services/download-progress.service.ts", "hash": "18353330242960400824", "deps": ["npm:@angular/core", "npm:rxjs"]}, {"file": "src/services/excel-export.service.ts", "hash": "6371166103846766761", "deps": ["npm:@angular/core", "npm:rxjs"]}, {"file": "src/services/export-api.service.ts", "hash": "13382310876126412514", "deps": ["npm:@angular/core", "npm:@angular/common", "npm:rxjs"]}, {"file": "src/services/pr-data.service.ts", "hash": "6181014632982395922", "deps": ["npm:@angular/core", "npm:rxjs", "npm:@angular/common"]}, {"file": "src/services/review-cycle.service.ts", "hash": "2173495718436235666", "deps": ["npm:@angular/core"]}, {"file": "src/services/signalr.service.ts", "hash": "17894227571226215849", "deps": ["npm:@angular/core", "npm:@microsoft/signalr", "npm:rxjs"]}, {"file": "src/services/time.service.ts", "hash": "1365562672145247794", "deps": ["npm:@angular/core"]}, {"file": "src/styles.scss", "hash": "5131403177039991842"}, {"file": "src/styles/components.scss", "hash": "1437840013024678459"}, {"file": "src/styles/pr-components.scss", "hash": "18333397852591003001"}, {"file": "src/styles/pr-variables.scss", "hash": "10267226911271737338"}, {"file": "src/utils/azure-devops-utils.ts", "hash": "15290092360021791704", "deps": ["npm:@angular/common", "npm:rxjs"]}, {"file": "src/utils/date-memoize-utils.ts", "hash": "17470673734410915020"}, {"file": "src/utils/date-utils.ts", "hash": "7696519591177883280"}, {"file": "tsconfig.app.json", "hash": "16864811146245124190"}, {"file": "tsconfig.editor.json", "hash": "17575078801745175164"}, {"file": "tsconfig.json", "hash": "14699918424639748579"}]}, "nonProjectFiles": []}}