﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;
using MediatR;
using   RIB.PRMetrics.Application.UseCases.Commons.Bases;
using   RIB.PRMetrics.Application.UseCases.Commons.Exceptions;

namespace  RIB.PRMetrics.Application.UseCases.Commons.Behaviours
{
    /// <summary>
    /// A behavior that validates the request using FluentValidation before passing it to the next handler in the MediatR pipeline.
    /// It implements the <see cref="IPipelineBehavior{TRequest,TResponse}"/> interface.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request being validated.</typeparam>
    /// <typeparam name="TResponse">The type of the response returned after handling the request.</typeparam>
    public class ValidationBehaviour<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse> where TRequest : IRequest<TResponse>
    {
        private readonly IEnumerable<IValidator<TRequest>> _validators; // Collection of validators for the request.

        /// <summary>
        /// Initializes a new instance of the <see cref="ValidationBehaviour{TRequest,TResponse}"/> class.
        /// </summary>
        /// <param name="validators">A collection of validators used to validate the request.</param>
        public ValidationBehaviour(IEnumerable<IValidator<TRequest>> validators)
        {
            _validators = validators ?? throw new ArgumentNullException(nameof(validators)); // Ensure validators are provided.
        }

        /// <summary>
        /// Validates the request using the registered validators and throws a custom exception if validation fails.
        /// </summary>
        /// <param name="request">The request that is being processed.</param>
        /// <param name="next">The delegate to the next handler in the pipeline.</param>
        /// <param name="cancellationToken">A token used to propagate notification of request cancellation.</param>
        /// <returns>The response generated by the request handler.</returns>
        public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
        {
            // If any validators are registered, perform validation.
            if (_validators.Any())
            {
                var context = new ValidationContext<TRequest>(request); // Create a validation context for the request.

                // Run the validation asynchronously for each registered validator.
                var validationResults = await Task.WhenAll(_validators.Select(v => v.ValidateAsync(context, cancellationToken)));

                // Collect all validation failures (if any).
                var failures = validationResults
                    .Where(r => r.Errors.Any()) // Filter out validators with no errors.
                    .SelectMany(r => r.Errors) // Flatten all the errors.
                    .Select(r => new BaseError() { PropertyMessage = r.PropertyName, ErrorMessage = r.ErrorMessage }) // Convert errors to BaseError objects.
                    .ToList();

                // If there are validation failures, throw a custom validation exception with the errors.
                if (failures.Any())
                {
                    throw new ValidationExceptionCustom(failures);
                }
            }

            return await next(); // Proceed to the next handler if validation passes.
        }
    }
}
