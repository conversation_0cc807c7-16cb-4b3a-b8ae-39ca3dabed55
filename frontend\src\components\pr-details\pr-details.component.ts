import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatTabsModule } from '@angular/material/tabs';
import { MatBadgeModule } from '@angular/material/badge';
import { PrDataService } from '../../services/pr-data.service';
import { ConfigService } from '../../services/config.service';
import {
  CommentThreadResponse,
  PullRequestDetails,
  CommentThread,
} from '../../models/pr-interfaces.model';
import { testReplaceGuids } from './guid-utils';
import { UserGuidToNamePipe } from './user-guid-to-name.pipe';
import { DownloadUiComponent } from '../download-ui/download-ui.component';
import {
  calculateDraftPublishedTime,
  calculateCommentThreadDurations,
  enhanceCommentsWithDurations,
  formatDuration as formatDurationUtil,
} from '../../utils/date-memoize-utils';
import { ReviewCycleService } from '../../services/review-cycle.service';
import { ReviewCycleMetrics } from '../../models/review-cycle.model';
import { ExcelExportService } from '../../services/excel-export.service';
import { DownloadProgressService } from '../../services/download-progress.service';

@Component({
  selector: 'app-pr-details',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatChipsModule,
    MatTooltipModule,
    MatTabsModule,
    MatBadgeModule,
    UserGuidToNamePipe,
    DownloadUiComponent,
  ],
  templateUrl: './pr-details.component.html',
  styleUrl: './pr-details.component.scss',
})
export class PrDetailsComponent implements OnInit {
  pullRequestId: any = '';
  pullRequest: PullRequestDetails | null = null;
  commentThreads: CommentThread[] = [];
  activeCommentCount: number = 0;
  resolvedCommentCount: number = 0;
  reviewsCount: any[] = [];
  isLoading = true;
  error: string | null = null;
  threadResponse: CommentThreadResponse | null = null;

  // Draft/Published time metrics
  draftModeMetrics: {
    totalDraftHours: number;
    totalPublishedHours: number;
    totalDraftFormatted: string;
    totalPublishedFormatted: string;
  } | null = null;

  // Review cycle metrics
  reviewCycleMetrics: ReviewCycleMetrics | null = null;

  // Export functionality
  isExporting = false;
  exportDownloadId: string | null = null;

  // Credentials for API calls
  public patToken = '';
  private selectedProject = '';
  private selectedRepo = '';
  
  // Expose the testReplaceGuids function for template use
  public testReplaceGuids = testReplaceGuids;
  
  // Expose the formatDurationUtility from date-memoize-utils for internal use
  private formatDurationUtility = formatDurationUtil;
  
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private http: HttpClient,
    private prDataService: PrDataService,
    private configService: ConfigService,
    private reviewCycleService: ReviewCycleService,
    private excelExportService: ExcelExportService,
    private downloadProgressService: DownloadProgressService
  ) {}

  ngOnInit(): void {
    // Get the PR ID from the route parameters
    this.route.paramMap.subscribe((params) => {
      this.pullRequestId = params.get('id') || '';

      if (!this.pullRequestId) {
        this.error = 'No Pull Request ID provided';
        this.isLoading = false;
        return;
      }

      // Get credentials from service
      this.prDataService.credentials$.subscribe((creds) => {
        if (creds) {
          this.patToken = creds.patToken;
          this.selectedProject = creds.project;
          this.selectedRepo = creds.repository;

          // Load the PR details and comments
          this.loadPullRequestDetails();
          this.loadCommentThreads();
        } else {
          this.error = 'Authentication credentials not found';
          this.isLoading = false;
          // Redirect to login if no credentials
          this.router.navigate(['/']);
        }
      });
    });
  }
  
  loadPullRequestDetails(): void {
    const url = `${this.configService.getPullRequestByIdEndpoint()}?PullRequestId=${this.pullRequestId}&Project=${this.selectedProject}&Repositories=${this.selectedRepo}&PATToken=${this.patToken}`;

    this.http.get<any>(url).subscribe({
      next: (response) => {
        if (response) {
          this.pullRequest = response.data;
        } else {
          this.error = 'Pull Request details not found';
        }
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Error fetching PR details:', err);
        this.error = 'Failed to load Pull Request details';
        this.isLoading = false;
      },
    });
  }
  
  loadCommentThreads(): void {
    const url = `${this.configService.getCommentThreadsEndpoint()}?pullRequestId=${this.pullRequestId}&project=${this.selectedProject}&repositories=${this.selectedRepo}&patToken=${this.patToken}`;

    this.http.get<CommentThreadResponse>(url).subscribe({
      next: (response) => {
        if (response && response.data && response.data.commentThread) {
          this.threadResponse = response;
          const commentThread = response.data.commentThread;

          // Store comment threads
          let threads = commentThread.commentThreads || [];
          
          // If we have a valid pull request, enhance threads with calculated metrics
          if (this.pullRequest) {
            // Calculate draft/published time metrics
            if (response.data.draftMode && this.pullRequest.creationDate) {
              const stateEvents = response.data.draftMode.stateEvents || [];
              const currentState = this.pullRequest.isDraft === 'true' ? 'draft' : 'published';
              
              this.draftModeMetrics = calculateDraftPublishedTime(
                stateEvents, 
                this.pullRequest.creationDate, 
                currentState
              );
            }
            
            // Enhance each comment thread with duration metrics
            threads = threads.map((thread: any) => {
              // For each thread, calculate thread durations
              if (thread.publishedDate && this.pullRequest?.creationDate) {
                const threadMetrics = calculateCommentThreadDurations(thread, this.pullRequest.creationDate);
                
                // Enhance thread with calculated metrics
                thread.activeDuration = threadMetrics.activeDuration;
                thread.activeDurationFormatted = threadMetrics.activeDurationFormatted;
                if (threadMetrics.fixedDuration !== undefined) {
                  thread.fixedDuration = threadMetrics.fixedDuration;
                  thread.fixedDurationFormatted = threadMetrics.fixedDurationFormatted;
                }
                // Convert number to string to match interface definition
                thread.hoursSincePRCreation = threadMetrics.hoursSincePRCreation.toString();
                thread.hoursSincePRCreationFormatted = threadMetrics.hoursSincePRCreationFormatted;
              }
              
              // Enhance comments with response times
              if (thread.comments && thread.comments.length > 0) {
                thread.comments = enhanceCommentsWithDurations(thread.comments);
              }
              return thread;
            });
          }
          
          this.commentThreads = threads;

          // Store active and resolved comment counts
          this.activeCommentCount = commentThread.activeCommentCount || 0;
          this.resolvedCommentCount = commentThread.resolvedCommentCount || 0;

          // Store reviewer stats
          this.reviewsCount = commentThread.reviewsCount || [];

          // Calculate review cycle metrics
          this.calculateReviewCycles(threads);
        } else {
          console.warn(
            'No comment threads found for PR #' + this.pullRequestId
          );
        }
      },
      error: (err) => {
        console.error('Error fetching comment threads:', err);
      },
    });
  }
  
  calculateReviewCycles(threads: CommentThread[]): void {
    // Calculate review cycles using the ReviewCycleService
    if (threads && threads.length > 0) {
      this.reviewCycleMetrics = this.reviewCycleService.calculateReviewCycles(threads);
    } else {
      // Default to zero cycles if no threads exist
      this.reviewCycleMetrics = {
        cycleCount: 0,
        description: 'Zero review cycles (no comments found)'
      };
    }
  }

  formatBranchName(branchName: string): string {
    return branchName.replace('refs/heads/', '');
  }
  
  getStatusClass(status: string): string {
    status = status.toLowerCase();
    // Use the status values from PR_STATUS_OPTIONS where applicable
    const statusMap: { [key: string]: string } = {
      'active': 'status-active',
      'completed': 'status-completed',
      'abandoned': 'status-abandoned',
      'draft': 'status-draft',
      'fixed': 'status-fixed',
      'closed': 'status-closed',
      'wontfix': 'status-wontfix',
      'pending': 'status-pending'
    };
    return statusMap[status] || '';
  }

  goBack(): void {
    this.router.navigate(['/pull-requests']);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  // Format legacy duration strings (this handles the old format, our imported formatDuration handles the new)
  formatLegacyDuration(durationString: string): string {
    if (!durationString) return '';

    // Check if it's already formatted
    if (durationString.includes('hour') || durationString.includes('minute')) {
      return durationString;
    }

    // Parse timespan format (e.g., "07:57:43.4960000")
    const parts = durationString.split(':');
    if (parts.length < 3) return durationString;

    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);

    let result = '';
    if (hours > 0) {
      result += `${hours} ${hours === 1 ? 'hour' : 'hours'}`;
    }

    if (minutes > 0) {
      if (result) result += ' ';
      result += `${minutes} ${minutes === 1 ? 'minute' : 'minutes'}`;
    }

    return result || 'Less than a minute';
  }
  
  // Wrapper for the imported formatDuration
  getFormattedDuration(ms: number): string {
    return formatDurationUtil(ms);
  }

  // Get total comments count
  getTotalCommentsCount(): number {
    return (this.activeCommentCount || 0) + (this.resolvedCommentCount || 0);
  }

  // Get comment threads grouped by status
  getThreadsByStatus(status: string): CommentThread[] {
    return this.commentThreads.filter(
      (thread) =>
        thread.status && thread.status.toLowerCase() === status.toLowerCase()
    );
  }

  // Scroll to the comments tab
  scrollToComments(): void {
    // Find the tab group element
    setTimeout(() => {
      // Set focus to the comments tab programmatically (index 0 is the comments tab)
      const tabGroup = document.querySelector('mat-tab-group');
      if (tabGroup) {
        // Use Angular Material's API to select the first tab (Comments tab)
        const tabHeader = tabGroup.querySelector('.mat-mdc-tab');
        if (tabHeader) {
          (tabHeader as HTMLElement).click();
        }

        // Scroll to the tab content
        tabGroup.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  }

  // Export PR details to Excel
  async exportToExcel(): Promise<void> {
    if (!this.pullRequest || this.isExporting) {
      return;
    }

    try {
      this.isExporting = true;

      // Start download progress tracking
      const fileName = `PR_${this.pullRequest.pullRequestId}_Details_${new Date().toISOString().split('T')[0]}.xlsx`;
      const { id, progress$ } = this.downloadProgressService.startDownload(fileName);
      this.exportDownloadId = id;

      // Simulate progress while generating Excel
      const progressPromise = this.downloadProgressService.simulateProgress(id, 100);

      // Generate and download Excel file
      await new Promise(resolve => setTimeout(resolve, 500)); // Small delay to show progress

      this.excelExportService.exportPRDetailsToExcel(
        this.pullRequest,
        this.commentThreads,
        this.reviewCycleMetrics,
        this.draftModeMetrics
      );

      // Wait for progress to complete
      await progressPromise;

    } catch (error) {
      console.error('Error exporting to Excel:', error);
      if (this.exportDownloadId) {
        this.downloadProgressService.setDownloadError(
          this.exportDownloadId,
          'Failed to export PR details to Excel'
        );
      }
    } finally {
      // Reset export state after a delay
      setTimeout(() => {
        this.isExporting = false;
        this.exportDownloadId = null;
      }, 3000);
    }
  }


}
