<div class="download-ui-container" *ngIf="currentProgress">
  <!-- Card wrapper (optional) -->
  <mat-card *ngIf="showCard" class="download-card">
    <mat-card-content>
      <div class="download-content">
        <!-- Download header -->
        <div class="download-header">
          <div class="download-info">
            <mat-icon [color]="getStatusColor()" class="status-icon">{{ getStatusIcon() }}</mat-icon>
            <div class="download-details">
              <div class="file-name">{{ currentProgress.fileName }}</div>
              <div class="status-text">{{ getStatusText() }}</div>
            </div>
          </div>
          
          <!-- Action buttons -->
          <div class="download-actions">
            <button 
              mat-icon-button 
              *ngIf="canPause()" 
              (click)="pauseDownload()"
              matTooltip="Pause download"
              color="primary">
              <mat-icon>pause</mat-icon>
            </button>
            
            <button 
              mat-icon-button 
              *ngIf="canResume()" 
              (click)="resumeDownload()"
              matTooltip="Resume download"
              color="primary">
              <mat-icon>play_arrow</mat-icon>
            </button>
            
            <button 
              mat-icon-button 
              *ngIf="canCancel()" 
              (click)="cancelDownload()"
              matTooltip="Cancel download"
              color="warn">
              <mat-icon>close</mat-icon>
            </button>
          </div>
        </div>

        <!-- Progress bar -->
        <div class="progress-container" *ngIf="isActive()">
          <mat-progress-bar 
            [mode]="getProgressMode()" 
            [value]="currentProgress.progress"
            [color]="getProgressColor()"
            class="download-progress">
          </mat-progress-bar>
          
          <!-- Progress details -->
          <div class="progress-details">
            <span class="progress-percentage">{{ currentProgress.progress.toFixed(1) }}%</span>
            <span class="progress-info" *ngIf="currentProgress.estimatedTimeRemaining">
              {{ currentProgress.estimatedTimeRemaining }} remaining
            </span>
            <span class="download-speed" *ngIf="currentProgress.downloadSpeed">
              {{ currentProgress.downloadSpeed }}
            </span>
          </div>
        </div>

        <!-- Completion message -->
        <div class="completion-message" *ngIf="isCompleted()">
          <mat-icon color="primary">check_circle</mat-icon>
          <span>Download completed successfully!</span>
        </div>

        <!-- Error message -->
        <div class="error-message" *ngIf="hasError()">
          <mat-icon color="warn">error</mat-icon>
          <span>{{ currentProgress.error || 'Download failed' }}</span>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Inline version (without card) -->
  <div *ngIf="!showCard" class="download-inline">
    <div class="download-header">
      <div class="download-info">
        <mat-icon [color]="getStatusColor()" class="status-icon">{{ getStatusIcon() }}</mat-icon>
        <div class="download-details">
          <div class="file-name">{{ currentProgress.fileName }}</div>
          <div class="status-text">{{ getStatusText() }}</div>
        </div>
      </div>
      
      <!-- Action buttons -->
      <div class="download-actions">
        <button 
          mat-icon-button 
          *ngIf="canPause()" 
          (click)="pauseDownload()"
          matTooltip="Pause download"
          color="primary">
          <mat-icon>pause</mat-icon>
        </button>
        
        <button 
          mat-icon-button 
          *ngIf="canResume()" 
          (click)="resumeDownload()"
          matTooltip="Resume download"
          color="primary">
          <mat-icon>play_arrow</mat-icon>
        </button>
        
        <button 
          mat-icon-button 
          *ngIf="canCancel()" 
          (click)="cancelDownload()"
          matTooltip="Cancel download"
          color="warn">
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </div>

    <!-- Progress bar -->
    <div class="progress-container" *ngIf="isActive()">
      <mat-progress-bar 
        [mode]="getProgressMode()" 
        [value]="currentProgress.progress"
        [color]="getProgressColor()"
        class="download-progress">
      </mat-progress-bar>
      
      <!-- Progress details -->
      <div class="progress-details">
        <span class="progress-percentage">{{ currentProgress.progress.toFixed(1) }}%</span>
        <span class="progress-info" *ngIf="currentProgress.estimatedTimeRemaining">
          {{ currentProgress.estimatedTimeRemaining }} remaining
        </span>
        <span class="download-speed" *ngIf="currentProgress.downloadSpeed">
          {{ currentProgress.downloadSpeed }}
        </span>
      </div>
    </div>

    <!-- Completion message -->
    <div class="completion-message" *ngIf="isCompleted()">
      <mat-icon color="primary">check_circle</mat-icon>
      <span>Download completed successfully!</span>
    </div>

    <!-- Error message -->
    <div class="error-message" *ngIf="hasError()">
      <mat-icon color="warn">error</mat-icon>
      <span>{{ currentProgress.error || 'Download failed' }}</span>
    </div>
  </div>
</div>
