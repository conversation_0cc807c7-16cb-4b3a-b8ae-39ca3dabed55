﻿using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Domain.Entities;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

namespace RIB.PRMetrics.Infrastructure.Services
{
    public class InMemoryDeleteJobQueue : IDeleteJobQueue
    {
        // Key = UUID, Value = (Job, ScheduledTime)
        private readonly ConcurrentDictionary<Guid, (ProcessProgress Job, DateTime ScheduledTime)> _jobs =
            new ConcurrentDictionary<Guid, (ProcessProgress, DateTime)>();

        public void Schedule(ProcessProgress job, TimeSpan delay)
        {
            var scheduledTime = DateTime.UtcNow.Add(delay);
            _jobs[job.Uuid] = (job, scheduledTime); // Adds or updates the job
        }

        public bool TryDequeue(out ProcessProgress job)
        {
            var now = DateTime.UtcNow;

            foreach (var entry in _jobs)
            {
                var uuid = entry.Key;
                var (queuedJob, scheduledTime) = entry.Value;

                if (now >= scheduledTime)
                {
                    // Try remove to ensure only one thread dequeues this job
                    if (_jobs.TryRemove(uuid, out var removed))
                    {
                        job = removed.Job;
                        return true;
                    }
                }
            }

            job = null;
            return false;
        }
    }
}
