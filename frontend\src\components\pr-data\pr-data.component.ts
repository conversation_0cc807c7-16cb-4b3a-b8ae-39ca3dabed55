import {
  Component,
  OnInit,
  ViewChild,
  AfterViewInit,
  OnDestroy,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatSort, MatSortModule, Sort } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { PrDataService } from '../../services/pr-data.service';
import { ConfigService } from '../../services/config.service';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';
import {
  ApiResponse,
  CommentThreadResponse,
  ContributionsHierarchyQueryResponse,
} from '../../models/pr-interfaces.model';
import { ApiUrlService } from '../../services/api-url.service';
import { ApproverDialogComponent } from '../dialogs/approver-dialog.component';
import { BuildLogsDialogComponent } from '../dialogs/build-logs-dialog.component';
import { TimeService } from '../../services/time.service';
import {
  CdkVirtualScrollViewport,
  ScrollingModule,
} from '@angular/cdk/scrolling';
import { FormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { PrDataCommonService } from './pr-data-common';
import { PrFilterComponent } from '../pr-filter/pr-filter.component';
import { PrCardHeaderComponent } from '../pr-card-header/pr-card-header.component';

@Component({
  selector: 'app-pr-data',
  standalone: true,
  imports: [
    CommonModule,
    MatTableModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatMenuModule,
    MatDialogModule,
    ScrollingModule,
    FormsModule,
    MatSelectModule,
    PrFilterComponent, // Filter component
    PrCardHeaderComponent, // Card header component
  ],
  templateUrl: './pr-data.component.html',
  styleUrl: './pr-data.component.scss',
})
export class PrDataComponent implements OnInit, AfterViewInit, OnDestroy {
  displayedColumns: string[] = [
    'serialNumber', // Serial number column
    'pullRequestId',
    'title',
    'creationDate',
    'timelapse',
    'activeComments',
    'status',
    'createdBy',
    'reviewers',
    'sourceRefName',
    'pipelineStatus',
  ];
  dataSource = new MatTableDataSource<any>();
  lastUpdated = new Date();
  isLoading = true;
  Math = Math; // Expose Math to the template

  // Filter related properties
  currentFilter = '';
  currentSort: Sort = { active: '', direction: '' };
  private searchTerms = new Subject<string>();
  private destroy$ = new Subject<void>();
  searchMode: 'client' | 'server' = 'client'; // Default to client-side filtering for better UX
  private patToken = '';
  private selectedProject = '';
  private selectedProjectId = '';
  private selectedRepoId = '';
  selectedRepo = ''; // Made public for template access

  // Add this property to store continuation tokens
  private continuationTokens: Map<number, string> = new Map();

  // Private properties to track pagination state
  private _lastPageRequested = -1;
  private _lastPageSize = 0;
  private _lastApiCallTimestamp = 0;
  // Infinite scroll variables
  prList: any[] = [];
  batchSize: number;
  hasMore = true;
  isLoadingMore = false;
  error: string | null = null;
  private prSet = new Set<number>(); // For deduplication

  // Status filter
  statusFilter: string = 'all';
  statusOptions: Array<{ value: string; label: string }>;

  // Track last status filter to prevent redundant API calls
  private _lastStatusFilter: string = 'all';

  // Date range filter properties
  private startDate: Date | null = null;
  private endDate: Date | null = null;

  totalCount = 0;
  completedCount = 0;
  abandonedCount = 0;

  // Individual loading states for PR counts
  loadingActiveCount = false;
  loadingCompletedCount = false;
  loadingAbandonedCount = false;

  sortSubscription: any;

  @ViewChild(MatSort) sort!: MatSort;
  @ViewChild(CdkVirtualScrollViewport) virtualScroll!: CdkVirtualScrollViewport;
  constructor(
    private router: Router,
    private http: HttpClient,
    private prDataService: PrDataService,
    private apiUrlService: ApiUrlService,
    private dialog: MatDialog,
    public prDataCommon: PrDataCommonService, // Inject shared service
    private configService: ConfigService,
    private timeService: TimeService // Inject time service
  ) {
    this.totalCount = 0;
    this.completedCount = 0;
    this.abandonedCount = 0;
    this.loadingActiveCount = false;
    this.loadingCompletedCount = false;
    this.loadingAbandonedCount = false;
    this.sortSubscription = null;

    // Initialize values from config service
    this.batchSize = this.configService.BATCH_SIZE;
    this.statusOptions = this.configService.PR_STATUS_OPTIONS;
  }
  ngOnInit(): void { 
    this.searchTerms
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(this.configService.SEARCH_DEBOUNCE_TIME), // Use configurable debounce time
        distinctUntilChanged() // Only emit if value is different from previous
      )
      .subscribe((term: string) => {
        console.log('Debounced search term:', term); // Log to confirm debounce is working
        this.isLoading = true; // Show loading indicator
        this.currentFilter = term;

        // For empty search, reset everything and reload data
        if (!term) {
          this.currentFilter = '';
          this.dataSource.filter = '';
          this.searchMode = 'server';
          this.loadPrData();
          return;
        }

        // Clear continuation tokens when filter changes
        this.continuationTokens.clear();

        // Try to find PR by ID in current data (client-side)
        const isNumeric = /^\d+$/.test(term);
        let found = false;
        if (isNumeric) {
          // Search by PR ID
          found = this.dataSource.data.some(
            (pr) => pr.pullRequestId?.toString() === term
          );
          if (found) {
            // Use client-side filter for exact PR ID match
            this.searchMode = 'client';
            this.applyClientSideFilter(term);
            return;
          } else {
            // If not found, call the API to get PR by ID directly
            this.isLoading = true;
            const url = this.apiUrlService.getPullRequestByIdUrl(
              term,
              this.selectedProject,
              this.selectedRepo,
              this.patToken
            );
            this.http.get<any>(url).subscribe({
              next: (response) => {
                if (response && response.data) {
                  this.dataSource.data = [response.data];
                  this.searchMode = 'client';
                } else {
                  this.dataSource.data = [];
                }
                this.isLoading = false;
              },
              error: () => {
                this.dataSource.data = [];
                this.isLoading = false;
              },
            });
            return;
          }
        } else {
          // Search by other fields (title, author, etc.)
          found = this.dataSource.data.some((pr) => {
            return (
              (pr.title && pr.title.toLowerCase().includes(term)) ||
              (pr.createdBy?.displayName &&
                pr.createdBy.displayName.toLowerCase().includes(term)) ||
              (pr.status && pr.status.toLowerCase().includes(term))
            );
          });
          if (found) {
            this.searchMode = 'client';
            this.applyClientSideFilter(term);
            return;
          }
        }

        // Determine search mode based on filter length and data size
        if (term.length <= 2 || this.dataSource.data.length > 200) {
          // Short queries or large datasets should use server-side filtering
          console.log('Using server-side filtering for search');
          this.searchMode = 'server';
          this.loadPrData();
        } else {
          // For longer queries on smaller datasets, client-side is more responsive
          console.log('Using client-side filtering for search');
          this.searchMode = 'client';
          this.applyClientSideFilter(term);
        }
      });// Get credentials from service
    this.prDataService.credentials$.subscribe((creds) => {
      if (creds) {
        this.patToken = creds.patToken;
        this.selectedProject = creds.project;
        this.selectedRepo = creds.repository;
        this.selectedProjectId = creds.projectId || '';
        this.selectedRepoId = creds.repositoryId || '';

        // Use the new API endpoint to get PR counts by status
        // this.fetchPrStatusCounts();

        // Initial data load with server-side mode
        this.searchMode = 'server';
        this._lastApiCallTimestamp = 0; // Reset timestamp to force load
        this.loadPrData();
      } else {
        ('No credentials found, checking localStorage...');
        // Try to reload credentials from localStorage
        this.prDataService.loadCredentialsFromStorage();
      }
    }); // Use cached PR counts when loading initial data (don't force refresh)
    this.fetchPrStatusCounts(false);
    // Initial load
    this.loadInitialPRs();
  }
  ngAfterViewInit() {
    // Connect the sort to the data source
    this.dataSource.sort = this.sort;

    // Set up custom filter predicate using the shared service
    this.prDataCommon.setupFilterPredicate(
      this.dataSource,
      this.formatBranchName.bind(this)
    );

    // Set up sort change listener
    if (this.sortSubscription) {
      this.sortSubscription.unsubscribe();
    }

    this.sortSubscription = this.sort.sortChange.subscribe((sort: Sort) => {
      console.log('Sort changed:', sort);
      this.currentSort = sort;

      // Only client-side sorting for timelapse column
      if (sort.active === 'timelapse') {
        console.log('Sorting timelapse column:', sort.direction);
        this.sortTimelapseColumn(sort.direction);
      } else {
        // Let MatTableDataSource handle other columns
        this.dataSource.sort = this.sort;
      }
    });

    // Custom sorting logic for all columns through the data accessor
    this.dataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case 'timelapse':
          return this.getTimelapseHours(item);
        default:
          return item[property];
      }
    };

    // Force the dataSource to be sorted using our custom sortingDataAccessor
    setTimeout(() => {
      // This ensures that the sort mechanism is initialized properly
      this.dataSource._updateChangeSubscription();

      // If there's an existing sort, apply it
      if (this.currentSort && this.currentSort.active) {
        this.sort.active = this.currentSort.active;
        this.sort.direction = this.currentSort.direction;

        if (this.currentSort.active === 'timelapse') {
          this.sortTimelapseColumn(this.currentSort.direction);
        } else {
          this.dataSource.sort = this.sort;
        }
      }
    }, 100);
  }
  sortTimelapseColumn(direction: 'asc' | 'desc' | ''): void {
    console.log('Sorting timelapse column with direction:', direction);
    if (!direction) return;

    // Make a copy of the data before sorting
    const dataToSort = [...this.prList];

    // Sort the array by calculated timelapse
    dataToSort.sort((a, b) => {
      const aTime = this.getTimelapseHours(a);
      const bTime = this.getTimelapseHours(b);
      console.log(
        `Comparing: ${a.pullRequestId}(${aTime}h) with ${b.pullRequestId}(${bTime}h)`
      );
      if (aTime === bTime) return 0;
      return direction === 'asc' ? aTime - bTime : bTime - aTime;
    });

    // Update both the arrays to ensure consistency
    this.prList = dataToSort;
    this.dataSource.data = dataToSort;

    console.log(
      'After sorting:',
      this.prList.map((p) => `${p.pullRequestId}:${this.getTimelapseHours(p)}`)
    );

    // Force update in case the table is showing filtered data
    if (this.searchMode === 'client') {
      this.applyClientSideFilter(this.currentFilter);
    }
  }
  getTimelapseHours(pr: any): number {
    // Delegate to the time service
    return this.timeService.getTimelapseHours(pr);
  }
  loadPrData() {
    // Prevent multiple calls within a short time period
    const now = Date.now();
    if (
      now - this._lastApiCallTimestamp <
      this.configService.API_DEBOUNCE_TIME
    ) {
      ('Ignoring duplicate API call (too soon after previous call)');
      return;
    }

    // Update the timestamp
    this._lastApiCallTimestamp = now;

    // Always start with loading state
    this.isLoading = true;

    // Always use server mode for empty filter
    if (!this.currentFilter) {
      this.searchMode = 'server';
    } // Calculate pagination parameters
    const skip = 0; // Always start from 0 for infinite scroll
    const top = this.batchSize;

    `Loading data: page=${0}, size=${this.batchSize}, skip=${skip}, top=${top}`;

    // Use common service to prepare filter parameters
    const filterParam = this.prDataCommon.formatFilterParams(
      this.currentFilter,
      this.statusFilter,
      this.searchMode
    );

    // Remove server-side sorting since API doesn't support it properly
    // All sorting will be handled client-side

    // Use common service to format date parameters
    const { minTime, maxTime } = this.prDataCommon.formatDateParams(
      this.startDate,
      this.endDate
    );

    // Get URL from service
    const url = this.apiUrlService.getPullRequestsUrl(
      top,
      skip,
      this.selectedProject,
      this.selectedRepo,
      this.patToken,
      filterParam,
      undefined, // Removed orderBy parameter to disable server-side sorting
      this.statusFilter, // Pass status explicitly
      minTime, // Start date for filtering
      maxTime // End date for filtering
    );

    // Make the HTTP request - simplified for reliability
    this.http.get<ApiResponse>(url).subscribe({
      next: (response) => {
        if (response && response.data) {
          // Clear any existing filter
          this.dataSource.filter = '';

          // Update data source with new data
          this.dataSource.data = response.data;

          // Apply current sort to the data if we have an active sort
          if (this.currentSort && this.currentSort.direction) {
            if (this.currentSort.active === 'timelapse') {
              this.sortTimelapseColumn(this.currentSort.direction);
            } else {
              // For other columns, let dataSource handle it
              this.dataSource.sort = this.sort;
            }
          }          // Fetch active comments for each PR
          this.fetchActiveCommentsForPRs(response.data);
            // Update prList with the new data for infinite scrolling
          this.prList = [...response.data];
          
          // If date filters are applied, count PRs from loaded data
          // Otherwise use the counts from API response if available
          // if (this.hasDateFilters()) {
          //   console.log('Date filter applied - updating PR counts from loaded data');
          //   this.countPrsByStatus(this.prList);
          // } else 
          if (response.totalCount !== undefined) {
            this.totalCount = response.totalCount;
          }

          this.lastUpdated = new Date();
        } else {
          console.warn('No PR data found or invalid format');
          this.dataSource.data = [];
        }

        // Hide loading indicator
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching PR data:', error);
        this.isLoading = false;
        this.dataSource.data = [];
      },
    });
  }  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value
      .trim()
      .toLowerCase();

    // Always use the searchTerms subject to benefit from debounce
    // This will route the search through the debounced pipeline
    this.searchTerms.next(filterValue);
    
    // Show immediate feedback that the search is in progress
    if (filterValue) {
      this.isLoading = true;
    }
  }  // Clear search field and reset filter - simplified version
  clearSearch(input: HTMLInputElement): void {
    // Clear input field
    input.value = '';

    // Use the searchTerms subject to clear the filter, which will
    // flow through our debounced pipeline
    this.searchTerms.next('');

    // No need to manually set loading state or reload data,
    // as the subscription to searchTerms will handle that
  }
  /**
   * Handle date range changes from the PR filter component
   * @param dateRange The selected date range with startDate and endDate
   */
  onDateRangeChanged(dateRange: {
    startDate: Date | null;
    endDate: Date | null;
  }): void {
    console.log('Date range changed in PR component:', dateRange);
    
    // Store previous filter state to detect changes in filter type
    const hadDateFiltersBefore = this.hasDateFilters();
    
    // Update filter values
    this.startDate = dateRange.startDate;
    this.endDate = dateRange.endDate;
    
    // Detect if we switched between filtered/unfiltered state
    const hasDateFiltersNow = this.hasDateFilters();
    const filterTypeChanged = hadDateFiltersBefore !== hasDateFiltersNow;
    
    // Set loading states for PR counts
    // this.loadingActiveCount = true;
    // this.loadingCompletedCount = true;
    // this.loadingAbandonedCount = true;
    
    // Reset data for new filter
    this.prList = [];
    this.prSet.clear();
    this.hasMore = true;
    this.continuationTokens.clear(); // Clear continuation tokens for clean reload

    // Reload data with the new date range
    this.isLoading = true;
    console.log('Loading data with date range:', 
      this.startDate ? this.startDate.toISOString() : 'null', 
      this.endDate ? this.endDate.toISOString() : 'null');
    this._lastApiCallTimestamp = 0; // Force a reload
    
    // If filters were cleared (switching from filtered to unfiltered state)
    // Use the count API for better performance
    if (filterTypeChanged && !this.hasDateFilters()) {
      console.log('Date filters cleared - switching to count API');
      this.fetchPrStatusCounts(true);
    }
    
    // Always load the data whether filtered or not
    this.loadPrData();
  }

  // Client-side filtering
  applyClientSideFilter(filterValue: string) {
    // Apply filter to the MatTableDataSource
    this.dataSource.filter = filterValue;

    // Log the filtered data count
    `Filter results: ${this.dataSource.filteredData.length} of ${this.dataSource.data.length} records match`;

    // If the filter returns very few results or many results, switch to server-side filtering
    if (
      (this.dataSource.filteredData.length < 5 ||
        this.dataSource.filteredData.length > 90) &&
      this.dataSource.data.length > 100
    ) {
      ('Switching to server-side filtering for more accurate results');
      this.searchMode = 'server';
      this.loadPrData();
    }
  }
  // Custom filter predicate for MatTableDataSource  // setupFilterPredicate method removed - now using the shared service implementation

  getStatusClass(status: string): string {
    status = status.toLowerCase();
    if (status === 'active') return 'status-active';
    if (status === 'completed') return 'status-completed';
    if (status === 'abandoned') return 'status-abandoned';
    if (status === 'draft') return 'status-draft';
    return '';
  }

  goBack() {
    this.router.navigate(['/']);
  }

  /**
   * Gets the repository name without the refs/heads/ prefix
   */ formatBranchName(branchName: string): string {
    return branchName.replace('refs/heads/', '');
  }
  // Navigate to the PR details page
  navigateToPrDetails(prId: number): void {
    this.router.navigate(['/pr-details', prId]);
  } // Refresh the data
  refreshData(): void {
    console.log('Manual refresh requested');
    // Reset pagination state
    this._lastApiCallTimestamp = 0;
    // Refresh PR status counts with force refresh to bypass cache
    this.fetchPrStatusCounts(true);
    // Load data with current pagination settings
    this.loadPrData();
  }
  /**
   * Sets default active comments count for pull requests
   * @param prData Array of pull request data
   */
  fetchActiveCommentsForPRs(prData: any[]) {
    if (!prData || prData.length === 0) return;

    // Set a default value for active comments and initialize pipeline status
    for (const pr of prData) {
      pr.activeComments = 0;
      pr.pipelineStatus = 'Loading...';
      pr.pipelineStatusDisplay = 0;

      // Fetch pipeline status for each PR if we have the required IDs
      if (this.selectedProjectId && this.selectedRepoId) {
        this.fetchPipelineStatus(pr);
      }
    }

    // Update the data source
    this.dataSource.data = [...prData];
  }
  /**
   * Fetches the actual comment count for a specific PR
   * @param pr The pull request to fetch comments for
   * @param event The click event
   */
  fetchCommentCount(pr: any, event: Event): void {
    // Prevent navigation to PR details
    event.stopPropagation();

    // If already loading, don't make another request
    if (pr.loadingComments) {
      return;
    }

    // Show loading state for this specific row
    pr.loadingComments = true; // API endpoint to get comment threads, using ConfigService endpoint
    const url = `${this.configService.getCommentThreadsEndpoint()}?pullRequestId=${
      pr.pullRequestId
    }&project=${this.selectedProject}&repositories=${
      this.selectedRepo
    }&patToken=${this.patToken}`;

    this.http.get<CommentThreadResponse>(url).subscribe({
      next: (response) => {
        if (
          response &&
          response.data &&
          response.data.commentThread &&
          response.data.commentThread.commentThreads
        ) {
          // Count active and fixed threads based on the new response structure
          const threads = response.data.commentThread.commentThreads;
          const activeThreads = threads.filter(
            (thread: any) => thread.status === 'active'
          );
          const fixedThreads = threads.filter(
            (thread: any) => thread.status === 'fixed'
          );

          // Update the active comments count
          pr.activeComments = activeThreads.length;
          pr.resolvedComments = fixedThreads.length;
          pr.totalComments = threads.length;
        } else {
          console.warn('No comment threads found for PR #' + pr.pullRequestId);
          // Reset counts if no data found
          pr.activeComments = 0;
          pr.resolvedComments = 0;
          pr.totalComments = 0;
        }
        // Remove loading state
        pr.loadingComments = false;
      },
      error: (err) => {
        console.error('Error fetching comment threads:', err);
        // Remove loading state on error
        pr.loadingComments = false;
        // Set a default error state
        pr.activeComments = 0;
      },
    });
  }
  /**
   * Fetches pipeline status for a specific PR
   * @param pr The pull request to fetch pipeline status for
   */
  fetchPipelineStatus(pr: any): void {
    // Skip if we don't have repository ID or project ID
    if (!this.selectedRepoId || !this.selectedProjectId) {
      console.warn(
        'Repository ID or Project ID is missing, cannot fetch pipeline status'
      );
      return;
    }
    if (pr.loadingPipelineStatus) {
      return;
    }
    pr.loadingPipelineStatus = true;
    const url = this.apiUrlService.getContributionsHierarchyQueryUrl(
      pr.pullRequestId,
      this.selectedRepoId,
      this.selectedProjectId,
      this.patToken
    );
    this.http.get<ContributionsHierarchyQueryResponse>(url).subscribe({
      next: (response) => {
        if (
          response &&
          response.data &&
          response.data.dataProviders?.prDetailDataProvider?.policies
        ) {
          this.prDataCommon.processPolicies(
            pr,
            response.data.dataProviders.prDetailDataProvider.policies
          );
        } else {
          pr.pipelinePolicies = [];
          pr.activePolicy = null;
          pr.pipelineStatus = 'No data';
          pr.pipelineStatusDisplay = 0;
        }
        pr.loadingPipelineStatus = false;
      },
      error: (err) => {
        pr.pipelinePolicies = [];
        pr.activePolicy = null;
        pr.pipelineStatus = 'Error loading status';
        pr.pipelineStatusDisplay = 0;
        pr.loadingPipelineStatus = false;
      },
    });
  }

  /**
   * Processes pipeline policies: sorts by running first, then by evaluation date, and sets the most relevant policy.
   */ /**
   * Processes pipeline policies: sorts by running first, then by evaluation date, and sets the most relevant policy.
   */
  processPolicies(pr: any, policies: any[]) {
    // Method implementation moved to PrDataCommonService
    return this.prDataCommon.processPolicies(pr, policies);
  }
  // --- Status count methods - using the new API ---
  // refreshAllPrCounts(): void {
  //   this.fetchPrStatusCounts();
  // }

  /**
   * Sets the status filter based on the clicked PR count tab and reloads the data
   * @param status The status filter value to apply ('active', 'completed', 'abandoned', or 'all')
   */
  setStatusFilterAndLoad(status: string): void {
    console.log(`Setting status filter to: ${status}`);

    // Only update and reload if the filter has actually changed
    if (this.statusFilter !== status) {
      this.statusFilter = status;
      // Reset all data and pagination
      this.loadInitialPRs();
    }
  }

  // Legacy methods for backward compatibility
  getActivePRCount(): number {
    // This is now replaced by the counts stored in this.totalCount
    return this.totalCount;
  }

  getCompletedPRCount(): number {
    // This is now replaced by the counts stored in this.completedCount
    return this.completedCount;
  }
  getPipelineStatusIcon(status: number): string {
    return this.prDataCommon.getPipelineStatusIcon(status);
  }
  getPipelineStatusClass(status: number): string {
    return this.prDataCommon.getPipelineStatusClass(status);
  }
  /**
   * Opens the build timeline dialog to show build logs
   * @param pr The pull request with build information
   */ openBuildTimeline(pr: any): void {
    // Open the dialog with pipeline policies if available, else fallback to buildId
    this.dialog.open(BuildLogsDialogComponent, {
      width: '1000px',
      height: '90vh',
      maxWidth: '95vw',
      maxHeight: '95vh',
      data: {
        patToken: this.patToken,
        projectId: this.selectedProjectId,
        buildId: pr.buildId,
        pipelinePolicies: pr.pipelinePolicies || [],
      },
    });
  }
  /**
   * Returns a human-readable timelapse string for a PR
   * @param pr The pull request object
   */ getTimelapse(pr: any): any {
    // Delegate to the time service
    return this.timeService.getTimelapse(pr);
  }

  /**
   * Returns the most relevant pipeline policy for display:
   * - If any policy is in progress, return that.
   * - Else if any policy failed, return that.
   * - Else if all succeeded, return the last one.
   * - Else undefined.
   */
  getPrimaryPipelinePolicy(pr: any): any {
    return this.prDataCommon.getPrimaryPipelinePolicy(pr);
  }
  ngOnDestroy() {
    // Clean up subscriptions
    this.destroy$.next();
    this.destroy$.complete();

    // Clean up direct subscriptions
    if (this.sortSubscription) {
      this.sortSubscription.unsubscribe();
    }

    // We don't need to unsubscribe from prDataService observables
    // since they're shared services that will persist for the app lifecycle
  }
  openApproverModal(reviewers: any[]) {
    this.dialog.open(ApproverDialogComponent, {
      width: '500px',
      data: { reviewers: reviewers || [] },
    });
  }  loadInitialPRs() {
    // Reset all data
    this.prList = [];
    this.prSet.clear();
    this.hasMore = true;
    this.error = null;
    this.isLoadingMore = false;
    
    // If no date filters, fetch PR counts from API
    if (!this.hasDateFilters()) {
      console.log('No date filters - using count API in initial load');
      this.fetchPrStatusCounts(false);
    }

    // Reset sorting if we're loading from scratch
    if (this.sort) {
      // Keep track of the current sort to reapply after loading
      const currentSort = this.currentSort;

      // Clear the sort in the MatSort directive
      this.sort.active = '';
      this.sort.direction = '';
      this.currentSort = { active: '', direction: '' };

      // Load data, then reapply sort if needed
      this.loadMorePRs();

      if (currentSort && currentSort.direction) {
        setTimeout(() => {
          this.sort.active = currentSort.active;
          this.sort.direction = currentSort.direction;
          this.currentSort = currentSort;

          if (currentSort.active === 'timelapse') {
            this.sortTimelapseColumn(currentSort.direction);
          }
        }, 500); // Give it some time to load the data first
      }
    } else {
      this.loadMorePRs();
    }
  }
  loadMorePRs() {
    if (this.isLoadingMore || !this.hasMore) return;
    this.isLoadingMore = true;
    const top = this.batchSize;
    const skip = this.prList.length; // Use common service to prepare filter parameters
    const filterParam = this.prDataCommon.formatFilterParams(
      this.currentFilter,
      this.statusFilter,
      'server' // Always use server mode for infinite scroll loading
    );

    // Use common service to format date parameters
    const { minTime, maxTime } = this.prDataCommon.formatDateParams(
      this.startDate,
      this.endDate
    );

    const url = this.apiUrlService.getPullRequestsUrl(
      top,
      skip,
      this.selectedProject,
      this.selectedRepo,
      this.patToken,
      filterParam || undefined,
      undefined, // Remove server-side sorting
      this.statusFilter, // Pass status explicitly
      minTime, // Start date for filtering
      maxTime // End date for filtering
    );
    this.http.get<any>(url).subscribe({
      next: (res) => {
        const newPRs = (res.data || []).filter(
          (pr: any) => !this.prSet.has(pr.pullRequestId)
        );
        newPRs.forEach((pr: any) => this.prSet.add(pr.pullRequestId));
        // Fetch pipeline status for each new PR
        for (const pr of newPRs) {
          pr.pipelineStatus = 'Loading...';
          pr.pipelineStatusDisplay = 0;
          this.fetchPipelineStatus(pr);
        }        // Update the prList with new data
        this.prList = [...this.prList, ...newPRs];

        // Always update the data source with the complete prList
        this.dataSource.data = this.prList;
          // If date filters are applied, update counts from loaded data
        // if (this.hasDateFilters()) {
        //   console.log('Date filter applied - updating PR counts after loading more');
        //   this.countPrsByStatus(this.prList);
        // }

        console.log(
          `Loaded ${newPRs.length} new PRs, total: ${this.prList.length}`
        );

        // Apply current sort to the updated list if we have an active sort
        if (this.currentSort && this.currentSort.direction) {
          console.log('Re-applying sort:', this.currentSort);
          if (this.currentSort.active === 'timelapse') {
            this.sortTimelapseColumn(this.currentSort.direction);
          } else {
            // For other columns, let datasource handle it
            setTimeout(() => {
              this.sort.active = this.currentSort.active;
              this.sort.direction = this.currentSort.direction;
              this.dataSource.sort = this.sort;
            });
          }
        }

        this.hasMore = res.data && res.data.length === top;
        this.isLoadingMore = false;
      },
      error: (err) => {
        this.error = "Couldn't load pull requests. Please try again.";
        this.isLoadingMore = false;
        this.hasMore = false;
      },
    });
  }
  /**
   * Fetches the PR counts by status using the cached service
   * Only fetches fresh data when necessary
   */
  fetchPrStatusCounts(forceRefresh = false): void {
    if (!this.selectedProject || !this.selectedRepo || !this.patToken) {
      console.warn(
        'Cannot fetch PR status counts - missing required parameters'
      );
      return;
    }

    console.log(
      `Fetching PR status counts for ${this.selectedProject}/${
        this.selectedRepo
      }${forceRefresh ? ' (forced refresh)' : ''}`
    );

    // Set loading states
    this.loadingActiveCount = true;
    this.loadingCompletedCount = true;
    this.loadingAbandonedCount = true;

    // Use the cached service to get PR counts
    this.prDataService.getPrStatusCount(
      this.http,
      this.selectedProject,
      this.selectedRepo,
      this.patToken,
      this.apiUrlService,
      forceRefresh
    );

    // Subscribe to the observable counts
    this.prDataService.activeCount$.subscribe((count) => {
      if (count !== null) {
        this.totalCount = count;
        this.loadingActiveCount = false;

        // Once active count is loaded, we can hide the main loading indicator
        this.isLoading = false;
      }
    });

    this.prDataService.completedCount$.subscribe((count) => {
      if (count !== null) {
        this.completedCount = count;
        this.loadingCompletedCount = false;
      }
    });

    this.prDataService.abandonedCount$.subscribe((count) => {
      if (count !== null) {
        this.abandonedCount = count;
        this.loadingAbandonedCount = false;
      }
    });
  }
  onScroll() {
    if (this.virtualScroll) {
      const end = this.virtualScroll.measureScrollOffset('bottom');
      // Only load more when we're close to the bottom, have more to load, and aren't already loading
      if (end < 300 && this.hasMore && !this.isLoadingMore) {
        this.loadMorePRs();
        // No need to fetch PR counts again when scrolling, use the cached values
      }
    }
  }
  /**
   * Count PRs by status from the loaded data
   * Used to update counts after applying date filters
   */
  countPrsByStatus(prData: any[]): void {
    if (!prData) {
      console.log('No PR data provided to count');
      return;
    }
    
    // Set loading states to true while counting
    this.loadingActiveCount = true;
    this.loadingCompletedCount = true;
    this.loadingAbandonedCount = true;
    
    // Count PRs by status
    const activeCount = prData.filter(pr => 
      pr.status && pr.status.toLowerCase() === 'active').length;
    const completedCount = prData.filter(pr => 
      pr.status && pr.status.toLowerCase() === 'completed').length;
    const abandonedCount = prData.filter(pr => 
      pr.status && pr.status.toLowerCase() === 'abandoned').length;
      
    // Update the counts
    this.totalCount = activeCount;
    this.completedCount = completedCount;
    this.abandonedCount = abandonedCount;
    
    // Hide loading states
    this.loadingActiveCount = false;
    this.loadingCompletedCount = false;
    this.loadingAbandonedCount = false;
    
    console.log(`Counted PRs: Active: ${activeCount}, Completed: ${completedCount}, Abandoned: ${abandonedCount}`);
    console.log(`Date filter active: ${this.startDate ? 'yes' : 'no'} (${this.startDate ? this.startDate.toISOString() : 'none'} to ${this.endDate ? this.endDate.toISOString() : 'none'})`);
  }
  /**
   * Check if date filters are currently applied
   * @returns true if either start or end date is set
   */
  hasDateFilters(): boolean {
    return !!(this.startDate || this.endDate);
  }
}
