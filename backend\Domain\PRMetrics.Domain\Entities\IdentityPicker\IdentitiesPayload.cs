﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities.IdentityPicker
{
    /// <summary>
    /// Represents the payload sent to the Azure DevOps Identity Picker API
    /// to query for identities based on specified parameters.
    /// </summary>
    public class IdentitiesPayload
    {
        /// <summary>
        /// The query string to search identities (e.g., user UUID or name).
        /// </summary>
        public string Query { get; set; }

        /// <summary>
        /// The types of identities to search for (e.g., "user", "group").
        /// </summary>
        public List<string> IdentityTypes { get; set; }

        /// <summary>
        /// The scopes within which to perform the operation (e.g., "ims", "source").
        /// </summary>
        public List<string> OperationScopes { get; set; }

        /// <summary>
        /// Hint about the type of the query, such as "uid" for user identifier.
        /// </summary>
        public string QueryTypeHint { get; set; }

        /// <summary>
        /// Options to specify minimum and maximum number of results returned.
        /// </summary>
        public QueryOptions Options { get; set; }

        /// <summary>
        /// List of properties to include in the response (e.g., "DisplayName", "Mail", "Active").
        /// </summary>
        public List<string> Properties { get; set; }
    }

    /// <summary>
    /// Options to control the minimum and maximum number of results
    /// returned by the Identity Picker query.
    /// </summary>
    public class QueryOptions
    {
        /// <summary>
        /// Minimum number of results to return.
        /// </summary>
        public int MinResults { get; set; }

        /// <summary>
        /// Maximum number of results to return.
        /// </summary>
        public int MaxResults { get; set; }
    }
}
