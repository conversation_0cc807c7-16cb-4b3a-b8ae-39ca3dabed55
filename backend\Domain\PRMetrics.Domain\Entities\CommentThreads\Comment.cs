﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities.CommentThreads
{
    /// <summary>
    /// Represents a comment within a pull request. This class holds information
    /// about the comment content, author, timestamps, and related metadata.
    /// </summary>
    public class Comment
    {
        /// <summary>
        /// Gets or sets the unique identifier of the comment.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the parent comment if this is a reply.
        /// If this is the root comment, this will be 0 or null.
        /// </summary>
        public int ParentCommentId { get; set; }

        /// <summary>
        /// Gets or sets the content of the comment.
        /// This is the text that the author has written in the comment.
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the comment was published.
        /// </summary>
        public DateTime PublishedDate { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the comment was last updated.
        /// </summary>
        public DateTime LastUpdatedDate { get; set; }

        /// <summary>
        /// Gets or sets the author of the comment.
        /// This is the user who created or updated the comment.
        /// </summary>
        public User Author { get; set; }

        /// <summary>
        /// Gets or sets the type of the thread this comment belongs to.
        /// It can be either 'reviewer' for reviewer comments or 'review response' for responses to reviewers.
        /// </summary>
        public string ThreadType { get; set; }
    
        /// <summary>
        /// Gets or sets the type of comment.
        /// This can indicate if the comment is a general comment or a special type (e.g., inline comment).
        /// </summary>
        public string CommentType { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the comment was response updated.
        /// </summary>
        public DateTime ResponseUpdatedDate { get; set; }
    }
}
