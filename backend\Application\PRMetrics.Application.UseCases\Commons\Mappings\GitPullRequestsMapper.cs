﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Domain.Entities;

namespace  RIB.PRMetrics.Application.UseCases.Commons.Mappings
{
    /// <summary>
    /// AutoMapper Profile for mapping between GitPullRequest and GitPullRequestDto, along with related objects.
    /// </summary>
    public class GitPullRequestsMapper : Profile
    {
        /// <summary>
        /// Constructor that defines the mapping configurations for GitPullRequest and related entities.
        /// </summary>
        public GitPullRequestsMapper()
        {
            // Mapping configuration between GitPullRequest and GitPullRequestDto
            CreateMap<GitPullRequest, GitPullRequestDto>()
                .ForMember(dest => dest.PullRequestId, opt => opt.MapFrom(src => src.PullRequestId))  // Mapping 'PullRequestId' property
                .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.Title))                    // Mapping 'Title' property
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))                  // Mapping 'Status' property
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))        // Mapping 'Description' property
                .ForMember(dest => dest.MergeStatus, opt => opt.MapFrom(src => src.MergeStatus))        // Mapping 'MergeStatus' property
                .ForMember(dest => dest.isDraft, opt => opt.MapFrom(src => src.isDraft))                // Mapping 'isDraft' property
                .ForMember(dest => dest.CreationDate, opt => opt.MapFrom(src => src.CreationDate))      // Mapping 'CreationDate' property
                .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy))            // Mapping 'CreatedBy' from GitIdentityRef to GitIdentityRefDto
                .ForMember(dest => dest.Reviewers, opt => opt.MapFrom(src => src.Reviewers))            // Mapping 'Reviewers' from GitReviewerDto
                .ForMember(dest => dest.Repository, opt => opt.MapFrom(src => src.Repository))          // Mapping 'Repository' from GitRepository to GitRepositoryDto
                .ForMember(dest => dest.SourceRefName, opt => opt.MapFrom(src => src.SourceRefName))    // Mapping 'SourceRefName' property
                .ForMember(dest => dest.TargetRefName, opt => opt.MapFrom(src => src.TargetRefName))    // Mapping 'TargetRefName' property
                .ForMember(dest => dest.DelayIndex, opt => opt.MapFrom(src => src.DelayIndex));        // Mapping 'DelayIndex' property

            // Mapping configuration for GitIdentityRef to GitIdentityRefDto
            CreateMap<GitIdentityRef, GitIdentityRefDto>();

            // Mapping configuration for GitRepository to GitRepositoryDto
            CreateMap<GitRepository, GitRepositoryDto>();

            // Mapping configuration for ReviewerDto to GitReviewerDto
            CreateMap<ReviewerDto, GitReviewerDto>();


            // Mapping configuration for GitPullRequestStatusCount to GitPullRequestStatusCountDto
            CreateMap<GitPullRequestStatusCount, GitPullRequestStatusCountDto>().ReverseMap();

            // Mapping configuration for GitPullRequestConflict to GitPullRequestConflictDto
            CreateMap<GitPullRequestConflict, GitPullRequestConflictDto>()
                .ForMember(dest => dest.ConflictPath, opt => opt.MapFrom(src => src.ConflictPath))
                .ForMember(dest => dest.ConflictId, opt => opt.MapFrom(src => src.ConflictId))
                .ForMember(dest => dest.ConflictType, opt => opt.MapFrom(src => src.ConflictType))
                .ForMember(dest => dest.Url, opt => opt.MapFrom(src => src.Url));

            CreateMap<ExportExcelReport, ExportExcelReportDto>().ReverseMap();

            CreateMap<ProcessProgress, ProcessProgressDto>().ReverseMap();



        }
    }
}
