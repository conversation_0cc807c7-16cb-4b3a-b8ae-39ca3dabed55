﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using System.Net;
using Newtonsoft.Json;
using   RIB.PRMetrics.Application.Interface.Persistence;
using   RIB.PRMetrics.Domain;
using   RIB.PRMetrics.Domain.Entities;
using   RIB.PRMetrics.Persistence.Common;

namespace  RIB.PRMetrics.Persistence.Repositories
{
    /// <summary>
    /// This class is responsible for retrieving Git repository data from Azure DevOps.
    /// It communicates with the Azure DevOps API to fetch repository information.
    /// </summary>
    public class GitRepoRepositories : IGitRepoRepository
    {
        private readonly AzureDevopsUrlBuilder azureDevopsUrlBuilder;
        private readonly AZDevopsClientCommon aZDevopsClientCommon;

        /// <summary>
        /// Initializes an instance of the <see cref="GitRepoRepositories"/> class.
        /// The constructor injects the necessary dependencies for interacting with Azure DevOps API.
        /// </summary>
        /// <param name="_azureDevopsUrlBuilder">An instance of <see cref="AzureDevopsUrlBuilder"/> used to build Azure DevOps API URLs.</param>
        /// <param name="httpClientFactory">A factory for creating HTTP client instances (not used here, but available for future extension).</param>
        /// <param name="aZDevopsClientCommon">An instance of <see cref="AZDevopsClientCommon"/> for sending HTTP requests to Azure DevOps API.</param>
        public GitRepoRepositories(AzureDevopsUrlBuilder _azureDevopsUrlBuilder, IHttpClientFactory httpClientFactory, AZDevopsClientCommon _aZDevopsClientCommon)
        {
            azureDevopsUrlBuilder = _azureDevopsUrlBuilder ?? throw new ArgumentNullException(nameof(azureDevopsUrlBuilder));
            this.aZDevopsClientCommon = _aZDevopsClientCommon ?? throw new ArgumentNullException(nameof(aZDevopsClientCommon));
        }

        /// <summary>
        /// Retrieves a list of Git repositories from Azure DevOps for a specific project.
        /// </summary>
        /// <param name="project">The name of the project for which to retrieve repositories.</param>
        /// <param name="token">The Personal Access Token (PAT) used for authentication with Azure DevOps API.</param>
        /// <returns>A list of <see cref="GitRepository"/> representing the repositories returned by Azure DevOps.</returns>
        /// <exception cref="Exception">Thrown when the request fails or an error occurs while retrieving the repositories.</exception>
        public async Task<List<GitRepository>> GetGitRepositoriesAsync(string project, string token)
        {
            var url = azureDevopsUrlBuilder.BuildGitRepositoryUri(project);
            var response = await aZDevopsClientCommon.GetAsync(url, token);

            if (response.IsSuccessStatusCode && response.StatusCode == HttpStatusCode.OK)
            {
                var jsonResponse = await response.Content.ReadAsStringAsync();
                var repositoryResponse = JsonConvert.DeserializeObject<GitRepositoryList>(jsonResponse);
                return repositoryResponse?.Value ?? new List<GitRepository>();
            }

            // Handle error by throwing an exception if the response is not successful
            throw new Exception($"Error retrieving Git repositories: {response.ReasonPhrase}");
        }

        /// <summary>
        /// Retrieves the details of a specific Git repository from Azure DevOps.
        /// </summary>
        /// <param name="pullRequestSearch">An instance of <see cref="GitCommon"/> containing project, repository, and PAT token information.</param>
        /// <returns>A <see cref="GitRepository"/> representing the details of the specified repository.</returns>
        /// <exception cref="Exception">Thrown when the request fails or an error occurs while retrieving the repository details.</exception>
        public async Task<GitRepository> GetGitRepositoriesDetailsAsync(GitCommon pullRequestSearch)
        {
            var url = azureDevopsUrlBuilder.BuildGitRepositoryUri(pullRequestSearch, pullRequestSearch.Repositories);
            var response = await aZDevopsClientCommon.GetAsync(url, pullRequestSearch.PATToken);

            if (response.IsSuccessStatusCode && response.StatusCode == HttpStatusCode.OK)
            {
                var jsonResponse = await response.Content.ReadAsStringAsync();
                var repositoryResponse = JsonConvert.DeserializeObject<GitRepository>(jsonResponse);
                return repositoryResponse ?? new GitRepository();
            }

            // Handle error by throwing an exception if the response is not successful
            throw new Exception($"Error retrieving Git repository details: {response.ReasonPhrase}");
        }
    }
}
