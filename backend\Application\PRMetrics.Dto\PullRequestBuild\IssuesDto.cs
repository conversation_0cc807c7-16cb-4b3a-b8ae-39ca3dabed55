﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto.PullRequestBuild
{
    /// <summary>
    /// Represents an issue related to a pull request build.
    /// This class contains the details about the issue's type, category, and message.
    /// </summary>
    public class IssuesDto
    {
        /// <summary>
        /// Gets or sets the type of the issue.
        /// This field indicates the category or nature of the issue, such as "error", "warning", or "info".
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Gets or sets the category of the issue.
        /// This field classifies the issue further, such as "build", "test", or "linting".
        /// </summary>
        public string Category { get; set; }

        /// <summary>
        /// Gets or sets the message associated with the issue.
        /// This is a descriptive message explaining the details of the issue that occurred during the build process.
        /// </summary>
        public string Message { get; set; }
    }
}
