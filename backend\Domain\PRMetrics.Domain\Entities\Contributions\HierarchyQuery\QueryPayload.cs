﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Newtonsoft.Json;

namespace RIB.PRMetrics.Domain.Entities.Contributions.HierarchyQuery
{
    /// <summary>
    /// Represents the payload for a query, including contribution IDs and the associated data provider context.
    /// </summary>
    public class QueryPayload
    {
        /// <summary>
        /// Gets or sets the list of contribution IDs to be queried.
        /// </summary>
        [JsonProperty("contributionIds")]
        public string[] ContributionIds { get; set; } = [];

        /// <summary>
        /// Gets or sets the data provider context associated with the query.
        /// </summary>
        [JsonProperty("dataProviderContext")]
        public DataProviderContext DataProviderContext { get; set; }
    }
}
