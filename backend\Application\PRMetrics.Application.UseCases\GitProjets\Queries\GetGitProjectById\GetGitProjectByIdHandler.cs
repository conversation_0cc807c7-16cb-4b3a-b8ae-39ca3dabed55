﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using MediatR;
using   RIB.PRMetrics.Application.Dto;
using   RIB.PRMetrics.Application.Interface.Persistence;
using   RIB.PRMetrics.Application.UseCases.Commons.Bases;

namespace  RIB.PRMetrics.Application.UseCases.GitProjets.Queries.GetGitProjectById
{
    /// <summary>
    /// Handler for the GetGitProjectById query. Fetches a GitProject by its ID.
    /// </summary>
    public class GetGitProjectByIdHandler : IRequestHandler<GetGitProjectByIdQuery, BaseReponseGeneric<GitProjectDto>>
    {
        // Dependency injection of IUnitOfWork to access repositories and IMapper to map between entities and DTOs
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        /// <summary>
        /// Constructor for GetGitProjectByIdHandler that injects IUnitOfWork and IMapper.
        /// </summary>
        /// <param name="unitOfWork">Unit of work interface to access repositories</param>
        /// <param name="mapper">AutoMapper instance to map entities to DTOs</param>
        public GetGitProjectByIdHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));  // Throw exception if unitOfWork is null
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));  // Throw exception if mapper is null
        }

        /// <summary>
        /// Handles the GetGitProjectByIdQuery. Fetches the Git project by its ID and returns a response with the GitProjectDto.
        /// </summary>
        /// <param name="request">The query containing the Git project ID and personal access token</param>
        /// <param name="cancellationToken">Cancellation token to support task cancellation</param>
        /// <returns>BaseResponseGeneric containing the GitProjectDto</returns>
        public async Task<BaseReponseGeneric<GitProjectDto>> Handle(GetGitProjectByIdQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<GitProjectDto>();  // Create an empty response object

            try
            {
                // Fetch the Git project from the repository using the provided project ID and PAT token
                var gitProject = await _unitOfWork.GitProjectRepository.GetGitProjectDetailsAsync(request.Id, request.PATToken);

                // Check if the project was found
                if (gitProject is not null)
                {
                    // Map the GitProject entity to GitProjectDto using AutoMapper
                    response.Data = _mapper.Map<GitProjectDto>(gitProject);

                    // Set success flag and success message
                    response.Succcess = true;
                    response.Message = "Git Project fetch succeed!";
                }
                else
                {
                    // If no project is found, set an appropriate message
                    response.Succcess = false;
                    response.Message = "Git Project not found.";
                }
            }
            catch (Exception ex)
            {
                // Handle any exceptions that occur during the fetching process
                response.Message = ex.Message;  // Set the exception message in the response
                response.Succcess = false;  // Indicate failure
            }

            // Return the response object containing the result
            return response;
        }
    }
}
