// Variables
@import '../../styles.scss';

$badge-border-radius: 16px;
$accent-color: #0078d4; // Define accent color for hover states

@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin status-badge($color) {
  background-color: rgba($color, 0.1);
  color: $color;

  .status-dot {
    background-color: $color;
  }
}

@mixin has-comments {
  background-color: rgba(#d40000, 0.1);
  color: #d40000;
      
  &:hover {
    background-color: rgba(#d40000, 0.2);
  }
}

@mixin has-resolved {
  background-color: rgba(#28a745, 0.1);
  color: #28a745;
  
  &:hover {
    background-color: rgba(#28a745, 0.2);
  }
}

@mixin no-comments {
   background-color: rgba(#666, 0.1);
   color: #666;
      
  &:hover {
    background-color: rgba(#666, 0.2);
  }
}

/* Serial number column styles */
.serial-number-cell {
  width: 50px;
  min-width: 50px;
  max-width: 50px;
  text-align: center;
  color: var(--text-light, #666);
  font-weight: 500;
}

/* Make table rows take pointer cursor when hovering */
.pr-table .pr-row {
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(#666, 0.08);
  }
}

/* Container layout */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--background-color);
  font-family: var(--font-family);
  margin: 0;
  padding: 14px;
  position: relative; /* Added to contain absolute children */
}

/* Header styling */
.app-header-pr {
  z-index: 1000;
  background: $primary-color;
  color: white;
  padding: 16px 24px;
  @include flex-between;
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-logo {
  height: 32px;
  width: auto;
}

.app-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.user-profile-button {
  color: white;
  transform: scale(1.2); /* Make the icon slightly larger */
  transition: transform 0.2s ease;

  mat-icon {
    font-size: 28px;
    height: 28px;
    width: 28px;
  }

  &:hover {
    transform: scale(1.3); /* Grow on hover */
  }
}

/* Main content area */
.main-content {
  flex: 1;
  max-width: 1700px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  position: relative; /* Added to contain absolute children */
}

/* Card styling */
.data-card {
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  margin-bottom: 24px;
  overflow: hidden;
  background-color: var(--card-bg-color);
}

mat-card-header {
  background-color: $header-bg;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
}

/* Card header styling moved to pr-card-header.component.scss */

/* Card actions and record count styles */
.card-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.record-count {
  background-color: var(--primary-color);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 500;
}

mat-card-content {
  padding: 0;
}
/* Filter styling moved to pr-filter.component.scss */

/* Table container */
.table-container {
  overflow-x: auto;
  position: relative;
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color); /* Light grey border */
  border-radius: 4px 4px 0 0; /* Rounded corners only at the top */
  
  /* Customize scrollbars */
  &::-webkit-scrollbar {
    width: 8px; /* Width of vertical scrollbar */
    height: 8px; /* Height of horizontal scrollbar */
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1; /* Light gray background */
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #bbb; /* Scrollbar color */
    border-radius: 4px;
    
    &:hover {
      background: #999; /* Darker on hover */
    }
  }
}

/* Table styling */
.pr-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-family: var(--font-family);
  /* Ensure table takes full width and height of container */
  height: 100%;
  table-layout:auto;

  // /* Ensure column widths are appropriate */
  .mat-column-serialNumber {
    width: 50px;
    min-width: 60px;
  }
  // .mat-column-pullRequestId {
  //   width: 80px;
  //   min-width: 80px;
  // }
  
  // .mat-column-title {
  //   width: 50px;
  //   min-width: 50px;
  // }
  
  // .mat-column-creationDate,
  // .mat-column-closedDate,
  // .mat-column-completionDate {
  //   width: 120px;
  //   min-width: 120px;
  // }
  
  // .mat-column-author {
  //   width: 140px;
  //   min-width: 140px;
  // }
  
  // .mat-column-reviewers {
  //   min-width: 150px;
  // }
  // .mat-column-status {
  //   width: 110px;
  //   min-width: 110px;
  // }
  
  // .mat-column-commentsInfo {
  //   width: 140px;
  //   min-width: 140px;
  // }
}

/* Header cell styling */
th.mat-header-cell {
  background-color: #e6f2ff; /* Light blue background color - consistent with other changes */
  color: var(--text-dark, #333);
  font-family: $font-family; /* Using global font-family */
  font-weight: 500; /* Match with PR Analysis header */
  padding: 14px 16px;
  white-space: nowrap;
  border-bottom: 2px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2px 2px -1px rgba(0,0,0,0.05);
  font-size: 0.95rem; /* Closer to PR Analysis header for consistency */
  letter-spacing: 0.5px; /* Matching PR Analysis header */
}

.header-cell-content {
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  min-width: fit-content; /* Ensure content doesn't wrap */
  overflow: hidden;
  text-overflow: ellipsis;
}

.sort-icon {
  font-size: 18px;
  opacity: 0.6;
  margin-left: 4px;
  vertical-align: middle;
}

/* Ensure header cells have proper background when sticky */
.mat-mdc-header-row {
  background-color: #e6f2ff; /* Light blue background color matching header cells */
  height: 56px; /* Increased height to accommodate font and prevent wrapping */
}

td.mat-cell {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-color);
  font-size: 0.95rem;
}

.pr-row {
  height: 56px;
  transition: background-color 0.2s;
}

.pr-row:hover {
  background-color: var(--hover-color);
}

/* Cell styling */
.id-cell {
  font-weight: 600;
  color: var(--primary-color);
  white-space: nowrap; /* Prevent PR ID from breaking */
  overflow: visible; /* Ensure PR ID is fully visible */
  
  &.clickable {
    cursor: pointer;
    
    &:hover .pr-id-link {
      color: $accent-color;
      text-decoration: underline;
    }
  }
}

.pr-id-link {
  transition: color 0.2s ease;
}

.title-cell {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  padding-right: 16px; // Add some padding to ensure text doesn't touch the edge

  // Ensure the text is truncated with ellipses
  &::after {
    content: "";
    display: inline-block;
  }
}

.author-cell {
  font-weight: 500;
  
  &.clickable {
    cursor: pointer;
    
    &:hover {
      background-color: rgba($accent-color, 0.05);
    }
  }
}

.author-info {
  display: flex;
  align-items: center;
}

.reviewers-preview {
  display: flex;
  flex-direction: column;
  
  .author-name {
    font-weight: 500;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .more-reviewers {
    font-size: 0.85rem;
    color: var(--text-lighter);
    font-style: italic;
  }
}

.date-cell {
  white-space: nowrap;
}

.date-info {
  display: flex;
  flex-direction: column;
}

.date {
  font-weight: 500;
}

.time {
  font-size: 0.85rem;
  color: var(--text-lighter);
}

.branch-cell {
  max-width: 200px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
}

.branch-info {
  display: flex;
  align-items: center;
  gap: 4px;
  max-width: 100%;

  span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100% - 24px); // Subtract icon width and gap
  }
}

.branch-icon {
  font-size: 16px;
  color: var(--text-lighter);
  flex-shrink: 0; // Prevent icon from shrinking
}

/* Comments cell styling */
.comments-cell {
  padding: 0 8px;
  
  &.clickable {
    cursor: pointer;
  }
  .comment-button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 3px 8px;
    border-radius: 16px;
    background-color: rgba(#0078d4, 0.1);
    color: #0078d4;
    transition: background-color 0.2s, color 0.2s;
    min-width: 140px;
    justify-content: center;
    
    &:hover {
      background-color: rgba(#0078d4, 0.2);
    }
    
    &.has-comments {
     @include has-comments;
    }
    
    &.has-resolved {
      @include has-resolved;
    }
    
    &.no-comments {
      @include no-comments;
    }
    
    .comments-icon {
      font-size: 16px;
      height: 16px;
      width: 16px;
      margin-right: 4px;
      
      &.loading {
        animation: spin 1s linear infinite;
      }
    }
  }
  
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  
  .comments-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 3px 8px;
    border-radius: 16px;
    width: fit-content;
    color: var(--text-light);
    
    &.has-comments {
      background-color: rgba($primary-color, 0.1);
      color: $primary-color;
    }
    
    .comments-icon {
      font-size: 16px;
      height: 16px;
      width: 16px;
    }
  }
}

/* Merge status*/
.merge-info {
  .merge {
    padding: 3px 8px;
    border-radius: 4px;
    &.has-comments {
    @include has-comments;
  }

  &.has-resolved {
    @include has-resolved;
  }
  }
  
}
/* Status badges */
.status-cell {
  white-space: nowrap;
}

.status-badge {
  padding: 6px 10px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: capitalize;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap; /* Prevent text from breaking */
  overflow: visible; /* Ensure text doesn't get cut off */
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-active { @include status-badge(#0078d4); }
.status-completed { @include status-badge(#28a745); }
.status-abandoned { @include status-badge(#f5222d); }
.status-draft { @include status-badge(#722ed1); }

/* Pipeline status styles */
.pipeline-status-cell {
  white-space: nowrap;
}

.pipeline-info {
  display: flex;
  align-items: center;
}

.pipeline-badge {
  padding: 6px 10px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  
  &.clickable {
    cursor: pointer;
    transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
    }
    
    &:active {
      transform: translateY(0);
      box-shadow: none;
    }
  }
}

.loading-spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 10px;
}

/* Pipeline status colors */
.status-success {
  @include status-badge(#28a745); /* Green for success */
}

.status-failed {
  @include status-badge(#f5222d); /* Red for failed/expired */
}

.status-pending {
  @include status-badge(#f39c12); /* Orange/Yellow for in progress */
}

.status-unknown {
  @include status-badge(#9e9e9e); /* Gray for unknown status */
}

/* No data message */
.no-data-cell {
  padding: 40px 0 !important;
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-lighter);
  gap: 12px;
  padding: 20px 0;
}

.no-data-message mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
  opacity: 0.5;
}

/* No search results message */
.no-results-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  
  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: #f39c12;
    margin-bottom: 16px;
  }
  
  p {
    font-size: 16px;
    color: var(--text-color);
    margin: 0;
  }
}

/* No data info message */
.no-data-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  
  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: #888;
    margin-bottom: 16px;
  }
  
  p {
    font-size: 16px;
    color: var(--text-color);
    margin: 0;
  }
}

.no-data-message span {
  text-align: center;
  max-width: 300px;
  line-height: 1.5;
}

/* Paginator styling */
.paginator-container {
  @include flex-between;
  padding: 0 16px;
  border-top: 1px solid var(--border-color);
  background-color: #e6f2ff; /* Light blue background color matching headers */
  border: 1px solid var(--border-color);
  border-top: none; /* Remove top border as it connects with the table */
  border-radius: 0 0 4px 4px; /* Round only bottom corners */
  margin-top: -1px; /* Overlap with table border to avoid double border */
  position: relative; /* Ensure proper stacking context */
  z-index: 1; /* Ensure paginator is above other elements */
  font-family: $font-family; /* Consistent font with headers */
}

/* Refresh button animation */
.card-actions button {
  transition: transform 0.3s ease;
  
  &:hover {
    transform: rotate(180deg);
  }
}

mat-paginator {
  background-color: transparent;

  // Style the paginator form field
  ::ng-deep .mat-mdc-form-field {
    // Add a subtle border to make the dropdown more visible
    .mat-mdc-text-field-wrapper {
      background-color: white;
      border: 1px solid var(--border-color);
      border-radius: 4px;
    }
  }

  // Ensure pagination buttons are clickable and visible
  ::ng-deep .mat-mdc-paginator-navigation-previous,
  ::ng-deep .mat-mdc-paginator-navigation-next,
  ::ng-deep .mat-mdc-paginator-navigation-first,
  ::ng-deep .mat-mdc-paginator-navigation-last {
    color: $primary-color;
    opacity: 0.8;
    
    &:hover {
      opacity: 1;
      background-color: rgba($primary-color, 0.1);
    }
    
    &:not(.mat-mdc-paginator-navigation-disabled) {
      pointer-events: auto;
      cursor: pointer;
    }
    
    &.mat-mdc-paginator-navigation-disabled {
      opacity: 0.4;
    }
  }
}

.pagination-info {
  color: var(--text-light);
  font-size: 0.85rem;
  white-space: nowrap;
  padding-right: 16px;
}

/* Footer styling */
.app-footer {
  background-color: $header-bg;
  color: var(--text-light);
  padding: 16px 24px;
  border-top: 1px solid var(--border-color);
}

.footer-content {
  @include flex-between;
  max-width: 1700px;
  margin: 0 auto;
}

.footer-content p {
  margin: 0;
  font-size: 0.85rem;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem;
}

.refresh-info mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Loading spinner styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  color: var(--text-light);
}

.loading-container p {
  margin-top: 16px;
  font-size: 14px;
}

/* Table loading overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.5); /* Reduced opacity */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 20;
  backdrop-filter: blur(0.5px); /* Reduced blur effect */
  transition: all 0.2s ease-in-out; /* Smooth transition */
}

.loading-overlay-active {
  position: relative;
}

/* Small indicator for client-side operations */
.client-operation-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--primary-color);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 15;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.client-operation-active .client-operation-indicator {
  opacity: 1;
}

/* User menu styles - these will be applied globally */
::ng-deep .mat-mdc-menu-panel.mat-menu-panel {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  .mat-mdc-menu-content {
    padding: 0;
  }

  .mat-mdc-menu-item {
    height: 48px;
    line-height: 48px;

    .mat-icon {
      margin-right: 12px;
      color: var(--text-color);
    }

    &:hover {
      background-color: var(--hover-color);
    }
  }
}
.load-more {
  border: 1px solid#666;
  border-radius: 20px;
  &:hover {
    background-color: rgba($primary-color, 0.1);
    color: $primary-color;
  }
}
/* Global loading overlay */
.global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(255,255,255,0.7);
  z-index: 99999;
  display: flex !important;
  align-items: center;
  justify-content: center;
  pointer-events: all;

  /* Make sure spinner is visible above blur */
  > mat-spinner {
    z-index: 100000;
    position: relative;
    display: block;
  }
}

.app-container, .main-content {
  position: relative;
}

/* Remove blur from overlay itself */
.global-loading-overlay {
  filter: none !important;
}

/* Remove blur from all children of overlay */
.global-loading-overlay * {
  filter: none !important;
}

/* Prevent main content from being visible when loading overlay is active */
.global-loading-overlay ~ .data-card,
.global-loading-overlay ~ *:not(.global-loading-overlay) {
  filter: blur(2px);
  pointer-events: none;
  user-select: none;
}

/* Spinner wrapper to ensure centering */
.spinner-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  z-index: 100001;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-header-pr {
    flex-direction: row; /* Keep as row for mobile */
    justify-content: space-between;
    padding: 12px 16px;
  }

  /* Header actions don't need special mobile styling */

  .user-profile-button {
    /* Adjust size for mobile */
    transform: scale(1.1);

    &:hover {
      transform: scale(1.2);
    }
  }

  .card-header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .card-actions {
    align-self: flex-start;
  }

  .paginator-container {
    flex-direction: column-reverse;
    gap: 8px;
    padding: 8px 16px;
  }

  .pagination-info {
    padding-bottom: 8px;
  }

  .footer-content {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .main-content {
    padding: 16px;
  }
}

.virtual-scroll-viewport {
  height: 70vh;
  width: 100%;
  min-height: 400px;
  border: 1px solid var(--border-color);
  background: var(--card-bg-color);
  margin-top: 16px;
  border-radius: 4px;
  
  overflow-x: auto;
  
  /* Customize scrollbars */
  &::-webkit-scrollbar {
    width: 8px; /* Width of vertical scrollbar */
    height: 8px; /* Height of horizontal scrollbar */
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1; /* Light gray background */
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #bbb; /* Scrollbar color */
    border-radius: 4px;
    
    &:hover {
      background: #999; /* Darker on hover */
    }
  }
  
  /* Add shadow indicators when table can be scrolled horizontally */
  &.scrollable-x {
    background: 
      linear-gradient(to right, white 30%, rgba(255, 255, 255, 0)),
      linear-gradient(to right, rgba(255, 255, 255, 0), white 70%) 100% 0,
      radial-gradient(farthest-side at 0 50%, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0)),
      radial-gradient(farthest-side at 100% 50%, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0)) 100% 0;
    background-repeat: no-repeat;
    background-size: 40px 100%, 40px 100%, 14px 100%, 14px 100%;
    background-attachment: local, local, scroll, scroll;
  }
}

/* Add utility classes to make font styles consistent */
.mat-typography .pr-table {
  font-family: $font-family; /* Ensure consistent font family throughout table */
}

/* Consistent header styling class matching Pull Request Analysis header */
.pr-header-font {
  font-family: $font-family;
  font-size: 0.95rem;
  font-weight: 500;
  letter-spacing: 0.5px;
  color: var(--text-dark, #333);
}

/* Error message styling */
.error-message {
  color: #d40000;
  text-align: center;
  padding: 16px 0;
  font-weight: 500;
}

.load-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px 0 12px 0;
}

.completed-prs-instruction {
  font-size: 12px;
  color: #888;
  margin-left: 8px;
  margin-top: 2px;
  font-style: italic;
  letter-spacing: 0.1px;
  line-height: 1.4;
  display: inline-block;
}

/* PR counts styling moved to pr-card-header.component.scss */
