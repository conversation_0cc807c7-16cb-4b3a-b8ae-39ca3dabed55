﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application
{
    /// <summary>
    /// Represents a state event that occurs during the lifecycle of a Git pull request thread.
    /// A state event captures the timestamp, state, duration, and other related content of a state transition.
    /// </summary>
    public class StateEventDto
    {
        /// <summary>
        /// Gets or sets the timestamp of when the state event occurred.
        /// This represents the exact moment a state transition (such as from draft to published) took place.
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Gets or sets the state that the pull request thread was in at the time of the event.
        /// The state could be "draft", "published", or another state depending on the lifecycle of the pull request.
        /// </summary>
        public string State { get; set; } // "draft" or "published"

        /// <summary>
        /// Gets or sets the time interval the pull request thread remained in the given state.
        /// This interval is typically measured in terms of time elapsed in the state (e.g., time spent in the "draft" state).
        /// </summary>
        //public TimeSpan Interval { get; set; }

        /// <summary>
        /// Gets or sets the content that describes the interval or provides additional context to the state event.
        /// This could contain information such as why the pull request remained in a particular state or other contextual details.
        /// </summary>
        //public string IntervalContent { get; set; }

        public DateTime NextStateUpdatedDate { get; set; }
    }
}
