﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto.PullRequestBuild
{
    /// <summary>
    /// Represents the response that contains a list of timeline items for a pull request build.
    /// This DTO includes details such as the last changes made to the timeline, its identifier, and the associated records.
    /// </summary>
    public class TimeLineResponseDto
    {
        /// <summary>
        /// Gets or sets the list of timeline records.
        /// Each record represents a specific step or event in the pull request build process.
        /// </summary>
        public List<TimeLineItemDto> Records { get; set; }

        /// <summary>
        /// Gets or sets the name of the person who last modified the timeline.
        /// This property tracks who made the last update to the timeline.
        /// </summary>
        public string LastChangedBy { get; set; }

        /// <summary>
        /// Gets or sets the timestamp of when the timeline was last changed.
        /// This indicates when the last modification occurred.
        /// </summary>
        public string LastChangedOn { get; set; }

        /// <summary>
        /// Gets or sets the unique identifier for the timeline response.
        /// This ID can be used to uniquely reference this particular timeline response.
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the change ID associated with the timeline response.
        /// This ID typically refers to the specific change or commit related to the timeline data.
        /// </summary>
        public double ChangeId { get; set; }

        /// <summary>
        /// Gets or sets the URL related to the timeline response.
        /// This URL can be used to access more details or documentation about the timeline data.
        /// </summary>
        public string url { get; set; }
    }
}
