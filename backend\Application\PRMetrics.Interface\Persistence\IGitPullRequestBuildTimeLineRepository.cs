﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using RIB.PRMetrics.Domain.Entities.PullRequestBuild;

namespace RIB.PRMetrics.Application.Interface.Persistence
{
    /// <summary>
    /// Represents the repository interface for querying the build timeline of a Git pull request.
    /// This interface defines methods for retrieving build timeline data related to a specific pull request build.
    /// </summary>
    public interface IGitPullRequestBuildTimeLineRepository : IGenericRepository<Object>
    {
        /// <summary>
        /// Asynchronously retrieves the build timeline for a given Git pull request build.
        /// This method is used to fetch the timeline data for a specific build, based on the project, build ID, and authentication token.
        /// </summary>
        /// <param name="ProjectId">The unique identifier for the project to which the pull request build belongs.</param>
        /// <param name="buildId">The unique identifier for the build associated with the pull request.</param>
        /// <param name="PATToken">The Personal Access Token (PAT) for authentication to access the build data.</param>
        /// <returns>A Task representing the asynchronous operation, containing the <see cref="TimeLineResponse"/> with the build timeline data.</returns>
        Task<TimeLineResponse> GetGitPullRequestBuildTimeLineAsync(string ProjectId, string buildId, string PATToken);
    }
}
