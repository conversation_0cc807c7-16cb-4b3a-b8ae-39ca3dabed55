﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using RIB.PRMetrics.Application.Interface.Persistence;
using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;

namespace RIB.PRMetrics.Infrastructure.BackgroundJobs
{
    public class JobWorker : BackgroundService
    {
        private readonly ILogger<JobWorker> _logger;
        private readonly IJobQueue _jobQueue;
        private readonly IServiceProvider _serviceProvider;

        // Optional: limit max parallel jobs to avoid resource exhaustion
        private readonly SemaphoreSlim _concurrencyLimiter = new SemaphoreSlim(5); // Adjust max concurrency as needed

        public JobWorker(ILogger<JobWorker> logger, IJobQueue jobQueue, IServiceProvider serviceProvider)
        {
            _logger = logger;
            _jobQueue = jobQueue;
            _serviceProvider = serviceProvider;
        }
        private readonly ConcurrentDictionary<Guid, Task> _runningJobs = new();

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                if (_jobQueue.TryDequeue(out var job))
                {
                    using var scope = _serviceProvider.CreateScope();
                    var jobService = scope.ServiceProvider.GetRequiredService<IJobService>();

                    // Start job asynchronously without awaiting to allow concurrency
                    var task = Task.Run(() => jobService.ExecuteAsync(job), stoppingToken);

                    _runningJobs[job.Uuid] = task;

                    // Optionally, remove completed tasks from _runningJobs here or periodically
                }
                else
                {
                    await Task.Delay(500, stoppingToken);
                }
            }

            // Optionally wait for all running jobs on shutdown:
            await Task.WhenAll(_runningJobs.Values);
        }


        //protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        //{
        //    _logger.LogInformation("JobWorker started.");

        //    while (!stoppingToken.IsCancellationRequested)
        //    {
        //        if (_jobQueue.TryDequeue(out var job))
        //        {
        //            _ = Task.Run(async () =>
        //            {
        //                await _concurrencyLimiter.WaitAsync(stoppingToken);

        //                try
        //                {
        //                    using var scope = _serviceProvider.CreateScope();
        //                    var jobService = scope.ServiceProvider.GetRequiredService<IJobService>();
        //                    await jobService.ExecuteAsync(job);
        //                }
        //                catch (Exception ex)
        //                {
        //                    _logger.LogError(ex, $"Error processing job {job.Uuid}");
        //                }
        //                finally
        //                {
        //                    _concurrencyLimiter.Release();
        //                }
        //            }, stoppingToken);
        //        }
        //        else
        //        {
        //            await Task.Delay(500, stoppingToken);
        //        }
        //    }

        //    _logger.LogInformation("JobWorker stopped.");
        //}
    }
}
