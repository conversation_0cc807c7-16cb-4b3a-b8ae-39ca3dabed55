﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities.CommentThreads
{
    /// <summary>
    /// Represents a collection of comment threads for a specific pull request.
    /// This class includes the details of the pull request and the associated comment threads,
    /// along with statistics about active and resolved comments.
    /// </summary>
    public class GitPullRequestCommentThread
    {
        /// <summary>
        /// Gets or sets the list of comment threads associated with this pull request.
        /// These comment threads represent specific conversations or topics that are discussed in the pull request.
        /// </summary>
        public List<CommentThread> CommentThreads { get; set; }
    }
}
