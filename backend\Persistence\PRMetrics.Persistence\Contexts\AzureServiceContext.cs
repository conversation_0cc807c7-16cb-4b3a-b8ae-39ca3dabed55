﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Microsoft.Extensions.Configuration;

namespace  RIB.PRMetrics.Persistence.Contexts
{
    /// <summary>
    /// This class is responsible for interacting with Azure Key Vault to retrieve secrets.
    /// It uses the Azure SDK to fetch a secret (in this case, a Personal Access Token) stored in a Key Vault.
    /// The class retrieves the secret by accessing the Key Vault URL and Secret Name provided in the application configuration.
    /// </summary>
    public class AzureServiceContext
    {
        private readonly IConfiguration _configuration;
        private readonly SecretClient _secretClient;
        private readonly string _secretName;

        /// <summary>
        /// Initializes an instance of the <see cref="AzureServiceContext"/> class.
        /// The constructor accepts an IConfiguration instance that contains the Key Vault URL and Secret Name.
        /// The class uses the <see cref="SecretClient"/> to interact with Azure Key Vault and fetch secrets.
        /// </summary>
        /// <param name="configuration">The application's configuration, used to retrieve Azure Key Vault settings.</param>
        /// <exception cref="ArgumentNullException">Thrown when <paramref name="configuration"/> is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown when Key Vault URL or Secret Name is not provided in the configuration.</exception>
        public AzureServiceContext(IConfiguration configuration)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            var vaultUrl = configuration["AzureKeyVault:KeyVaultUrl"];
            _secretName = configuration["AzureKeyVault:SecretName"];
            if (string.IsNullOrEmpty(vaultUrl) || string.IsNullOrEmpty(_secretName))
                throw new InvalidOperationException("Key Vault URL or Secret Name is not configured.");

            _secretClient = new SecretClient(new Uri(vaultUrl), new DefaultAzureCredential());
        }

        /// <summary>
        /// Retrieves the secret (PAT Token) from the configured Azure Key Vault.
        /// This method fetches the secret by its name and returns its value.
        /// </summary>
        /// <returns>The value of the retrieved secret, which is the Personal Access Token (PAT).</returns>
        /// <exception cref="Azure.RequestFailedException">Thrown when an error occurs while fetching the secret from Azure Key Vault.</exception>
        public async Task<string> GetPATTokenAsync()
        {
            KeyVaultSecret secret = await _secretClient.GetSecretAsync(_secretName);
            return secret.Value;
        }
    }
}
