﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto
{
    /// <summary>
    /// Represents a user in the application, containing basic information about the devloper and reviewer.
    /// This class is used to transfer user-related data, such as ID, display name, and unique name.
    /// </summary>
    public class UserDto
    {
        /// <summary>
        /// Gets or sets the unique identifier for the user.
        /// This ID is typically used to uniquely identify the user within the system or external services.
        /// </summary>
        
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the display name of the user.
        /// This is the name displayed in the system or UI, which may be more user-friendly than the unique name.
        /// </summary>
        
        public string DisplayName { get; set; }

        /// <summary>
        /// Gets or sets the unique name of the user.
        /// This is a system-specific identifier that uniquely identifies the user, typically used for authentication or integration purposes.
        /// </summary>
        
        public string UniqueName { get; set; }
    }
}
