﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace  RIB.PRMetrics.Application.Dto
{
    /// <summary>
    /// Represents a pull request in an Azure DevOps Git repository.
    /// </summary>
    public class GitPullRequestDto
    {
        /// <summary>
        /// The unique identifier of the pull request.
        /// </summary>
        
        public int PullRequestId { get; set; }

        /// <summary>
        /// The title of the pull request.
        /// </summary>
        
        public string Title { get; set; }

        /// <summary>
        /// The current status of the pull request (e.g., active, completed, abandoned).
        /// </summary>
        
        public string Status { get; set; }

        /// <summary>
        /// The description or summary of the pull request.
        /// </summary>
        
        public string Description { get; set; }

        /// <summary>
        /// The merge status indicating whether the pull request can be merged.
        /// </summary>
        
        public string MergeStatus { get; set; }

        /// <summary>
        /// Indicates whether the pull request is in draft state.
        /// </summary>
        
        public string isDraft { get; set; } // Consider changing to bool if API returns true/false.

        /// <summary>
        /// The date and time when the pull request was created.
        /// </summary>
        
        public DateTime CreationDate { get; set; }

        /// <summary>
        /// The identity of the user who created the pull request.
        /// </summary>
        
        public UserDto CreatedBy { get; set; }

        /// <summary>
        /// The list of reviewers assigned to the pull request.
        /// </summary>
        
        public List<GitReviewerDto> Reviewers { get; set; }

        /// <summary>
        /// The repository to which the pull request belongs.
        /// </summary>
        
        public GitRepositoryDto Repository { get; set; }

        /// <summary>
        /// The source branch of the pull request.
        /// </summary>
        
        public string SourceRefName { get; set; }

        /// <summary>
        /// The target branch the pull request is merging into.
        /// </summary>
        
        public string TargetRefName { get; set; }

        /// <summary>
        /// The delay index value, used internally for tracking or scheduling.
        /// </summary>
        
        public string DelayIndex { get; set; }

        
    }
}
