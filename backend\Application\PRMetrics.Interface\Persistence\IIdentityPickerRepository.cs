﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using RIB.PRMetrics.Domain.Entities.IdentityPicker;

namespace RIB.PRMetrics.Application.Interface.Persistence
{
    /// <summary>
    /// Repository interface for managing Identity Picker related data operations.
    /// </summary>
    public interface IIdentityPickerRepository : IGenericRepository<object>
    {
        /// <summary>
        /// Asynchronously retrieves a list of identity items matching the specified user UUID.
        /// </summary>
        /// <param name="userUuid">The unique identifier of the user to query.</param>
        /// <param name="token">The authentication token used for API access.</param>
        /// <returns>A task representing the asynchronous operation, containing a list of <see cref="IdentitiesItem"/>.</returns>
        Task<List<IdentitiesItem>> GetIdentitiesAsync(string userUuid, string token);
    }
}
