﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using MediatR;
using RIB.PRMetrics.Application.Dto.PullRequestBuild;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequestBuildTimeLine.Queries.GetGitPullRequestBuildTimeLine
{
    /// <summary>
    /// Handler for the GetGitPullRequestBuildTimeLineQuery.
    /// Retrieves the build timeline for a specific Git pull request build
    /// and maps the result to a corresponding DTO.
    /// </summary>
    public class GetGitPullRequestBuildTimeLineHandler : IRequestHandler<GetGitPullRequestBuildTimeLineQuery, BaseReponseGeneric<TimeLineResponseDto>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        /// <summary>
        /// Constructor to inject required dependencies.
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance to access repositories.</param>
        /// <param name="mapper">AutoMapper instance for mapping domain models to DTOs.</param>
        public GetGitPullRequestBuildTimeLineHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        /// <summary>
        /// Handles the GetGitPullRequestBuildTimeLineQuery request.
        /// Retrieves the build timeline information and maps it to a DTO.
        /// </summary>
        /// <param name="request">The request object containing necessary parameters for fetching the build timeline.</param>
        /// <param name="cancellationToken">Token to monitor for cancellation requests.</param>
        /// <returns>A response containing the build timeline data wrapped in a BaseReponseGeneric DTO.</returns>
        public async Task<BaseReponseGeneric<TimeLineResponseDto>> Handle(GetGitPullRequestBuildTimeLineQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<TimeLineResponseDto>();

            try
            {
                // Call the repository to fetch the build timeline data using the provided parameters.
                var timeLineResponse = await _unitOfWork.GitPullRequestBuildTimeLineRepository
                    .GetGitPullRequestBuildTimeLineAsync(request.ProjectId, request.BuildId, request.PATToken);

                if (timeLineResponse is not null)
                {
                    // Map the retrieved domain entity (TimeLineResponse) to the corresponding DTO (TimeLineResponseDto)
                    response.Data = _mapper.Map<TimeLineResponseDto>(timeLineResponse);
                    response.Succcess = true;
                    response.Message = "Git Pull Request Build Timeline fetch succeeded!";
                }
                else
                {
                    response.Message = "No build timeline data found for the provided parameters.";
                }
            }
            catch (Exception ex)
            {
                // Capture and return any exceptions that occur during data retrieval
                response.Message = $"An error occurred: {ex.Message}";
            }

            return response;
        }
    }
}
