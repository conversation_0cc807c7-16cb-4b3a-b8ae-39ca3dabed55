﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using RIB.PRMetrics.Domain.Entities.JobStatus;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using RIB.PRMetrics.Domain.Entities;
using System.ComponentModel.DataAnnotations;
using RIB.PRMetrics.Application.Dto;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.TakeActionExcelReport
{
    public class TakeActionExcelReportCommand :  IRequest<BaseReponseGeneric<ProcessProgressDto>>
    {
        [Required(ErrorMessage = "uuid is required.")]
        [FromQuery(Name = "uuid")]
        public string Uuid { get; set; }

        [Required(ErrorMessage = "JobStatus is required.")]
        [FromQuery(Name = "jobStatus")]
        public string JobStatus { get; set; }
    }
}
