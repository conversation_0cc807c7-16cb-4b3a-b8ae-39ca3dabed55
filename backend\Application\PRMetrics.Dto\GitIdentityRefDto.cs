﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */
using Newtonsoft.Json;

namespace  RIB.PRMetrics.Application.Dto
{
    /// <summary>
    /// Git Identity Details
    /// </summary>
    public class GitIdentityRefDto
    {
        /// <summary>
        /// Identity Display Name
        /// </summary>
        
        public string DisplayName { get; set; }

        /// <summary>
        /// Identity Unique Name
        /// </summary>
        
        public string UniqueName { get; set; }

        /// <summary>
        /// Identity Id
        /// </summary>
        
        public string Id { get; set; }

        /// <summary>
        /// Identity Image Url
        /// </summary>
        
        public string ImageUrl { get; set; }
    }

}
