﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities
{
    /// <summary>
    /// Represents a Git repository, including details like ID, name, project association, URLs, and its status.
    /// </summary>
    public class GitRepository
    {
        /// <summary>
        /// Gets or sets the unique identifier for the repository.
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the name of the repository.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the project to which the repository belongs.
        /// </summary>
        public GitProject Project { get; set; }

        /// <summary>
        /// Gets or sets the URL of the repository.
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        /// Gets or sets the default branch of the repository.
        /// </summary>
        public string DefaultBranch { get; set; }

        /// <summary>
        /// Gets or sets the SSH URL for cloning the repository.
        /// </summary>
        public string SshUrl { get; set; }

        /// <summary>
        /// Gets or sets the web URL of the repository.
        /// </summary>
        public string WebUrl { get; set; }

        /// <summary>
        /// Gets or sets the status of the repository (whether it is disabled or not).
        /// </summary>
        public bool IsDisabled { get; set; }
    }
}
