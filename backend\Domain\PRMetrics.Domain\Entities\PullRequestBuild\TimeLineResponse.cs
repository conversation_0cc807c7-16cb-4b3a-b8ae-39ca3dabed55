﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Newtonsoft.Json;

namespace RIB.PRMetrics.Domain.Entities.PullRequestBuild
{
    public class TimeLineResponse
    {
        /// <summary>
        /// A list of timeline items that represent individual records for a pull request build.
        /// </summary>
        [JsonProperty("records")]
        public List<TimeLineItem> Records { get; set; }

        /// <summary>
        /// The name of the person who last changed this timeline response.
        /// </summary>
        [JsonProperty("lastChangedBy")]
        public string LastChangedBy { get; set; }

        /// <summary>
        /// The timestamp of the last change to this timeline response.
        /// </summary>
        [JsonProperty("lastChangedOn")]
        public string LastChangedOn { get; set; }

        /// <summary>
        /// The unique identifier for this timeline response.
        /// </summary>
        [JsonProperty("id")]
        public string Id { get; set; }

        /// <summary>
        /// The change ID associated with the timeline response.
        /// </summary>
        [JsonProperty("changeId")]
        public double ChangeId { get; set; }

        /// <summary>
        /// The URL associated with this timeline response, likely a link to more detailed information.
        /// </summary>
        [JsonProperty("Url")]
        public string url { get; set; }
    }
}
