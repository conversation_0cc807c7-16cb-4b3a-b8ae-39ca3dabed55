﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Newtonsoft.Json;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Domain.Entities.IdentityPicker;
using RIB.PRMetrics.Persistence.Common;
using System.Net;

namespace RIB.PRMetrics.Persistence.Repositories
{
    public class IdentityPickerRepository : IIdentityPickerRepository
    {
        private readonly AzureDevopsUrlBuilder azureDevopsUrlBuilder;
        private readonly AZDevopsClientCommon aZDevopsClientCommon;

        /// <summary>
        /// Initializes an instance of the <see cref="IdentityPickerRepository"/> class.
        /// The constructor injects the necessary dependencies for interacting with Azure DevOps API.
        /// </summary>
        /// <param name="_azureDevopsUrlBuilder">An instance of <see cref="AzureDevopsUrlBuilder"/> used to build Azure DevOps API URLs.</param>
        /// <param name="httpClientFactory">A factory for creating HTTP client instances (not used here, but available for future extension).</param>
        /// <param name="aZDevopsClientCommon">An instance of <see cref="AZDevopsClientCommon"/> for sending HTTP requests to Azure DevOps API.</param>
        public IdentityPickerRepository(AzureDevopsUrlBuilder _azureDevopsUrlBuilder, IHttpClientFactory httpClientFactory, AZDevopsClientCommon _aZDevopsClientCommon)
        {
            azureDevopsUrlBuilder = _azureDevopsUrlBuilder ?? throw new ArgumentNullException(nameof(azureDevopsUrlBuilder));
            this.aZDevopsClientCommon = _aZDevopsClientCommon ?? throw new ArgumentNullException(nameof(aZDevopsClientCommon));
        }

        /// <summary>
        /// Retrieves a list of user identities from Azure DevOps using the identity picker API.
        /// </summary>
        /// <param name="userUuid">The UUID of the user to be queried (used as a search hint).</param>
        /// <param name="token">The Azure DevOps personal access token (PAT) used for authentication.</param>
        /// <returns>
        /// A list of <see cref="IdentitiesItem"/> representing the matched user identities, or an empty list if none are found.
        /// </returns>
        /// <exception cref="Exception">Thrown when the API call fails or returns an unsuccessful response.</exception>

        public async Task<List<IdentitiesItem>> GetIdentitiesAsync(string userUuid, string token)
        {
            // Build the API URL 
            var url = azureDevopsUrlBuilder.BuildIdentityPickerUri();

            // Prepare the data payload to send with the request
            var postData = new IdentitiesPayload()
            {
                IdentityTypes = new List<string>() { "user" },
                OperationScopes = new List<string>() { "ims", "source" },
                Options = new QueryOptions()
                {
                    MaxResults=40,
                    MinResults=5
                },
                Properties=new List<string> { "DisplayName", "Mail", "Active" },
                Query=userUuid,
                QueryTypeHint= "uid"

            };

            // Convert the payload to JSON format
            string jsonBody = JsonConvert.SerializeObject(postData);

            // Send the HTTP POST request to the Azure DevOps API
            var response = await aZDevopsClientCommon.PostAsync(url, token, jsonBody);

            // If the response is successful, parse the JSON response into the expected data structure
            if (response.IsSuccessStatusCode && response.StatusCode == HttpStatusCode.OK)
            {
                var jsonResponse = await response.Content.ReadAsStringAsync();
                
                var IdentityResponse = JsonConvert.DeserializeObject<IdentitiesResponse>(jsonResponse);
                return IdentityResponse.Results[0].Identities ?? new List<IdentitiesItem>();
            }

            // Throw an exception if the API request was unsuccessful
            throw new Exception($"Error retrieving Identity Overall Status: {response.ReasonPhrase}");
        }
    }
}
