﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;
using MediatR;
using RIB.PRMetrics.Application.Dto.PullRequestBuild;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequestBuildTimeLine.Queries.GetGitPullRequestBuildTimeLine
{
    /// <summary>
    /// Query object for retrieving the build timeline of a Git pull request.
    /// This is used in a MediatR-based CQRS setup to request build timeline data.
    /// </summary>
    public class GetGitPullRequestBuildTimeLineQuery : IRequest<BaseReponseGeneric<TimeLineResponseDto>>
    {
        /// <summary>
        /// Personal Access Token (PAT) used for authenticating the request.
        /// Required for accessing protected resources from services like Azure DevOps.
        /// </summary>
        [Required(ErrorMessage = "Token is required.")]
        [FromQuery(Name = "patToken")]
        public string PATToken { get; set; }

        /// <summary>
        /// The unique identifier of the project that contains the build.
        /// This is used to scope the request to the correct project.
        /// </summary>
        [Required(ErrorMessage = "Project Id is required.")]
        [FromQuery(Name = "projectId")]
        public string ProjectId { get; set; }

        /// <summary>
        /// The unique identifier of the build whose timeline is being requested.
        /// This value is essential to locate the specific build details.
        /// </summary>
        [Required(ErrorMessage = "Build Id is required.")]
        [FromQuery(Name = "buildId")]
        public string BuildId { get; set; }
    }
}
