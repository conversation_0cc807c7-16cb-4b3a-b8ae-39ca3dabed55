﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities
{
    /// <summary>
    /// Represents a list of Git repositories along with the count of repositories.
    /// </summary>
    public class GitRepositoryList
    {
        /// <summary>
        /// Gets or sets the count of repositories in the list.
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// Gets or sets the list of Git repositories.
        /// </summary>
        public List<GitRepository> Value { get; set; }
    }
}

