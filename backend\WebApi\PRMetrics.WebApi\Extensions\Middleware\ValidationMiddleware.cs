﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using System.Text.Json;
using   RIB.PRMetrics.Application.UseCases.Commons.Bases;
using   RIB.PRMetrics.Application.UseCases.Commons.Exceptions;


namespace  RIB.PRMetrics.WebApi.Extensions.Middleware
{
    /// <summary>
    /// Middleware for handling validation errors that occur during the processing of HTTP requests.
    /// </summary>
    public class ValidationMiddleware
    {
        private readonly RequestDelegate _next;

        /// <summary>
        /// Initializes a new instance of the <see cref="ValidationMiddleware"/> class.
        /// </summary>
        /// <param name="next">The next middleware in the request pipeline.</param>
        public ValidationMiddleware(RequestDelegate next)
        {
            _next = next ?? throw new ArgumentNullException(nameof(next));
        }

        /// <summary>
        /// Invokes the middleware, passing control to the next middleware in the pipeline.
        /// If a validation exception occurs, it handles the exception and serializes an error response.
        /// </summary>
        /// <param name="context">The HTTP context for the current request.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        public async Task Invoke(HttpContext context)
        {
            try
            {
                // Pass control to the next middleware in the pipeline.
                await _next.Invoke(context);
            }
            catch (ValidationExceptionCustom ex)
            {
                // Handle validation exceptions by serializing the error response.
                context.Response.ContentType = "application/json";
                await JsonSerializer.SerializeAsync(
                    context.Response.Body,
                    new BaseResponse<object>
                    {
                        Message = "Validation Errors",
                        Errors = ex.Errors // Serialize the validation errors to the response body
                    });
            }
        }
    }
}
