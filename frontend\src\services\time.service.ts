import { Injectable } from '@angular/core';
import { ConfigService } from './config.service';
import { calculateWorkingTime } from '../utils/date-utils';

/**
 * Specialized service for time-related calculations in the PR analytics application
 * Extracts time calculation logic from components for better maintainability
 */
@Injectable({
  providedIn: 'root'
})
export class TimeService {
  constructor(private configService: ConfigService) {}

  /**
   * Returns working hours as a number for a PR
   * @param pr The pull request object
   * @returns Number of working hours
   */
  getTimelapseHours(pr: any): number {
    if (!pr || !pr.creationDate) {
      console.warn('Invalid PR object or missing creationDate:', pr);
      return 0;
    }
    
    // Use the calculateWorkingTime utility from date-utils
    const result = calculateWorkingTime(
      pr.creationDate,
      pr.closedDate ?? new Date().toISOString()
      // this.configService - commented out as in original
    );
    
    // Try to parse totalWorkingHours if available, else fallback to 0
    const hours = result && typeof result.totalWorkingHours === 'number' ? result.totalWorkingHours : 0;
    
    return hours;
  }

  /**
   * Returns a human-readable timelapse string for a PR
   * @param pr The pull request object
   * @returns Formatted string with working time
   */
  getTimelapse(pr: any): any {
    return calculateWorkingTime(
      pr.creationDate,
      pr.closedDate ?? new Date().toISOString()
      // this.configService - commented out as in original
    ).totalWorkingHoursFormattedInWord;
  }
  
  /**
   * Calculate working time between two date strings
   * This is a wrapper for the calculateWorkingTime utility
   * @param startDateStr Start date string
   * @param endDateStr End date string (defaults to current time)
   * @returns The calculated working time object
   */
  calculateWorkingTimeBetween(startDateStr: string, endDateStr?: string): any {
    return calculateWorkingTime(
      startDateStr,
      endDateStr ?? new Date().toISOString()
    );
  }
  
  /**
   * Checks if a PR has exceeded a specified duration threshold
   * Useful for highlighting PRs that have been open too long
   * @param pr The pull request object
   * @param thresholdHours Number of hours that defines the threshold
   * @returns Boolean indicating if the PR exceeds the threshold
   */
  exceedsDurationThreshold(pr: any, thresholdHours: number): boolean {
    return this.getTimelapseHours(pr) > thresholdHours;
  }
}
