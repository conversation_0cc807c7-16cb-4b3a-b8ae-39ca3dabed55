﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto
{
    /// <summary>
    /// Data Transfer Object representing the count of Git pull requests filtered by project, repository, and status.
    /// </summary>
    public class GitPullRequestStatusCountDto
    {
        /// <summary>
        /// Gets or sets the name of the project.
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// Gets or sets the name of the repository.
        /// </summary>
        public string RepositoryName { get; set; }

        /// <summary>
        /// Gets or sets the status of the pull request (e.g., active, completed).
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Gets or sets the count of pull requests matching the criteria.
        /// </summary>
        public uint Value { get; set; }
    }
}
