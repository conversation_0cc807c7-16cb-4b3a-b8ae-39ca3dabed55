﻿using AutoMapper;
using MediatR;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.TakeActionExcelReport
{
    public class TakeActionExcelReportCommandHandler : IRequestHandler<TakeActionExcelReportCommand, BaseReponseGeneric<ProcessProgressDto>>
    {
        private readonly IUnitOfWork _unitOfWork;

        private readonly IMapper _mapper;

        public TakeActionExcelReportCommandHandler(IUnitOfWork unitOfWork,IMapper mapper)
        {
            _unitOfWork= unitOfWork;
            _mapper= mapper;
        }

        public async Task<BaseReponseGeneric<ProcessProgressDto>> Handle(TakeActionExcelReportCommand request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<ProcessProgressDto>();

            try
            {

                var excelExportReport = await _unitOfWork.GitPullRequestsRepository.TakeActionOnExcelReportProgressAsync(request.Uuid,request.JobStatus);

                if (excelExportReport is not null)
                {
                    response.Data = _mapper.Map<ProcessProgressDto>(excelExportReport);
                    response.Succcess = true;
                    response.Message = $"Excel Export File {request.JobStatus} successfully.";
                }
                else
                {
                    response.Succcess = false;
                    response.Message = "Excel Export File not found.";
                }
            }
            catch (Exception ex)
            {
                response.Succcess = false;
                response.Message = $"Error occurred: {ex.Message}";
            }

            return response;
        }
    }
}
