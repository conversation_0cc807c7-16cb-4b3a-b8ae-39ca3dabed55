﻿using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Domain.Entities;
using System.Collections.Concurrent;

namespace RIB.PRMetrics.Infrastructure.Services
{
    public class InMemoryJobQueue : IJobQueue
    {
        private readonly ConcurrentQueue<ExportJob> _jobs = new();

        public void Enqueue(ExportJob job) => _jobs.Enqueue(job);

        public bool TryDequeue(out ExportJob job) => _jobs.TryDequeue(out job);
    }
}
