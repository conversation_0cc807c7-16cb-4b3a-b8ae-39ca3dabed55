{"version": 3, "file": "Loggers.js", "sourceRoot": "", "sources": ["../../src/Loggers.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE;;;AAIvE,mEAAmE;AACnE,MAAa,UAAU;IAInB,gBAAuB,CAAC;IAExB,kBAAkB;IAClB,2BAA2B;IACpB,GAAG,CAAC,SAAmB,EAAE,QAAgB;IAChD,CAAC;;AATL,gCAUC;AATG,2EAA2E;AAC7D,mBAAQ,GAAY,IAAI,UAAU,EAAE,CAAC", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\n\r\n/** A logger that does nothing when log messages are sent to it. */\r\nexport class NullLogger implements ILogger {\r\n    /** The singleton instance of the {@link @microsoft/signalr.NullLogger}. */\r\n    public static instance: ILogger = new NullLogger();\r\n\r\n    private constructor() {}\r\n\r\n    /** @inheritDoc */\r\n    // eslint-disable-next-line\r\n    public log(_logLevel: LogLevel, _message: string): void {\r\n    }\r\n}\r\n"]}