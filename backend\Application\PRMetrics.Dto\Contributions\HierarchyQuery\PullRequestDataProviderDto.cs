﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto.Contributions.HierarchyQuery
{
    /// <summary>
    /// Represents a data provider for pull request details in the hierarchy query.
    /// This DTO includes lists of policies and statuses associated with the pull request.
    /// </summary>
    public class PullRequestDataProviderDto
    {
        /// <summary>
        /// Gets or sets the list of policies associated with the pull request.
        /// Policies represent the rules or conditions that the pull request must comply with.
        /// </summary>
        public List<PolicyDto> Policies { get; set; }

        /// <summary>
        /// Gets or sets the list of statuses related to the pull request.
        /// The statuses may indicate various conditions such as approval status, build status, etc.
        /// </summary>
        public List<object> Statuses { get; set; }
    }
}
