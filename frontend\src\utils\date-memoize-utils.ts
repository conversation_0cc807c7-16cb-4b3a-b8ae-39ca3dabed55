// Utility functions for date/time calculations with memoization for performance

import { calculateWorkingTime } from "./date-utils";

// Memoization cache for parsed dates
const dateCache = new Map<string, Date>();

/**
 * Parse date string with memoization for performance
 * @param dateStr The date string to parse
 * @returns A Date object
 */
export function parseDateMemoized(dateStr: string): Date {
  if (!dateStr) {
    return new Date();
  }
  
  if (dateCache.has(dateStr)) {
    return dateCache.get(dateStr)!;
  }
  
  const date = new Date(dateStr);
  dateCache.set(dateStr, date);
  return date;
}

/**
 * Calculate duration between two dates in milliseconds
 * @param startDateStr Start date string
 * @param endDateStr End date string (defaults to current time)
 * @returns Duration in milliseconds
 */
export function calculateDurationMs(startDateStr: string, endDateStr?: string): number {
  const startDate = parseDateMemoized(startDateStr);
  const endDate = endDateStr ? parseDateMemoized(endDateStr) : new Date();
  
  return Math.max(0, endDate.getTime() - startDate.getTime());
}

/**
 * Calculate duration between two dates in hours
 * @param startDateStr Start date string
 * @param endDateStr End date string (defaults to current time)
 * @returns Duration in hours as a number
 */
export function calculateDurationHours(startDateStr: string, endDateStr?: string): number {
  return calculateDurationMs(startDateStr, endDateStr) / (1000 * 60 * 60);
}

/**
 * Format a duration in milliseconds to a human-readable string
 * @param durationMs Duration in milliseconds
 * @returns Human-readable duration string
 */
export function formatDuration(durationMs: number): string {
  if (durationMs <= 0) {
    return 'Less than a minute';
  }

  const hours = Math.floor(durationMs / (1000 * 60 * 60));
  const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
  
  let result = '';
  if (hours > 0) {
    result += `${hours} ${hours === 1 ? 'hour' : 'hours'}`;
  }

  if (minutes > 0) {
    if (result) result += ' ';
    result += `${minutes} ${minutes === 1 ? 'minute' : 'minutes'}`;
  }

  return result || 'Less than a minute';
}

/**
 * Calculate and format the draft vs published time for a PR
 * @param stateEvents Array of state change events 
 * @param creationDate PR creation date
 * @param currentState Current PR state
 * @returns Object with draft and published durations
 */
export function calculateDraftPublishedTime(stateEvents: any[], creationDateStr: string, currentState: string = 'published'): { 
  totalDraftHours: any, 
  totalPublishedHours: any,
  totalDraftFormatted: any,
  totalPublishedFormatted: any
} {
  if (!stateEvents || !stateEvents.length) {
    // If no state events, assume the entire time was in the current state
    const totalHours = calculateWorkingTime(creationDateStr, new Date().toISOString()).totalWorkingHoursFormattedInWord;
    const totalMs = calculateWorkingTime(creationDateStr, new Date().toISOString()).totalWorkingHoursFormattedInWord;
    
    if (currentState === 'draft') {
      return { 
        totalDraftHours: totalHours,
        totalPublishedHours: 0,
        totalDraftFormatted: totalMs,
        totalPublishedFormatted: 'None' 
      };
    } else {
      return { 
        totalDraftHours: 0, 
        totalPublishedHours: totalHours,
        totalDraftFormatted: 'None',
        totalPublishedFormatted: totalMs
      };
    }
  }

  let draftMs = 0;
  let publishedMs = 0;
  let lastEventDate = parseDateMemoized(creationDateStr);
  let lastState = currentState === 'draft' ? 'published' : 'draft'; // Assume opposite of current to calculate first interval

  // Sort events by date
  const sortedEvents = [...stateEvents].sort((a, b) => 
    parseDateMemoized(a.date).getTime() - parseDateMemoized(b.date).getTime()
  );

  // Process each state change
  sortedEvents.forEach(event => {
    const eventDate = parseDateMemoized(event.date);
    const duration = eventDate.getTime() - lastEventDate.getTime();
    
    if (duration > 0) {
      if (lastState === 'draft') {
        draftMs += duration;
      } else {
        publishedMs += duration;
      }
    }
    
    lastEventDate = eventDate;
    lastState = event.state;
  });

  // Add time from last event to now
  const now = new Date();
  const finalDuration = now.getTime() - lastEventDate.getTime();
  
  if (finalDuration > 0) {
    if (lastState === 'draft') {
      draftMs += finalDuration;
    } else {
      publishedMs += finalDuration;
    }
  }

  return {
    totalDraftHours: draftMs / (1000 * 60 * 60),
    totalPublishedHours: publishedMs / (1000 * 60 * 60),
    totalDraftFormatted: formatDuration(draftMs),
    totalPublishedFormatted: formatDuration(publishedMs)
  };
}

/**
 * Calculate the durations for comment threads
 * @param thread The comment thread object
 * @param prCreationDateStr PR creation date string
 * @returns Object with calculated durations
 */
export function calculateCommentThreadDurations(thread: any, prCreationDateStr: string): {
  activeDuration: number, 
  activeDurationFormatted: string,
  fixedDuration?: number,
  fixedDurationFormatted?: string,
  hoursSincePRCreation: number,
  hoursSincePRCreationFormatted: string
} {
  const publishedDate = thread.publishedDate;
  const lastUpdatedDate = thread.lastUpdatedDate;
  const status = thread.status?.toLowerCase();
  
  // Time from thread creation to PR creation
  const hoursSincePRCreation = calculateDurationHours(prCreationDateStr, publishedDate);
  const hoursSincePRCreationMs = calculateDurationMs(prCreationDateStr, publishedDate);
  
  // For fixed threads, calculate how long it took to fix
  if (status === 'fixed') {
    const fixedDuration = calculateDurationHours(publishedDate, lastUpdatedDate);
    const fixedDurationMs = calculateDurationMs(publishedDate, lastUpdatedDate);
    
    return {
      activeDuration: 0, // Not active anymore
      activeDurationFormatted: 'None',
      fixedDuration,
      fixedDurationFormatted: formatDuration(fixedDurationMs),
      hoursSincePRCreation,
      hoursSincePRCreationFormatted: formatDuration(hoursSincePRCreationMs)
    };
  }
  
  // For active threads, calculate how long it has been active
  const activeDuration = calculateDurationHours(publishedDate);
  const activeDurationMs = calculateDurationMs(publishedDate);
  
  return {
    activeDuration,
    activeDurationFormatted: formatDuration(activeDurationMs),
    hoursSincePRCreation,
    hoursSincePRCreationFormatted: formatDuration(hoursSincePRCreationMs)
  };
}

/**
 * Enhance comments with calculated active duration and response time
 * @param comments The array of comments
 * @returns Comments with calculated durations
 */
export function enhanceCommentsWithDurations(comments: any[]): any[] {
  if (!comments || !comments.length) {
    return [];
  }

  const enhanced = [...comments];

  for (let i = 0; i < enhanced.length; i++) {
    const comment = enhanced[i];
    
    // For the first comment, there's no response time
    if (i === 0) {
      if (enhanced.length > 1) {
        // Calculate time to first response if there are further comments
        const nextComment = enhanced[1];
        const responseTime = calculateDurationMs(comment.publishedDate, nextComment.publishedDate);
        comment.responseTime = responseTime;
        comment.responseTimeFormatted = formatDuration(responseTime);
      }
      continue;
    }
    
    // For subsequent comments, calculate response time from previous comment
    const prevComment = enhanced[i - 1];
    const responseTime = calculateDurationMs(prevComment.publishedDate, comment.publishedDate);
    comment.responseTime = responseTime;
    comment.responseTimeFormatted = formatDuration(responseTime);
    
    // For active comments (last in thread), calculate active duration
    if (i === enhanced.length - 1) {
      const activeTime = calculateDurationMs(comment.publishedDate);
      comment.activeTime = activeTime;
      comment.activeTimeFormatted = formatDuration(activeTime);
    }
  }

  return enhanced;
}
