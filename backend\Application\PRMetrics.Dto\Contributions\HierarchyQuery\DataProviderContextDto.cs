﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Newtonsoft.Json;

namespace RIB.PRMetrics.Application.Dto.Contributions.HierarchyQuery
{
    /// <summary>
    /// Represents the context data passed to a data provider in the hierarchy query.
    /// This class typically wraps configuration or filter information under a 'properties' node.
    /// </summary>
    public class DataProviderContextDto
    {
        /// <summary>
        /// Gets or sets the properties used by the data provider.
        /// These may include custom parameters or metadata required during the query.
        /// </summary>
        
        public DataProviderContextPropertiesDto Properties { get; set; }
    }
}
