<div class="app-container">
  <main class="login-main">
    <div class="login-dialog">
      <div class="login-content">
        <!-- Logo and Title -->
        <div class="login-header">
          <div class="logo-container">
            <img src="assets/rib-logo-text.svg" alt="RIB Logo" class="rib-logo">
            <div class="version"><span style="font-weight: 100;">|</span> 4.0</div>
          </div>
        </div>

        <!-- Login Form -->
        <div class="login-form">
          <!-- Username/Token Input with password toggle and horizontal scroll -->
          <mat-form-field appearance="outline" class="form-input full-width">
            <mat-label>&nbsp; Azure Personal Access Token </mat-label>
            <input matInput [type]="hideToken ? 'password' : 'text'" [(ngModel)]="patToken"
              placeholder="&nbsp; Azure DevOps Personal Access Token" [disabled]="!tokenError && projects.length > 0"
              class="pat-input" />
            <button mat-icon-button matSuffix (click)="hideToken = !hideToken"
              [attr.aria-label]="'Toggle token visibility'" type="button">
              <mat-icon>{{hideToken ? 'visibility_off' : 'visibility'}}</mat-icon>
            </button>
          </mat-form-field>

          <div *ngIf="tokenError" class="error-message">
            <mat-icon>error_outline</mat-icon> &nbsp;Invalid credentials. Please try again.
          </div>

          <!-- Login Button -->
          <button *ngIf="tokenError || projects.length === 0" mat-flat-button color="primary" class="login-button"
            (click)="validateToken()">
            Validate Token
          </button> <!-- Project Dropdown (Only shown after successful login) -->
          <mat-form-field *ngIf="!tokenError && projects.length" appearance="outline"
            class="form-input full-width project-field">
            <mat-label>Project</mat-label>
            <mat-select [(ngModel)]="selectedProject" (selectionChange)="loadRepos()" class="project-select">
              <mat-option *ngFor="let project of projects" [value]="project.name">
                {{ project.name }}
              </mat-option>
            </mat-select>
          </mat-form-field> <!-- Repo Dropdown -->
          <mat-form-field *ngIf="repositories.length" appearance="outline" class="form-input full-width repo-field">
            <mat-label>Repository</mat-label>
            <mat-select [(ngModel)]="selectedRepo" class="repo-select">
              <mat-option *ngFor="let repo of repositories" [value]="repo.name">
                {{ repo.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Action Buttons -->
          <div *ngIf="selectedRepo" class="button-group">
            <button mat-stroked-button color="warn" (click)="cancel()">
              Cancel
            </button>
            <button mat-raised-button color="primary" class="action-button get-data-button" (click)="getData()">
              <mat-icon>cloud_download</mat-icon>
              <span>Get Data</span>
            </button>
          </div>
        </div>

        <!-- Footer -->
        <div class="login-footer">
          <span>v1.0.0.0 / 2025</span>
        </div>
      </div>
    </div>
  </main>
</div>