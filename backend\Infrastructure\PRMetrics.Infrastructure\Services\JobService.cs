﻿using ClosedXML.Excel;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Caching.Memory;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Domain;
using RIB.PRMetrics.Domain.Entities;
using RIB.PRMetrics.Domain.Entities.JobStatus;
using RIB.PRMetrics.Infrastructure.SignalR;
using System.Collections.Concurrent;

namespace RIB.PRMetrics.Infrastructure.Services
{
    public class JobService : IJobService
    {
        private readonly IMemoryCache _cache;
        private readonly IHubContext<ProgressHub> _hub;
        private readonly IGitPullRequestRepository _gitPullRequestRepository;
        private readonly IGitPullRequestThreadsRepository _gitPullRequestThreadsRepository;
        private readonly IContributionsHierarchyQueryRepository _contributionsHierarchyQueryRepository;
        private readonly IGitPullRequestBuildTimeLineRepository _gitPullRequestBuildTimeLineRepository;
        private readonly IDeleteJobQueue _deleteJobQueue;
        private readonly IJobTracker _jobTracker;

        public JobService(
            IMemoryCache cache,
            IHubContext<ProgressHub> hub,
            IGitPullRequestRepository gitPullRequestRepository,
            IGitPullRequestThreadsRepository gitPullRequestThreadsRepository,
            IContributionsHierarchyQueryRepository contributionsHierarchyQueryRepository,
            IGitPullRequestBuildTimeLineRepository gitPullRequestBuildTimeLineRepository,
            IDeleteJobQueue deleteJobQueue,
            IJobTracker jobTracker)
        {
            _cache = cache;
            _hub = hub;
            _gitPullRequestRepository = gitPullRequestRepository;
            _gitPullRequestThreadsRepository = gitPullRequestThreadsRepository;
            _contributionsHierarchyQueryRepository = contributionsHierarchyQueryRepository;
            _gitPullRequestBuildTimeLineRepository = gitPullRequestBuildTimeLineRepository;
            _deleteJobQueue = deleteJobQueue;
            _jobTracker = jobTracker;
        }

        private void UpdateProgress(ExportJob job, int percent, string status, string jobStatus, string fileUrl = "")
        {
            var progress = new ProcessProgress
            {
                Uuid = job.Uuid,
                Percentage = percent,
                Status = status,
                FileName = job.FileName,
                JobStatus= jobStatus,
                FileUrl = fileUrl
            };

            _cache.Set(job.Uuid.ToString(), progress);
            progress.FileUrl = "";
            _hub.Clients.All.SendAsync("ReceiveProgress", progress);
        }

        public async Task ExecuteAsync(ExportJob job)
        {
            job.Status = JobStatus.Running;
            _jobTracker.Add(job);
            UpdateProgress(job, 0, "Started", JobStatus.Pending);
            await PreparedReportDataAsync(job);
        }

        private async Task PreparedReportDataAsync(ExportJob job)
        {
            var gitCommon = new GitCommon
            {
                PATToken = job.pullRequestSearchCriteria.PATToken,
                Project = job.pullRequestSearchCriteria.Project,
                Repositories = job.pullRequestSearchCriteria.Repositories,
            };

            // Check for cancel/pause before starting
            CheckPauseCancel(job);

            UpdateProgress(job, 4, "Prepare Payload", JobStatus.Running);

            // Check for cancel/pause before starting
            CheckPauseCancel(job);

            var prList = await _gitPullRequestRepository.GetGitPullRequestsReportAsync(job.pullRequestSearchCriteria, (percent, status, jobStatus) => UpdateProgress(job, percent, status, jobStatus), () => CheckPauseCancel(job));

            // Check for cancel/pause before starting
            CheckPauseCancel(job);

            //UpdateProgress(job, 30, "Fetching Pull Requests", JobStatus.Running);

            // Check for cancel/pause before starting
            CheckPauseCancel(job);

            //var tasks = prList.Select(async pr =>
            //{
            //    // Check for cancel/pause before starting
            //    CheckPauseCancel(job);

            //    var threadTask = _gitPullRequestThreadsRepository.GetGitPullRequestThreadsAsync(gitCommon, pr.PullRequestId, pr.CreatedBy.Id);
            //    var contributionTask = _contributionsHierarchyQueryRepository.GetGitPullRequestContributionsHierarchyQueryAsync(pr.PullRequestId.ToString(), pr.Repository.Id, pr.Repository.Project.Id, gitCommon.PATToken);

            //    await Task.WhenAll(threadTask, contributionTask);

            //    var thread = await threadTask;
            //    var contribution = await contributionTask;

            //    var policy = contribution?.DataProviders?.prDetailDataProvider?.Policies?
            //        .FirstOrDefault(x => !string.IsNullOrEmpty(x.DisplayName) && x.DisplayName.Contains("CI must pass"));

            //    return new ExcelReport
            //    {
            //        PullRequestId = pr.PullRequestId,
            //        Title = pr.Title,
            //        Status = pr.Status,
            //        Author = pr.CreatedBy.DisplayName,
            //        SourceBranch = pr.SourceRefName.Replace("refs/heads/",""),
            //        Reviewer = string.Join(", ", pr.Reviewers.Select(x => x.DisplayName.Replace("[itwo40]\\",""))),
            //        CreationDate = pr.CreationDate,
            //        ActiveComment = thread.commentThread.CommentThreads.Count(x => x.Status == "active"),
            //        ResolvedComment = thread.commentThread.CommentThreads.Count(x => x.Status == "fixed"),
            //        TotalComment = thread.commentThread.CommentThreads.Count(x => x.Status == "active" || x.Status == "fixed"),
            //        PipelineStatus = policy?.DisplayText,
            //        PipelineDetails = policy != null ? $"https://dev.azure.com/ribdev/{pr.Repository.Project.Name}/_build/results?buildId={policy.BuildId}&view=results" : null
            //    };
            //});

            //// Check for cancel/pause before starting
            //CheckPauseCancel(job);

            //var mainReportData = (await Task.WhenAll(tasks)).ToList();

            var mainReportData = new ConcurrentBag<ExcelReport>();
            int total = prList.Count;
            int batchSize = 5; // Tune this based on performance

            for (int i = 0; i < total; i += batchSize)
            {
                CheckPauseCancel(job);

                var batch = prList.Skip(i).Take(batchSize).ToList();
                var tasks = batch.Select(async (pr, indexInBatch) =>
                {
                    CheckPauseCancel(job);

                    var threadTask = _gitPullRequestThreadsRepository.GetGitPullRequestThreadsAsync(gitCommon, pr.PullRequestId, pr.CreatedBy.Id);
                    var contributionTask = _contributionsHierarchyQueryRepository.GetGitPullRequestContributionsHierarchyQueryAsync(
                        pr.PullRequestId.ToString(),
                        pr.Repository.Id,
                        pr.Repository.Project.Id,
                        gitCommon.PATToken
                    );

                    await Task.WhenAll(threadTask, contributionTask);

                    var thread = await threadTask;
                    var contribution = await contributionTask;

                    var policy = contribution?.DataProviders?.prDetailDataProvider?.Policies?
                        .FirstOrDefault(x => !string.IsNullOrEmpty(x.DisplayName) && x.DisplayName.Contains("CI must pass"));

                    mainReportData.Add(new ExcelReport
                    {
                        PullRequestId = pr.PullRequestId,
                        Title = pr.Title,
                        Status = pr.Status,
                        Author = pr.CreatedBy.DisplayName,
                        SourceBranch = pr.SourceRefName.Replace("refs/heads/", ""),
                        Reviewer = string.Join(", ", pr.Reviewers.Select(x => x.DisplayName.Replace("[itwo40]\\", ""))),
                        CreationDate = pr.CreationDate,
                        ActiveComment = thread.commentThread.CommentThreads.Count(x => x.Status == "active"),
                        ResolvedComment = thread.commentThread.CommentThreads.Count(x => x.Status == "fixed"),
                        TotalComment = thread.commentThread.CommentThreads.Count(x => x.Status == "active" || x.Status == "fixed"),
                        PipelineStatus = policy?.DisplayText,
                        PipelineDetails = policy != null ? $"https://dev.azure.com/ribdev/{pr.Repository.Project.Name}/_build/results?buildId={policy.BuildId}&view=results" : null
                    });

                    // Progress update per PR
                    var current = Math.Min(i + indexInBatch + 1, total);
                    int progress = 30 + (current * 20 / total); // progress between 30–50
                    UpdateProgress(job, progress, "Processing Pull Requests", job.Status);
                });

                await Task.WhenAll(tasks);
            }

            UpdateProgress(job, 50, "Prepare Excel Report Data", JobStatus.Running);
            await ExportToExcelAsync(job, mainReportData.ToList());

            // Check for cancel/pause before starting
            CheckPauseCancel(job);
        }

        public async Task ExportToExcelAsync(ExportJob job, IEnumerable<ExcelReport> reportdata)
        {
            // Check for cancel/pause before starting
            CheckPauseCancel(job);

            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add($"PR_Metrics_{DateTime.Now:dd_MM_yyyy_HH_mm_ss}");
            UpdateProgress(job, 55, "Generating Worksheet", JobStatus.Running);

            string[] headers = new[]
            {
                "Sr.No.", "Pull Request Id", "Title", "Created Date", "Active Comment", "Resolved Comment",
                "Total Comment", "Status", "Author", "Reviewer", "Source Branch", "Pipeline Status", "Pipeline Details"
            };

            // Check for cancel/pause before starting
            CheckPauseCancel(job);

            for (int i = 0; i < headers.Length; i++)
                worksheet.Cell(1, i + 1).Value = headers[i];

            for (int i = 0; i < reportdata.Count(); i++)
            {
                // Check for cancel/pause before starting
                CheckPauseCancel(job);

                var report = reportdata.ElementAt(i);
                worksheet.Cell(i + 2, 1).Value = i + 1;
                worksheet.Cell(i + 2, 2).Value = report.PullRequestId;
                worksheet.Cell(i + 2, 3).Value = report.Title;
                worksheet.Cell(i + 2, 4).Value = report.CreationDate;
                worksheet.Cell(i + 2, 5).Value = report.ActiveComment;
                worksheet.Cell(i + 2, 6).Value = report.ResolvedComment;
                worksheet.Cell(i + 2, 7).Value = report.TotalComment;
                worksheet.Cell(i + 2, 8).Value = report.Status;
                worksheet.Cell(i + 2, 9).Value = report.Author;
                worksheet.Cell(i + 2, 10).Value = report.Reviewer;
                worksheet.Cell(i + 2, 11).Value = report.SourceBranch;
                worksheet.Cell(i + 2, 12).Value = report.PipelineStatus;
                worksheet.Cell(i + 2, 13).Value = report.PipelineDetails;

                // Throttle progress updates
                if ((i + 1) % (reportdata.Count() / 4) == 0)
                    UpdateProgress(job, 60 + ((i + 1) * 30 / reportdata.Count()), "Writing Excel Rows", JobStatus.Running);
            }

            worksheet.RangeUsed().CreateTable();

            // Check for cancel/pause before starting
            CheckPauseCancel(job);

            var tempFolder = Path.Combine(Directory.GetCurrentDirectory(), "TempFiles");
            if (!Directory.Exists(tempFolder)) Directory.CreateDirectory(tempFolder);
            var filePath = Path.Combine(tempFolder, $"{job.FileName}");

            workbook.SaveAs(filePath);

            UpdateProgress(job, 90, "Excel Generated", JobStatus.Running, filePath);
            UpdateProgress(job, 100, "Completed", JobStatus.Completed, filePath);
            _deleteJobQueue.Schedule(new ProcessProgress
            {
                Uuid = job.Uuid,
                FileName = job.FileName,
                FileUrl= filePath
            }, TimeSpan.FromDays(1));
        }
        private void CheckPauseCancel(ExportJob job)
        {
            job.TokenSource.Token.ThrowIfCancellationRequested();
            job.PauseHandle.Wait();
        }
    }


}
