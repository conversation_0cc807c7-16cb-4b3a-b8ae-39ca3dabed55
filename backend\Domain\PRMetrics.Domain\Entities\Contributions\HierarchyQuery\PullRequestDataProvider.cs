﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Newtonsoft.Json;

namespace RIB.PRMetrics.Domain.Entities.Contributions.HierarchyQuery
{
    /// <summary>
    /// Represents the data provider for pull request, including its associated policies and statuses.
    /// </summary>
    public class PullRequestDataProvider
    {
        /// <summary>
        /// Gets or sets the list of policies associated with the pull request.
        /// </summary>
        [JsonProperty("policies")]
        public List<Policy> Policies { get; set; }

        /// <summary>
        /// Gets or sets the list of statuses related to the pull request.
        /// The type of status is represented as an object, which can be further defined based on the business needs.
        /// </summary>
        [JsonProperty("statuses")]
        public List<object> Statuses { get; set; }
    }
}
