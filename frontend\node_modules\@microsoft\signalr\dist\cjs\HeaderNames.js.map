{"version": 3, "file": "HeaderNames.js", "sourceRoot": "", "sources": ["../../src/HeaderNames.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE;;;AAEvE,MAAsB,WAAW;;AAAjC,kCAGC;AAFmB,yBAAa,GAAG,eAAe,CAAC;AAChC,kBAAM,GAAG,QAAQ,CAAC", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nexport abstract class HeaderNames {\r\n    static readonly Authorization = \"Authorization\";\r\n    static readonly Cookie = \"<PERSON>ie\";\r\n}\r\n"]}