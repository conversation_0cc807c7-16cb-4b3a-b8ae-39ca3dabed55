﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities
{
    /// <summary>
    /// Get Git Pull Request details
    /// </summary>
    public class GitPullRequest
    {
        /// <summary>
        /// Gets or sets the unique identifier for the pull request.
        /// </summary>
        public int PullRequestId { get; set; }

        /// <summary>
        /// Gets or sets the title of the pull request.
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Gets or sets the status of the pull request (e.g., open, closed, merged).
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Gets or sets the description of the pull request.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the merge status of the pull request (e.g., merged, not merged).
        /// </summary>
        public string MergeStatus { get; set; }

        /// <summary>
        /// Gets or sets whether the pull request is a draft.
        /// </summary>
        public string isDraft { get; set; }

        /// <summary>
        /// Gets or sets the creation date of the pull request.
        /// </summary>
        public DateTime CreationDate { get; set; }

        /// <summary>
        /// Gets or sets the user who created the pull request.
        /// </summary>
        public User CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the list of reviewers for the pull request.
        /// </summary>
        public List<ReviewerDto> Reviewers { get; set; }

        /// <summary>
        /// Gets or sets the repository associated with the pull request.
        /// </summary>
        public GitRepository Repository { get; set; }

        /// <summary>
        /// Gets or sets the source reference name of the pull request (e.g., branch name).
        /// </summary>
        public string SourceRefName { get; set; }

        /// <summary>
        /// Gets or sets the target reference name of the pull request (e.g., base branch name).
        /// </summary>
        public string TargetRefName { get; set; }

        /// <summary>
        /// Gets or sets the delay index of the pull request.
        /// </summary>
        public string DelayIndex { get; set; }

    }
}
