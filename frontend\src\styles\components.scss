// Shared component styles
// Contains common variables, mixins, and styles used across components

// Component Variables
$badge-border-radius: 16px;
$card-spacing: 24px;
$comment-thread-bg: #f9fafb;
$timeline-color: #e0e0e0;
$active-color: #ff9800;
$fixed-color: #4caf50;
$closed-color: #f44336;
$wontfix-color: #9e9e9e;
$draft-color: #5c6bc0;
$published-color: #26a69a;

// Review cycle colors
$cycle-colors: (
  cycle-0: #4285f4, // Google blue
  cycle-1: #34a853, // Google green  
  cycle-2: #fbbc05, // Google yellow
  cycle-3: #ea4335  // Google red
);

// Common mixins
@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin status-badge($color) {
  background-color: rgba($color, 0.1);
  color: $color;

  .status-dot {
    background-color: $color;
  }
}

// Common card styles
@mixin card-base {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

// Common container styles
@mixin container-base {
  padding: 24px;
  margin: 0 auto;
}

// Status classes
.status-fixed {
  @include status-badge($fixed-color);
}

.status-active {
  @include status-badge(#0078d4);
}

.status-closed {
  @include status-badge($closed-color);
}

.status-wontfix {
  @include status-badge($wontfix-color);
}

.status-pending {
  @include status-badge(#2196f3);
}

// Common timeline styles
@mixin timeline-base {
  position: relative;
  padding-left: 24px;
  
  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 10px;
    height: 100%;
    width: 2px;
    background-color: $timeline-color;
  }
}

// Common responsive adjustments
@mixin responsive-mobile {
  @media (max-width: 768px) {
    @content;
  }
}

@mixin responsive-small {
  @media (max-width: 600px) {
    @content;
  }
}

// Section title styles
@mixin section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  margin-bottom: 16px;
  color: #333;
  
  mat-icon {
    color: var(--primary-color);
  }
}

// No content placeholder
@mixin no-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  color: var(--text-light);
  text-align: center;
  
  mat-icon {
    font-size: 36px;
    height: 36px;
    width: 36px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
}
