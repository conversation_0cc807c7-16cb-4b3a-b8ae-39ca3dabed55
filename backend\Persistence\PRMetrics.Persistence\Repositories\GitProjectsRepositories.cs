﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using System.Net;
using Newtonsoft.Json;
using   RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Domain.Entities;
using RIB.PRMetrics.Persistence.Common;

namespace  RIB.PRMetrics.Persistence.Repositories
{
    /// <summary>
    /// This class handles the operations for retrieving Git project data from Azure DevOps using the Azure DevOps API.
    /// It provides methods to get a list of Git projects as well as details about a specific Git project.
    /// </summary>
    public class GitProjectRepositories : IGitProjectRepository
    {
        private readonly AzureDevopsUrlBuilder azureDevopsUrlBuilder;
        private readonly AZDevopsClientCommon aZDevopsClientCommon;

        /// <summary>
        /// Initializes an instance of the <see cref="GitProjectRepositories"/> class.
        /// The constructor takes dependencies for URL building and client communication with Azure DevOps.
        /// </summary>
        /// <param name="_azureDevopsUrlBuilder">An instance of <see cref="AzureDevopsUrlBuilder"/> to build URLs for Azure DevOps API requests.</param>
        /// <param name="httpClientFactory">A factory for creating HTTP client instances.</param>
        /// <param name="_aZDevopsClientCommon">An instance of <see cref="AZDevopsClientCommon"/> for handling Azure DevOps client communication.</param>
        /// <exception cref="ArgumentNullException">Thrown when <paramref name="_azureDevopsUrlBuilder"/> or <paramref name="_aZDevopsClientCommon"/> is null.</exception>
        public GitProjectRepositories(AzureDevopsUrlBuilder _azureDevopsUrlBuilder, IHttpClientFactory httpClientFactory, AZDevopsClientCommon _aZDevopsClientCommon)
        {
            azureDevopsUrlBuilder = _azureDevopsUrlBuilder ?? throw new ArgumentNullException(nameof(azureDevopsUrlBuilder));
            aZDevopsClientCommon = _aZDevopsClientCommon ?? throw new ArgumentNullException(nameof(aZDevopsClientCommon));
        }

        /// <summary>
        /// Fetches the list of Git projects from Azure DevOps.
        /// It builds the request URL, sends the HTTP GET request, and deserializes the response into a list of <see cref="GitProject"/>.
        /// </summary>
        /// <param name="pATToken">The Personal Access Token (PAT) for authenticating with the Azure DevOps API.</param>
        /// <returns>A list of <see cref="GitProject"/> representing the Git projects from Azure DevOps.</returns>
        /// <exception cref="Exception">Thrown when the request fails or an error occurs while retrieving the Git projects.</exception>
        public async Task<List<GitProject>> GetGitProjectsAsync(string pATToken)
        {
            var url = azureDevopsUrlBuilder.BuildGitProjectUri(null);
            var response = await aZDevopsClientCommon.GetAsync(url, pATToken);

            if (response.IsSuccessStatusCode && response.StatusCode == HttpStatusCode.OK)
            {
                var jsonResponse = await response.Content.ReadAsStringAsync();
                var template = new
                {
                    count = 0,
                    Value = new List<GitProject>()
                };
                var pullRequestsResponse = JsonConvert.DeserializeAnonymousType(jsonResponse, template);
                return pullRequestsResponse?.Value ?? new List<GitProject>();
            }

            // Handle error by throwing an exception if the response is not successful
            throw new Exception($"Error retrieving Git projects: {response.ReasonPhrase}");
        }

        /// <summary>
        /// Fetches the details of a specific Git project from Azure DevOps.
        /// It builds the request URL using the project ID, sends the HTTP GET request, and deserializes the response into a <see cref="GitProject"/>.
        /// </summary>
        /// <param name="projectId">The ID of the Git project to retrieve.</param>
        /// <param name="pATToken">The Personal Access Token (PAT) for authenticating with the Azure DevOps API.</param>
        /// <returns>A <see cref="GitProject"/> object representing the details of the Git project.</returns>
        /// <exception cref="Exception">Thrown when the request fails or an error occurs while retrieving the Git project details.</exception>
        public async Task<GitProject> GetGitProjectDetailsAsync(string projectId, string pATToken)
        {
            var url = azureDevopsUrlBuilder.BuildGitProjectUri(projectId);
            var response = await aZDevopsClientCommon.GetAsync(url, pATToken);

            if (response.IsSuccessStatusCode && response.StatusCode == HttpStatusCode.OK)
            {
                var jsonResponse = await response.Content.ReadAsStringAsync();
                var pullRequestsResponse = JsonConvert.DeserializeObject<GitProject>(jsonResponse);
                return pullRequestsResponse ?? new GitProject();
            }

            // Handle error by throwing an exception if the response is not successful
            throw new Exception($"Error retrieving Git project details: {response.ReasonPhrase}");
        }
    }
}
