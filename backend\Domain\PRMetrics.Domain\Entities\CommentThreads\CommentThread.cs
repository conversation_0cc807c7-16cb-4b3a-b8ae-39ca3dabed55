﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities.CommentThreads
{
    /// <summary>
    /// Represents a thread of comments in a pull request. A comment thread contains multiple comments 
    /// that are related to a specific topic or review point within the pull request.
    /// </summary>
    public class CommentThread
    {
        /// <summary>
        /// Gets or sets the unique identifier of the comment thread.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the list of comments within this thread.
        /// These comments are part of the same conversation within the pull request.
        /// </summary>
        public List<Comment> Comments { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the comment thread was first published.
        /// This is the timestamp of when the first comment in the thread was made.
        /// </summary>
        public DateTime PublishedDate { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the comment thread was last updated.
        /// This is the timestamp of when the last comment in the thread was updated.
        /// </summary>
        public DateTime LastUpdatedDate { get; set; }

        /// <summary>
        /// Gets or sets the current status of the comment thread.
        /// This can indicate whether the thread is active, resolved, or other statuses.
        /// </summary>
        public string Status { get; set; }
    }
}
