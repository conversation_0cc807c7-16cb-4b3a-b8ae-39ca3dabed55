﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;
using MediatR;
using RIB.PRMetrics.Application.Dto.Contributions.HierarchyQuery;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;

namespace RIB.PRMetrics.Application.UseCases.ContributionsHierarchyQuery.Queries.GetContributionsHierarchy
{
    /// <summary>
    /// Query request to retrieve contributions hierarchy data for a specific pull request.
    /// Implements MediatR's IRequest interface to enable CQRS-based query handling.
    /// </summary>
    public class GetContributionsHierarchyQuery : IRequest<BaseReponseGeneric<QueryResponseDto>>
    {
        /// <summary>
        /// Gets or sets the unique identifier for the pull request.
        /// Required for locating the specific pull request data.
        /// </summary>
        [Required(ErrorMessage = "PullRequestId is required.")]
        [FromQuery(Name = "pullRequestId")]
        public string PullRequestId { get; set; }

        /// <summary>
        /// Gets or sets the repository ID where the pull request is located.
        /// Required to correctly scope the pull request data.
        /// </summary>
        [Required(ErrorMessage = "RepositoryId is required.")]
        [FromQuery(Name = "repositoryId")]
        public string RepositoryId { get; set; }

        /// <summary>
        /// Gets or sets the project ID that contains the repository.
        /// This helps in further scoping the query to a specific project.
        /// </summary>
        [Required(ErrorMessage = "ProjectId is required.")]
        [FromQuery(Name = "projectId")]
        public string ProjectId { get; set; }

        /// <summary>
        /// Gets or sets the Personal Access Token (PAT) used for authentication.
        /// Required for making authenticated requests to services like Azure DevOps or GitHub.
        /// </summary>
        [Required(ErrorMessage = "PAT Token is required.")]
        [FromQuery(Name = "patToken")]
        public string PATToken { get; set; }
    }
}
