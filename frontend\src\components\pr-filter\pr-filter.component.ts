import { Component, EventEmitter, Input, Output, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';

/**
 * Reusable filter component for PR data tables
 * Extracted from pr-data.component for improved reusability
 */
@Component({
  selector: 'app-pr-filter',
  standalone: true,  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    MatSelectModule,
    MatTooltipModule,
    MatDatepickerModule,
    MatNativeDateModule
  ],
  templateUrl: './pr-filter.component.html',  styleUrl: './pr-filter.component.scss'
})
export class PrFilterComponent implements OnInit, OnDestroy {
  // Observable for search input changes
  private searchTerms = new Subject<string>();
  // Subject for handling component destruction
  private destroy$ = new Subject<void>();
  
  // Date range filter properties
  startDate: Date | null = null;
  endDate: Date | null = null;
  
  // Configurable debounce time in milliseconds
  @Input() debounceTime: number = 300;
  
  /**
   * List of status options for the status filter dropdown
   */
  @Input() statusOptions: Array<{value: string, label: string}> = [];
    /**
   * Current value of the status filter
   */
  @Input() statusFilter: string = 'all';
  
  /**
   * Placeholder text for search input field
   */
  @Input() searchPlaceholder: string = 'Enter PR ID';
  /**
   * Event emitted when status filter selection changes
   */
  @Output() statusFilterChange = new EventEmitter<string>();
  
  /**
   * Event emitted when search text changes
   */
  @Output() filterChange = new EventEmitter<Event>();
  
  /**
   * Event emitted when search is cleared
   */
  @Output() searchClear = new EventEmitter<HTMLInputElement>();
  
  /**
   * Event emitted when date range changes
   */
  @Output() dateRangeChange = new EventEmitter<{startDate: Date | null, endDate: Date | null}>();

  /**
   * Handle status filter selection change
   */
  onStatusFilterChange(): void {
    this.statusFilterChange.emit(this.statusFilter);
  }
  
  /**
   * Initialize the component
   */
  ngOnInit(): void {
    // Setup debounced search
    this.searchTerms.pipe(
      takeUntil(this.destroy$),
      debounceTime(this.debounceTime),
      distinctUntilChanged()
    ).subscribe(term => {
      // Create a synthetic event to emit
      const event = new CustomEvent('search', { detail: term });
      this.filterChange.emit(event as unknown as Event);
    });
  }
  
  /**
   * Clean up subscriptions when component is destroyed
   */
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
  
  /**
   * Handle search input change with debounce
   * @param event The keyboard event from the input field
   */
  onFilterChange(event: Event): void {
    const term = (event.target as HTMLInputElement).value;
    this.searchTerms.next(term);
    // Still emit the original event for immediate feedback
    this.filterChange.emit(event);
  }
    /**
   * Handle clearing of the search input
   * @param input The input element reference
   */
  onSearchClear(input: HTMLInputElement): void {
    this.searchClear.emit(input);
  }  /**
   * Handle date range changes and emit event to parent component
   * Emits when either start or end date is selected or changed
   */
  onDateRangeChange(): void {
    console.log('Date range changed in filter component:', 
      this.startDate ? this.startDate.toISOString() : 'null',
      this.endDate ? this.endDate.toISOString() : 'null');
      
    // Emit regardless of whether one or both dates are set
    // This ensures filtering works even with partial date ranges
    this.dateRangeChange.emit({
      startDate: this.startDate,
      endDate: this.endDate
    });
  }
  
  /**
   * Clear the date filter and emit event to notify parent component
   */
  clearDateFilter(): void {
    // Reset date values
    this.startDate = null;
    this.endDate = null;
    
    // Emit event with null dates to clear the filter
    this.dateRangeChange.emit({
      startDate: null,
      endDate: null
    });
    
    // Emit statusFilterChange to trigger data reload in the parent component
    this.statusFilterChange.emit(this.statusFilter);
  }
}
// Adding debug logs
