.dashboard-container {
  padding: 20px;
  
  .no-credentials {
    text-align: center;
    margin-top: 50px;
    
    p {
      font-size: 16px;
      color: #666;
    }
  }
  
  .dashboard-content {    .dashboard-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
      
      .action-buttons {
        display: flex;
        gap: 8px;
        align-items: center;
      }
    }
    
    .loading-spinner {
      display: flex;
      justify-content: center;
      margin: 50px 0;
    }
    
    .no-data {
      text-align: center;
      margin: 50px 0;
      
      p {
        font-size: 16px;
        color: #666;
      }
    }
      .charts-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
      gap: 20px;
      
      @media (max-width: 1024px) {
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      }
      
      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
      
      .chart-card, .stats-card {
        margin-bottom: 20px;
        height: 400px;
        
        mat-card-header {
          margin-bottom: 10px;
        }
        
        .chart-wrapper {
          height: 300px;
          position: relative;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          
          .no-data-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #666;
            font-style: italic;
            font-size: 14px;
            z-index: 10;
            background: rgba(255, 255, 255, 0.8);
            padding: 10px;
            border-radius: 4px;
          }
        }
        
        .debug-info {
          margin-top: 10px;
          background-color: #f8f9fa;
          border: 1px solid #ddd;
          border-radius: 4px;
          padding: 8px;
          font-size: 12px;
          
          p {
            font-weight: bold;
            margin: 0 0 5px 0;
            color: #e91e63;
          }
          
          ul {
            margin: 0;
            padding-left: 20px;
            
            li {
              margin-bottom: 2px;
              word-break: break-all;
            }
          }
        }
      }
      
      .stats-card {
        height: auto;
        
        .stats-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 20px;
          padding: 20px;
          
          .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            background-color: #f5f5f5;
            border-radius: 8px;
            
            .stat-value {
              font-size: 32px;
              font-weight: 500;
              color: #3f51b5;
            }
            
            .stat-label {
              font-size: 14px;
              color: #666;
              margin-top: 8px;
            }
          }
        }
      }
    }
  }
}
