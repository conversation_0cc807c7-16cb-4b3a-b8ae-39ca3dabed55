﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;

namespace  RIB.PRMetrics.Application.UseCases.GitProjets.Queries.GetGitProjectById
{
    /// <summary>
    /// Validator for GetGitProjectByIdQuery using FluentValidation.
    /// Ensures required fields are not null or empty.
    /// </summary>
    internal class GetGitProjectByIdValidator : AbstractValidator<GetGitProjectByIdQuery>
    {
        public GetGitProjectByIdValidator()
        {
            // Validate that the project ID is not null or empty
            RuleFor(x => x.Id)
               .NotEmpty().WithMessage("Project Id must not be empty.")
               .NotNull().WithMessage("Project Id is required.");

            // Validate that the PAT token is not null or empty
            RuleFor(x => x.PATToken)
               .NotEmpty().WithMessage("PAT Token must not be empty.")
               .NotNull().WithMessage("PAT Token is required.");
        }
    }
}
