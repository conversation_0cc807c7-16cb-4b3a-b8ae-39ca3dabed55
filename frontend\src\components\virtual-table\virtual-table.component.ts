import {
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectionStrategy,
  ViewChild,
  AfterViewInit,
  OnDestroy,
  ChangeDetectorRef
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { CdkVirtualScrollViewport, ScrollingModule } from '@angular/cdk/scrolling';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-virtual-table',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    ScrollingModule,
    MatTableModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="virtual-table-container">
      <cdk-virtual-scroll-viewport
        #viewport
        [itemSize]="itemSize"
        [minBufferPx]="minBufferPx"
        [maxBufferPx]="maxBufferPx"
        class="virtual-viewport"
        (scrolledIndexChange)="onScrolledIndexChange($event)">
        
        <div *cdkVirtualFor="let item of items; let i = index; trackBy: trackByFn"
             class="virtual-item"
             [class.loading]="item.loading"
             (click)="onItemClick(item, i)">
          <ng-container [ngTemplateOutlet]="itemTemplate" 
                       [ngTemplateOutletContext]="{ $implicit: item, index: i }">
          </ng-container>
        </div>

        <!-- Loading indicator for infinite scroll -->
        <div *ngIf="isLoadingMore" class="loading-indicator">
          <mat-spinner diameter="30"></mat-spinner>
          <span>Loading more items...</span>
        </div>
      </cdk-virtual-scroll-viewport>
    </div>
  `,
  styles: [`
    .virtual-table-container {
      height: 100%;
      width: 100%;
    }

    .virtual-viewport {
      height: 100%;
      width: 100%;
    }

    .virtual-item {
      border-bottom: 1px solid #e0e0e0;
      transition: background-color 0.2s ease;
      cursor: pointer;
    }

    .virtual-item:hover {
      background-color: #f5f5f5;
    }

    .virtual-item.loading {
      opacity: 0.6;
      pointer-events: none;
    }

    .loading-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      gap: 10px;
    }
  `]
})
export class VirtualTableComponent implements AfterViewInit, OnDestroy {
  @Input() items: any[] = [];
  @Input() itemSize = 60;
  @Input() minBufferPx = 200;
  @Input() maxBufferPx = 400;
  @Input() itemTemplate: any;
  @Input() trackByFn: (index: number, item: any) => any = (index, item) => item.id || index;
  @Input() isLoadingMore = false;
  @Input() hasMore = true;
  @Input() loadMoreThreshold = 5; // Load more when 5 items from bottom

  @Output() itemClick = new EventEmitter<{ item: any; index: number }>();
  @Output() loadMore = new EventEmitter<void>();
  @Output() scrolledIndexChange = new EventEmitter<number>();

  @ViewChild('viewport', { static: false }) viewport!: CdkVirtualScrollViewport;

  private destroy$ = new Subject<void>();
  private lastScrollIndex = 0;

  constructor(private cdr: ChangeDetectorRef) {}

  ngAfterViewInit() {
    if (this.viewport) {
      // Monitor scroll position for infinite loading
      this.viewport.scrolledIndexChange
        .pipe(takeUntil(this.destroy$))
        .subscribe(index => {
          this.onScrolledIndexChange(index);
        });
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onScrolledIndexChange(index: number) {
    this.lastScrollIndex = index;
    this.scrolledIndexChange.emit(index);

    // Check if we need to load more items
    if (this.shouldLoadMore(index)) {
      this.loadMore.emit();
    }
  }

  onItemClick(item: any, index: number) {
    this.itemClick.emit({ item, index });
  }

  private shouldLoadMore(currentIndex: number): boolean {
    if (!this.hasMore || this.isLoadingMore) {
      return false;
    }

    const totalItems = this.items.length;
    const remainingItems = totalItems - currentIndex;
    
    return remainingItems <= this.loadMoreThreshold;
  }

  /**
   * Scroll to a specific index
   */
  scrollToIndex(index: number) {
    if (this.viewport) {
      this.viewport.scrollToIndex(index);
    }
  }

  /**
   * Scroll to top
   */
  scrollToTop() {
    if (this.viewport) {
      this.viewport.scrollToIndex(0);
    }
  }

  /**
   * Get current scroll position
   */
  getScrollPosition(): number {
    return this.viewport?.measureScrollOffset() || 0;
  }

  /**
   * Get visible range
   */
  getVisibleRange(): { start: number; end: number } | null {
    if (!this.viewport) {
      return null;
    }

    const range = this.viewport.getRenderedRange();
    return {
      start: range.start,
      end: range.end
    };
  }

  /**
   * Force update the virtual scroll viewport
   */
  updateViewport() {
    if (this.viewport) {
      this.viewport.checkViewportSize();
      this.cdr.markForCheck();
    }
  }

  /**
   * Get viewport size
   */
  getViewportSize(): number {
    return this.viewport?.getViewportSize() || 0;
  }

  /**
   * Get data length
   */
  getDataLength(): number {
    return this.items.length;
  }
}
