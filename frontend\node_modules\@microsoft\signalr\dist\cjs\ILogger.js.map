{"version": 3, "file": "ILogger.js", "sourceRoot": "", "sources": ["../../src/ILogger.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE;;;AAEvE,2GAA2G;AAC3G;;;GAGG;AACH,IAAY,QAeX;AAfD,WAAY,QAAQ;IAChB,2DAA2D;IAC3D,yCAAS,CAAA;IACT,sDAAsD;IACtD,yCAAS,CAAA;IACT,uDAAuD;IACvD,qDAAe,CAAA;IACf,2EAA2E;IAC3E,6CAAW,CAAA;IACX,0FAA0F;IAC1F,yCAAS,CAAA;IACT,4GAA4G;IAC5G,+CAAY,CAAA;IACZ,wHAAwH;IACxH,uCAAQ,CAAA;AACZ,CAAC,EAfW,QAAQ,GAAR,gBAAQ,KAAR,gBAAQ,QAenB", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// These values are designed to match the ASP.NET Log Levels since that's the pattern we're emulating here.\r\n/** Indicates the severity of a log message.\r\n *\r\n * Log Levels are ordered in increasing severity. So `Debug` is more severe than `Trace`, etc.\r\n */\r\nexport enum LogLevel {\r\n    /** Log level for very low severity diagnostic messages. */\r\n    Trace = 0,\r\n    /** Log level for low severity diagnostic messages. */\r\n    Debug = 1,\r\n    /** Log level for informational diagnostic messages. */\r\n    Information = 2,\r\n    /** Log level for diagnostic messages that indicate a non-fatal problem. */\r\n    Warning = 3,\r\n    /** Log level for diagnostic messages that indicate a failure in the current operation. */\r\n    Error = 4,\r\n    /** Log level for diagnostic messages that indicate a failure that will terminate the entire application. */\r\n    Critical = 5,\r\n    /** The highest possible log level. Used when configuring logging to indicate that no log messages should be emitted. */\r\n    None = 6,\r\n}\r\n\r\n/** An abstraction that provides a sink for diagnostic messages. */\r\nexport interface ILogger {\r\n    /** Called by the framework to emit a diagnostic message.\r\n     *\r\n     * @param {LogLevel} logLevel The severity level of the message.\r\n     * @param {string} message The message.\r\n     */\r\n    log(logLevel: LogLevel, message: string): void;\r\n}\r\n"]}