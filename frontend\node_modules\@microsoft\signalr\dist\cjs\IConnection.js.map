{"version": 3, "file": "IConnection.js", "sourceRoot": "", "sources": ["../../src/IConnection.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { TransferFormat } from \"./ITransport\";\r\n\r\n/** @private */\r\nexport interface IConnection {\r\n    readonly features: any;\r\n    readonly connectionId?: string;\r\n\r\n    baseUrl: string;\r\n\r\n    start(transferFormat: TransferFormat): Promise<void>;\r\n    send(data: string | ArrayBuffer): Promise<void>;\r\n    stop(error?: Error | unknown): Promise<void>;\r\n\r\n    onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    onclose: ((error?: Error) => void) | null;\r\n}\r\n"]}