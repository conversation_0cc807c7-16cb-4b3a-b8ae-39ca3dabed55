import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, map, of } from 'rxjs';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Credentials } from '../models/pr-interfaces.model';
import { ApiUrlService } from './api-url.service';
import { ConfigService } from './config.service';
import { environment } from '../environments/environment'; // Added import

@Injectable({
  providedIn: 'root',
})
/**
 * Service to manage pull request data and credentials.
 */
export class PrDataService {
  private apiUrl = environment.apiUrl; // Changed to use environment.apiUrl

  private prDataSubject = new BehaviorSubject<any[]>([]);
  prData$ = this.prDataSubject.asObservable();

  private credentialsSubject = new BehaviorSubject<Credentials | null>(null);
  credentials$ = this.credentialsSubject.asObservable();

  // Store continuation tokens
  private continuationTokens = new Map<number, string>();

  // Get storage key from config service
  private readonly STORAGE_KEY: string;  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    // Initialize configuration values from config service
    this.STORAGE_KEY = this.configService.STORAGE_KEYS.CREDENTIALS;
    this.CACHE_EXPIRATION_MS = this.configService.CACHE_DURATION.PR_STATUS_COUNT;
    
    // Load credentials from localStorage during initialization
    this.loadCredentialsFromStorage();
  }

  setPrData(data: any[]) {
    this.prDataSubject.next(data);
  }
  setCredentials(
    patToken: string,
    project: string,
    repository: string,
    projectId?: string,
    repositoryId?: string
  ) {
    this.credentialsSubject.next({
      patToken,
      project,
      repository,
      projectId,
      repositoryId,
    });

    const credentials = {
      patToken,
      project,
      repository,
      projectId,
      repositoryId,
    };
    // Save to local storage
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(credentials));
  }

  // Load credentials from local storage
  public loadCredentialsFromStorage() {
    const storedCredentials = localStorage.getItem(this.STORAGE_KEY);
    if (storedCredentials) {
      try {
        const credentials = JSON.parse(storedCredentials) as Credentials;
        this.credentialsSubject.next(credentials);
      } catch (error) {
        console.error('Error parsing stored credentials:', error);
        localStorage.removeItem(this.STORAGE_KEY);
      }
    }
  }

  // Clear credentials (for logout)
  clearCredentials() {
    ('Clearing credentials from service and localStorage');
    // Clear from localStorage first
    localStorage.removeItem(this.STORAGE_KEY);
    // Then update the subject
    this.credentialsSubject.next(null);

    // Also clear any stored continuation tokens
    this.continuationTokens.clear();

    return true; // Return a value to indicate completion
  }
  // Method to fetch data with continuation token support
  fetchPrData(
    pageIndex: number,
    pageSize: number,
    filter?: string,
    sort?: string
  ) {
    return new Promise<{
      data: any[];
      totalCount: number;
      continuationToken?: string;
    }>((resolve, reject) => {
      const credentials = this.credentialsSubject.getValue();
      if (!credentials) {
        reject('No credentials available');
        return;
      }      const skip = pageIndex * pageSize;

      // Use ApiUrlService to build the URL rather than hardcoding
      let url = this.configService.getPullRequestsEndpoint() + `?Top=${pageSize}&Skip=${skip}&Project=${credentials.project}&Repositories=${credentials.repository}&PATToken=${credentials.patToken}`;

      if (filter) {
        url += `&$filter=${encodeURIComponent(filter)}`;
      }

      if (sort) {
        url += `&$orderby=${encodeURIComponent(sort)}`;
      }

      const headers: HttpHeaders = new HttpHeaders();
      const token = this.continuationTokens.get(pageIndex);
      if (token) {
        headers.set('x-ms-continuationtoken', token);
      }

      this.http.get(url, { headers, observe: 'response' }).subscribe({
        next: (response: any) => {
          // Store continuation token if present
          const nextToken = response.headers.get('x-ms-continuationtoken');
          if (nextToken) {
            this.continuationTokens.set(pageIndex + 1, nextToken);
          }

          const body = response.body;
          resolve({
            data: body.data || [],
            totalCount: body.totalCount || 1000,
            continuationToken: nextToken,
          });
        },
        error: (error) => {
          console.error('Error fetching PR data:', error);
          reject(error);
        },
      });
    });
  }

  // PR status count cache
  private prStatusCountCache: {
    active?: number;
    completed?: number;
    abandoned?: number;
    timestamp: number;
    project?: string;
    repository?: string;
  } = { timestamp: 0 };
  // Cache expiration time from config
  private readonly CACHE_EXPIRATION_MS: number;

  // Observable subjects for PR counts
  private activeCountSubject = new BehaviorSubject<number | null>(null);
  private completedCountSubject = new BehaviorSubject<number | null>(null);
  private abandonedCountSubject = new BehaviorSubject<number | null>(null);

  // Observable streams for components to subscribe to
  activeCount$ = this.activeCountSubject.asObservable();
  completedCount$ = this.completedCountSubject.asObservable();
  abandonedCount$ = this.abandonedCountSubject.asObservable();

  /**
   * Gets PR status counts, using cached values if available and not expired
   * @param http HttpClient instance
   * @param project Project name
   * @param repository Repository name
   * @param patToken Personal access token
   * @param apiUrlService API URL service
   * @param forceRefresh Whether to force a refresh of the cache
   */
  getPrStatusCount(
    http: HttpClient,
    project: string,
    repository: string,
    patToken: string,
    apiUrlService: ApiUrlService,
    forceRefresh = false
  ): void {
    // If parameters don't match cached project/repo, force refresh
    if (
      this.prStatusCountCache.project !== project ||
      this.prStatusCountCache.repository !== repository
    ) {
      forceRefresh = true;
    }

    const now = Date.now();
    const cacheExpired =
      now - this.prStatusCountCache.timestamp > this.CACHE_EXPIRATION_MS;

    // Return cached values if we have them and they're not expired
    if (
      !forceRefresh &&
      !cacheExpired &&
      this.prStatusCountCache.active !== undefined &&
      this.prStatusCountCache.completed !== undefined &&
      this.prStatusCountCache.abandoned !== undefined
    ) {
      console.log('Using cached PR status counts');
      this.activeCountSubject.next(this.prStatusCountCache.active);
      this.completedCountSubject.next(this.prStatusCountCache.completed);
      this.abandonedCountSubject.next(this.prStatusCountCache.abandoned);
      return;
    }

    console.log('Fetching fresh PR status counts');

    // Update cache project and repository
    this.prStatusCountCache.project = project;
    this.prStatusCountCache.repository = repository;

    // Create a function to get the URL for a specific status
    const getUrl = (status: string): string => {
      return apiUrlService.getPullRequestStatusCountUrl(
        status,
        project,
        repository,
        patToken
      );
    };

    // Fetch active count
    http
      .get<any>(getUrl('active'))
      .pipe(
        map((response) => response?.data?.value || 0),
        catchError(() => {
          console.error('Error fetching active PR count');
          return of(this.prStatusCountCache.active || 0);
        })
      )
      .subscribe((count) => {
        this.prStatusCountCache.active = count;
        this.prStatusCountCache.timestamp = Date.now();
        this.activeCountSubject.next(count);
      });

    // Fetch completed count
    http
      .get<any>(getUrl('completed'))
      .pipe(
        map((response) => response?.data?.value || 0),
        catchError(() => {
          console.error('Error fetching completed PR count');
          return of(this.prStatusCountCache.completed || 0);
        })
      )
      .subscribe((count) => {
        this.prStatusCountCache.completed = count;
        this.prStatusCountCache.timestamp = Date.now();
        this.completedCountSubject.next(count);
      });

    // Fetch abandoned count
    http
      .get<any>(getUrl('abandoned'))
      .pipe(
        map((response) => response?.data?.value || 0),
        catchError(() => {
          console.error('Error fetching abandoned PR count');
          return of(this.prStatusCountCache.abandoned || 0);
        })
      )
      .subscribe((count) => {
        this.prStatusCountCache.abandoned = count;
        this.prStatusCountCache.timestamp = Date.now();
        this.abandonedCountSubject.next(count);
      });
  }

  /**
   * Clears the PR status count cache, forcing a refresh on next request
   */
  clearPrStatusCountCache(): void {
    this.prStatusCountCache = { timestamp: 0 };
    this.activeCountSubject.next(null);
    this.completedCountSubject.next(null);
    this.abandonedCountSubject.next(null);
  }
}
