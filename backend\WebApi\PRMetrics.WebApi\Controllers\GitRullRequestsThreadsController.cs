﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Microsoft.AspNetCore.Mvc;
using MediatR;
using RIB.PRMetrics.Application.UseCases.GitPullRequestThreads.Queries.GetGitPullRequestThreads;

namespace RIB.PRMetrics.WebApi.Controllers
{
    /// <summary>
    /// Controller responsible for handling Git pull request threads -related API requests.
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class GitPullRequestThreadsController : ControllerBase
    {
        private readonly IMediator _mediator;

        /// <summary>
        /// Initializes a new instance of the <see cref="GitPullRequestThreadsController"/> class.
        /// </summary>
        /// <param name="mediator">The mediator used to send queries to the application layer.</param>
        public GitPullRequestThreadsController(IMediator mediator)
        {
            _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
        }


        /// <summary>
        /// Handles the HTTP GET request to fetch Git Pull Request threads based on the provided query parameters.
        /// </summary>
        /// <param name="query">The query containing necessary pull request details (e.g., pull request ID, project, repository) passed in as query parameters.</param>
        /// <returns>An HTTP response containing either the data (OK) or an error message (BadRequest) depending on the success of the operation.</returns>
        [HttpGet("GetGitPullRequestThreads")]
        public async Task<IActionResult> GetGitPullRequestThreads([FromQuery] GetGitPullRequestThreadsQuery query)
        {
            // Send the query to the mediator for processing, which will handle the request and retrieve the data.
            var response = await _mediator.Send(query);

            // Check if the response was successful
            if (response.Succcess)
            {
                // Return an OK response with the data if the request was successful
                return Ok(response);
            }

            // Return a BadRequest response with the error message if the request failed
            return BadRequest(response);
        }

    }
}