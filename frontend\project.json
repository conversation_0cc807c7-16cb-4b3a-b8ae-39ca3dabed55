{"name": "frontend", "$schema": "node_modules/nx/schemas/project-schema.json", "includedScripts": [], "projectType": "application", "prefix": "app", "sourceRoot": "./src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/frontend", "index": "./src/index.html", "browser": "./src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public", "output": "/"}], "styles": ["./src/styles.scss"], "scripts": []}, "configurations": {"production": {"optimization": {"scripts": true, "styles": true, "fonts": true}, "aot": true, "buildOptimizer": true, "extractLicenses": true, "sourceMap": false, "namedChunks": false, "vendorChunk": true, "commonChunk": true, "budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "2mb"}, {"type": "anyComponentStyle", "maximumWarning": "8kb", "maximumError": "30kb"}, {"type": "bundle", "name": "vendor", "maximumWarning": "500kb", "maximumError": "1mb"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "staging": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "2mb"}, {"type": "anyComponentStyle", "maximumWarning": "8kb", "maximumError": "30kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"continuous": true, "executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "frontend:build:production"}, "staging": {"buildTarget": "frontend:build:staging"}, "development": {"buildTarget": "frontend:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "frontend:build"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["./src"]}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "frontend:build", "staticFilePath": "dist/frontend/browser", "spa": true}}}}