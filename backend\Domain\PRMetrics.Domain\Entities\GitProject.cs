﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities
{
    /// <summary>
    /// Represents a Git project with properties like ID, name, description, URL, and state.
    /// </summary>
    public class GitProject
    {
        /// <summary>
        /// Gets the unique identifier for the Git project.
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Gets the name of the Git project.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets the description of the Git project.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets the url of the Git project.
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        /// Gets the state of the Git project.
        /// </summary>
        public string State { get; set; }
    }
}
