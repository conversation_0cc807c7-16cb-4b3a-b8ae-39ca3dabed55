﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using Microsoft.AspNetCore.Mvc;
using RIB.PRMetrics.Application.Dto.IdentityPicker;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using System.ComponentModel.DataAnnotations;

namespace RIB.PRMetrics.Application.UseCases.IdentityPicker.Queries.GetIdentities
{
    public class GetIdentitiesQuery : IRequest<BaseReponseGeneric<List<IdentitiesItemDto>>>
    {
        /// <summary>
        /// The unique user identifier (UUID) to query identities for.
        /// </summary>
        [Required(ErrorMessage = "UserUuid Id is required.")]
        [FromQuery(Name = "userUuid")]
        public string UserUuid { get; set; }

        /// <summary>
        /// Personal Access Token (PAT) used for authentication.
        /// </summary>
        [Required(ErrorMessage = "PAT Token is required.")]
        [FromQuery(Name = "patToken")]
        public string PATToken { get; set; }
    }
}
