﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using AutoMapper;
using   RIB.PRMetrics.Application.Dto;
using   RIB.PRMetrics.Application.Interface.Persistence;
using   RIB.PRMetrics.Application.UseCases.Commons.Bases;

namespace  RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetPullRequests
{
    public class GetGitPullRequestByIdHandler : IRequestHandler<GetGitPullRequestQuery, BaseReponseGeneric<List<GitPullRequestDto>>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public GetGitPullRequestByIdHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        public async Task<BaseReponseGeneric<List<GitPullRequestDto>>> Handle(GetGitPullRequestQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<List<GitPullRequestDto>>()
            {
                Succcess = false // Ensuring default to false in case of error
            };

            try
            {
                var gitPullRequests = await _unitOfWork.GitPullRequestsRepository.GetGitPullRequestsAsync(request);

                if (gitPullRequests != null && gitPullRequests.Any())
                {
                    response.Data = _mapper.Map<List<GitPullRequestDto>>(gitPullRequests);
                    response.Succcess = true;
                    response.Message = "Git Pull Request list fetch succeeded!";
                }
                else
                {
                    response.Message = "No pull requests found for the given query.";
                }
            }
            catch (Exception ex)
            {
                response.Message = $"An error occurred while fetching pull requests: {ex.Message}";
                // You could log the exception here (e.g., _logger.LogError(ex, "Failed to fetch pull requests"));
            }

            return response;
        }
    }
}
