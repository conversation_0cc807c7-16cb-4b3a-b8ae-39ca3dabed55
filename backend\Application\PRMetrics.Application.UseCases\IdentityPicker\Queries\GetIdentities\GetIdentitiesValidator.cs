﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;

namespace RIB.PRMetrics.Application.UseCases.IdentityPicker.Queries.GetIdentities
{
    /// <summary>
    /// Validator class for the GetIdentitiesQuery request.
    /// Ensures that the UserUuid and PATToken properties meet validation criteria before processing.
    /// </summary>
    public class GetIdentitiesValidator : AbstractValidator<GetIdentitiesQuery>
    {
        public GetIdentitiesValidator()
        {
            // Validate UserUuid property:
            RuleFor(x => x.UserUuid)
                // UserUuid must not be null
                .NotNull().WithMessage("UserUuid must not be null.")
                // UserUuid must not be empty string
                .NotEmpty().WithMessage("UserUuid is required.");
                

            // Validate PATToken property:
            RuleFor(x => x.PATToken)
                // PATToken must not be null
                .NotNull().WithMessage("PATToken must not be null.")
                // PATToken must not be empty string
                .NotEmpty().WithMessage("PATToken is required.");
        }

        
    }
}
