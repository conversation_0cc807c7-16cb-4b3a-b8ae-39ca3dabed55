﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Newtonsoft.Json;

namespace RIB.PRMetrics.Domain.Entities.CommentThreads
{
    /// <summary>
    /// Represents the response structure for comment threads.
    /// This class is used to deserialize the JSON response containing an array of comment threads.
    /// </summary>
    public class CommentThreadResponse
    {
        /// <summary>
        /// Gets or sets the array of <see cref="CommentThread"/> objects.
        /// Each item in the array represents an individual comment thread.
        /// </summary>
        [JsonProperty("value")]
        public CommentThread[] Value { get; set; }
    }
}
