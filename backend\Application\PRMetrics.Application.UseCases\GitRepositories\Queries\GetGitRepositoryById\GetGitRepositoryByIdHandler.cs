﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using MediatR;
using   RIB.PRMetrics.Application.Dto;
using   RIB.PRMetrics.Application.Interface.Persistence;
using   RIB.PRMetrics.Application.UseCases.Commons.Bases;


namespace  RIB.PRMetrics.Application.UseCases.GitRepositories.Queries.GetGitRepositoryById
{
    /// <summary>
    /// Handler for the GetGitRepositoryByIdQuery. 
    /// This handler fetches details of a Git repository based on the provided query and maps the result to a GitRepositoryDto.
    /// </summary>
    public class GetGitProjectByIdHandler : IRequestHandler<GetGitRepositoryByIdQuery, BaseReponseGeneric<GitRepositoryDto>>
    {
        private readonly IUnitOfWork _unitOfWork; // The unit of work interface for interacting with the database.
        private readonly IMapper _mapper; // The AutoMapper instance for mapping between entities and DTOs.

        /// <summary>
        /// Constructor to initialize the handler with the necessary dependencies.
        /// </summary>
        /// <param name="unitOfWork">The unit of work interface</param>
        /// <param name="mapper">The AutoMapper instance</param>
        public GetGitProjectByIdHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        /// <summary>
        /// Handles the GetGitRepositoryByIdQuery and fetches the Git repository details.
        /// If successful, it maps the data to a GitRepositoryDto and returns it in a response.
        /// </summary>
        /// <param name="request">The request containing the Git repository query parameters.</param>
        /// <param name="cancellationToken">Token to handle cancellation of the operation.</param>
        /// <returns>A response containing the GitRepositoryDto with the result of the query.</returns>
        async Task<BaseReponseGeneric<GitRepositoryDto>> IRequestHandler<GetGitRepositoryByIdQuery, BaseReponseGeneric<GitRepositoryDto>>.Handle(GetGitRepositoryByIdQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<GitRepositoryDto>();
            try
            {
                // Fetch the Git repository details from the database via the unit of work.
                var gitRepo = await _unitOfWork.GitRepoRepository.GetGitRepositoriesDetailsAsync(request);

                // If a valid repository is found, map the result to the DTO and set success.
                if (gitRepo is not null)
                {
                    response.Data = _mapper.Map<GitRepositoryDto>(gitRepo);
                    response.Succcess = true;
                    response.Message = "Git Repository fetch succeeded!";
                }
                else
                {
                    response.Message = "Git Repository not found.";
                }
            }
            catch (Exception ex)
            {
                // In case of an error, set the error message.
                response.Message = ex.Message;
            }
            return response;
        }
    }
}
