import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ExportApiService, ExportRequest } from './export-api.service';
import { DownloadProgressService } from './download-progress.service';
import { SignalRService } from './signalr.service';
import { ConfigService } from './config.service';

@Injectable({
  providedIn: 'root'
})
export class ExcelExportService {

  constructor(
    private exportApiService: ExportApiService,
    private downloadProgressService: DownloadProgressService,
    private signalRService: SignalRService,
    private configService: ConfigService
  ) { }

  /**
   * Export PR table data to Excel using backend API
   * @param exportParams Export parameters including filters and credentials
   * @returns Observable with download progress tracking
   */
  async exportPRTableToExcel(exportParams: {
    project: string;
    repository: string;
    patToken: string;
    status?: string;
    startDate?: string;
    endDate?: string;
    searchTerm?: string;
  }): Promise<{ uuid: string; progress$: Observable<any> }> {

    // Ensure SignalR connection
    await this.signalRService.ensureConnection();

    // Prepare export request with correct parameter names
    const exportRequest: ExportRequest = {
      project: exportParams.project,
      repositories: exportParams.repository, // Note: backend uses 'repositories'
      patToken: exportParams.patToken,
      status: exportParams.status && exportParams.status !== 'all' ? exportParams.status : undefined,
      minTime: exportParams.startDate ? new Date(exportParams.startDate).toISOString() : undefined,
      maxTime: exportParams.endDate ? new Date(exportParams.endDate + 'T23:59:59.999Z').toISOString() : undefined,
      includeLinks: true
    };

    try {
      // Start export process
      const response = await this.exportApiService.startExport(exportRequest).toPromise();

      if (!response?.succcess || !response.data) {
        throw new Error(response?.message || 'Failed to start export process');
      }

      const uuid = response.data;

      // Start tracking progress with SignalR
      // The backend generates the filename, so we'll use a default here and update it from SignalR
      const progress$ = this.downloadProgressService.startDownloadTracking(
        uuid,
        `prmetrics-${new Date().toISOString().split('T')[0]}.xlsx`
      );

      return { uuid, progress$ };

    } catch (error) {
      console.error('Error starting export:', error);
      throw error;
    }
  }

  /**
   * Download completed export file
   * @param uuid Export job UUID
   * @param fileName File name for download
   */
  async downloadExportFile(uuid: string, fileName: string): Promise<void> {
    try {
      await this.exportApiService.downloadAndSaveFile(uuid, fileName);
    } catch (error) {
      console.error('Error downloading export file:', error);
      throw error;
    }
  }

  /**
   * Pause export process
   * @param uuid Export job UUID
   */
  async pauseExport(uuid: string): Promise<void> {
    return this.downloadProgressService.pauseDownload(uuid);
  }

  /**
   * Resume export process
   * @param uuid Export job UUID
   */
  async resumeExport(uuid: string): Promise<void> {
    return this.downloadProgressService.resumeDownload(uuid);
  }

  /**
   * Cancel export process
   * @param uuid Export job UUID
   */
  async cancelExport(uuid: string): Promise<void> {
    return this.downloadProgressService.cancelDownload(uuid);
  }

  /**
   * Get export progress
   * @param uuid Export job UUID
   */
  getExportProgress(uuid: string): Observable<any> | null {
    return this.downloadProgressService.getDownloadProgress(uuid);
  }

}




