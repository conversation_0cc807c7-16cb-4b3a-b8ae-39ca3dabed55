import { Injectable } from '@angular/core';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { PullRequestDetails, CommentThread } from '../models/pr-interfaces.model';
import { ReviewCycleMetrics } from '../models/review-cycle.model';

@Injectable({
  providedIn: 'root'
})
export class ExcelExportService {

  constructor() { }

  /**
   * Export PR details to Excel file
   * @param pullRequest PR details data
   * @param commentThreads Comment threads data
   * @param reviewCycleMetrics Review cycle metrics
   * @param draftModeMetrics Draft mode metrics
   */
  exportPRDetailsToExcel(
    pullRequest: PullRequestDetails,
    commentThreads: CommentThread[],
    reviewCycleMetrics: ReviewCycleMetrics | null,
    draftModeMetrics: any | null
  ): void {
    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Create PR Overview sheet
    this.createPROverviewSheet(workbook, pullRequest, reviewCycleMetrics, draftModeMetrics);

    // Create Comments sheet
    this.createCommentsSheet(workbook, commentThreads);

    // Create Reviewers sheet
    this.createReviewersSheet(workbook, pullRequest.reviewers || []);

    // Create Work Items sheet
    this.createWorkItemsSheet(workbook, pullRequest.workItemRefs || []);

    // Generate filename with PR ID and current date
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `PR_${pullRequest.pullRequestId}_Details_${timestamp}.xlsx`;

    // Write and save the file
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    saveAs(blob, filename);
  }

  /**
   * Create PR Overview sheet
   */
  private createPROverviewSheet(
    workbook: XLSX.WorkBook,
    pullRequest: PullRequestDetails,
    reviewCycleMetrics: ReviewCycleMetrics | null,
    draftModeMetrics: any | null
  ): void {
    const data = [
      ['Pull Request Details', ''],
      ['PR ID', pullRequest.pullRequestId],
      ['Title', pullRequest.title],
      ['Status', pullRequest.status],
      ['Is Draft', pullRequest.isDraft === 'true' ? 'Yes' : 'No'],
      ['Created By', pullRequest.createdBy?.displayName || ''],
      ['Creation Date', this.formatDate(pullRequest.creationDate)],
      ['Source Branch', this.formatBranchName(pullRequest.sourceRefName)],
      ['Target Branch', this.formatBranchName(pullRequest.targetRefName)],
      ['Merge Status', pullRequest.mergeStatus || ''],
      ['Description', pullRequest.description || 'No description provided'],
      [''],
      ['Metrics', ''],
    ];

    // Add draft mode metrics if available
    if (draftModeMetrics) {
      data.push(
        ['Time in Draft', draftModeMetrics.totalDraftFormatted || ''],
        ['Time in Published', draftModeMetrics.totalPublishedFormatted || '']
      );
    }

    // Add review cycle metrics if available
    if (reviewCycleMetrics) {
      data.push(
        ['Review Cycles', reviewCycleMetrics.cycleCount?.toString() || ''],
        ['Review Description', reviewCycleMetrics.description || '']
      );
    }

    const worksheet = XLSX.utils.aoa_to_sheet(data);
    
    // Set column widths
    worksheet['!cols'] = [
      { width: 20 },
      { width: 50 }
    ];

    XLSX.utils.book_append_sheet(workbook, worksheet, 'PR Overview');
  }

  /**
   * Create Comments sheet
   */
  private createCommentsSheet(workbook: XLSX.WorkBook, commentThreads: CommentThread[]): void {
    const data = [
      ['Thread ID', 'Status', 'File Path', 'Line', 'Author', 'Comment', 'Published Date', 'Duration']
    ];

    commentThreads.forEach(thread => {
      const filePath = thread.pullRequestThreadContext?.filePath || '';
      const line = thread.pullRequestThreadContext?.rightFileStart?.line || '';
      
      thread.comments?.forEach(comment => {
        data.push([
          thread.id?.toString() || '',
          thread.status || '',
          filePath,
          line.toString(),
          comment.author?.displayName || '',
          this.cleanContent(comment.content || ''),
          this.formatDate(comment.publishedDate),
          thread.activeDurationFormatted || thread.fixedDurationFormatted || ''
        ]);
      });
    });

    const worksheet = XLSX.utils.aoa_to_sheet(data);
    
    // Set column widths
    worksheet['!cols'] = [
      { width: 10 }, // Thread ID
      { width: 12 }, // Status
      { width: 30 }, // File Path
      { width: 8 },  // Line
      { width: 20 }, // Author
      { width: 50 }, // Comment
      { width: 18 }, // Published Date
      { width: 15 }  // Duration
    ];

    XLSX.utils.book_append_sheet(workbook, worksheet, 'Comments');
  }

  /**
   * Create Reviewers sheet
   */
  private createReviewersSheet(workbook: XLSX.WorkBook, reviewers: any[]): void {
    const data = [
      ['Reviewer Name', 'Vote', 'Vote Status', 'Is Required', 'Email']
    ];

    reviewers.forEach(reviewer => {
      const voteStatus = this.getVoteStatus(reviewer.vote);
      data.push([
        reviewer.displayName || '',
        reviewer.vote?.toString() || '',
        voteStatus,
        reviewer.isRequired ? 'Yes' : 'No',
        reviewer.uniqueName || ''
      ]);
    });

    const worksheet = XLSX.utils.aoa_to_sheet(data);
    
    // Set column widths
    worksheet['!cols'] = [
      { width: 25 }, // Reviewer Name
      { width: 8 },  // Vote
      { width: 25 }, // Vote Status
      { width: 12 }, // Is Required
      { width: 30 }  // Email
    ];

    XLSX.utils.book_append_sheet(workbook, worksheet, 'Reviewers');
  }

  /**
   * Create Work Items sheet
   */
  private createWorkItemsSheet(workbook: XLSX.WorkBook, workItems: any[]): void {
    const data = [
      ['Work Item ID', 'Title', 'URL']
    ];

    workItems.forEach(workItem => {
      data.push([
        workItem.id?.toString() || '',
        workItem.title || '',
        workItem.url || ''
      ]);
    });

    const worksheet = XLSX.utils.aoa_to_sheet(data);
    
    // Set column widths
    worksheet['!cols'] = [
      { width: 15 }, // Work Item ID
      { width: 40 }, // Title
      { width: 50 }  // URL
    ];

    XLSX.utils.book_append_sheet(workbook, worksheet, 'Work Items');
  }

  /**
   * Helper method to format date
   */
  private formatDate(dateString: string): string {
    if (!dateString) return '';
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  }

  /**
   * Helper method to format branch name
   */
  private formatBranchName(refName: string): string {
    if (!refName) return '';
    return refName.replace('refs/heads/', '');
  }

  /**
   * Helper method to get vote status text
   */
  private getVoteStatus(vote: number): string {
    switch (vote) {
      case 10: return 'Approved';
      case 5: return 'Approved with suggestions';
      case 0: return 'No vote';
      case -5: return 'Waiting for author';
      case -10: return 'Rejected';
      default: return 'Unknown';
    }
  }

  /**
   * Helper method to clean content (remove GUID tokens)
   */
  private cleanContent(content: string): string {
    if (!content) return '';
    // Remove @<GUID> tokens for cleaner export
    return content.replace(/@<[0-9a-fA-F\-]{36}>/g, '[User]');
  }
}
