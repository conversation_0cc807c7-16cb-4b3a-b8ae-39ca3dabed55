import { Injectable } from '@angular/core';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { PullRequestDetails, CommentThread } from '../models/pr-interfaces.model';
import { ReviewCycleMetrics } from '../models/review-cycle.model';

@Injectable({
  providedIn: 'root'
})
export class ExcelExportService {

  constructor() { }

  /**
   * Export PR table data to Excel file
   * @param prData Array of PR data from the table
   * @param filters Applied filters information
   */
  exportPRTableToExcel(prData: any[], filters?: any): void {
    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Create PR Table sheet
    this.createPRTableSheet(workbook, prData, filters);

    // Generate filename with current date
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `PR_Table_Export_${timestamp}.xlsx`;

    // Write and save the file
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    saveAs(blob, filename);
  }

  /**
   * Create PR Table sheet with all PR data
   */
  private createPRTableSheet(workbook: XLSX.WorkBook, prData: any[], filters?: any): void {
    // Define headers for the PR table
    const headers = [
      'PR ID',
      'Title',
      'Status',
      'Created By',
      'Creation Date',
      'Source Branch',
      'Target Branch',
      'Is Draft',
      'Merge Status',
      'Active Comments',
      'Pipeline Status',
      'Time Lapse (Hours)',
      'Closed Date',
      'Repository',
      'Project'
    ];

    // Create data array starting with headers
    const data = [headers];

    // Add filter information if provided
    if (filters) {
      data.push([]);
      data.push(['Applied Filters:']);
      if (filters.status && filters.status !== 'all') {
        data.push(['Status Filter:', filters.status]);
      }
      if (filters.dateRange) {
        data.push(['Date Range:', `${filters.dateRange.start} to ${filters.dateRange.end}`]);
      }
      if (filters.search) {
        data.push(['Search Term:', filters.search]);
      }
      data.push([]);
      data.push(headers); // Add headers again after filter info
    }

    // Process each PR and add to data
    prData.forEach(pr => {
      const row = [
        pr.pullRequestId || '',
        pr.title || '',
        pr.status || '',
        pr.createdBy?.displayName || '',
        this.formatDate(pr.creationDate),
        this.formatBranchName(pr.sourceRefName),
        this.formatBranchName(pr.targetRefName),
        pr.isDraft === 'true' ? 'Yes' : 'No',
        pr.mergeStatus || '',
        pr.activeComments || 0,
        this.getPipelineStatusText(pr.pipelineStatusDisplay),
        this.calculateTimeLapse(pr.creationDate, pr.closedDate),
        this.formatDate(pr.closedDate),
        pr.repository?.name || '',
        pr.project?.name || ''
      ];
      data.push(row);
    });

    const worksheet = XLSX.utils.aoa_to_sheet(data);

    // Set column widths for better readability
    worksheet['!cols'] = [
      { width: 10 },  // PR ID
      { width: 40 },  // Title
      { width: 12 },  // Status
      { width: 20 },  // Created By
      { width: 18 },  // Creation Date
      { width: 25 },  // Source Branch
      { width: 25 },  // Target Branch
      { width: 10 },  // Is Draft
      { width: 15 },  // Merge Status
      { width: 15 },  // Active Comments
      { width: 15 },  // Pipeline Status
      { width: 15 },  // Time Lapse
      { width: 18 },  // Closed Date
      { width: 20 },  // Repository
      { width: 20 }   // Project
    ];

    // Add some styling to headers
    const headerRowIndex = filters ? 6 : 0; // Adjust based on whether filters are shown
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');

    // Style header row
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: headerRowIndex, c: col });
      if (!worksheet[cellAddress]) continue;

      worksheet[cellAddress].s = {
        font: { bold: true },
        fill: { fgColor: { rgb: "CCCCCC" } },
        alignment: { horizontal: "center" }
      };
    }

    XLSX.utils.book_append_sheet(workbook, worksheet, 'PR Table Data');
  }

  /**
   * Create PR Overview sheet
   */
  private createPROverviewSheet(
    workbook: XLSX.WorkBook,
    pullRequest: PullRequestDetails,
    reviewCycleMetrics: ReviewCycleMetrics | null,
    draftModeMetrics: any | null
  ): void {
    const data = [
      ['Pull Request Details', ''],
      ['PR ID', pullRequest.pullRequestId],
      ['Title', pullRequest.title],
      ['Status', pullRequest.status],
      ['Is Draft', pullRequest.isDraft === 'true' ? 'Yes' : 'No'],
      ['Created By', pullRequest.createdBy?.displayName || ''],
      ['Creation Date', this.formatDate(pullRequest.creationDate)],
      ['Source Branch', this.formatBranchName(pullRequest.sourceRefName)],
      ['Target Branch', this.formatBranchName(pullRequest.targetRefName)],
      ['Merge Status', pullRequest.mergeStatus || ''],
      ['Description', pullRequest.description || 'No description provided'],
      [''],
      ['Metrics', ''],
    ];

    // Add draft mode metrics if available
    if (draftModeMetrics) {
      data.push(
        ['Time in Draft', draftModeMetrics.totalDraftFormatted || ''],
        ['Time in Published', draftModeMetrics.totalPublishedFormatted || '']
      );
    }

    // Add review cycle metrics if available
    if (reviewCycleMetrics) {
      data.push(
        ['Review Cycles', reviewCycleMetrics.cycleCount?.toString() || ''],
        ['Review Description', reviewCycleMetrics.description || '']
      );
    }

    const worksheet = XLSX.utils.aoa_to_sheet(data);
    
    // Set column widths
    worksheet['!cols'] = [
      { width: 20 },
      { width: 50 }
    ];

    XLSX.utils.book_append_sheet(workbook, worksheet, 'PR Overview');
  }

  /**
   * Create Comments sheet
   */
  private createCommentsSheet(workbook: XLSX.WorkBook, commentThreads: CommentThread[]): void {
    const data = [
      ['Thread ID', 'Status', 'File Path', 'Line', 'Author', 'Comment', 'Published Date', 'Duration']
    ];

    commentThreads.forEach(thread => {
      const filePath = thread.pullRequestThreadContext?.filePath || '';
      const line = thread.pullRequestThreadContext?.rightFileStart?.line || '';
      
      thread.comments?.forEach(comment => {
        data.push([
          thread.id?.toString() || '',
          thread.status || '',
          filePath,
          line.toString(),
          comment.author?.displayName || '',
          comment.content || '',
          this.formatDate(comment.publishedDate),
          thread.activeDurationFormatted || thread.fixedDurationFormatted || ''
        ]);
      });
    });

    const worksheet = XLSX.utils.aoa_to_sheet(data);
    
    // Set column widths
    worksheet['!cols'] = [
      { width: 10 }, // Thread ID
      { width: 12 }, // Status
      { width: 30 }, // File Path
      { width: 8 },  // Line
      { width: 20 }, // Author
      { width: 50 }, // Comment
      { width: 18 }, // Published Date
      { width: 15 }  // Duration
    ];

    XLSX.utils.book_append_sheet(workbook, worksheet, 'Comments');
  }

  /**
   * Create Reviewers sheet
   */
  private createReviewersSheet(workbook: XLSX.WorkBook, reviewers: any[]): void {
    const data = [
      ['Reviewer Name', 'Vote', 'Vote Status', 'Is Required', 'Email']
    ];

    reviewers.forEach(reviewer => {
      const voteStatus = this.getReviewerVoteStatus(reviewer.vote);
      data.push([
        reviewer.displayName || '',
        reviewer.vote?.toString() || '',
        voteStatus,
        reviewer.isRequired ? 'Yes' : 'No',
        reviewer.uniqueName || ''
      ]);
    });

    const worksheet = XLSX.utils.aoa_to_sheet(data);
    
    // Set column widths
    worksheet['!cols'] = [
      { width: 25 }, // Reviewer Name
      { width: 8 },  // Vote
      { width: 25 }, // Vote Status
      { width: 12 }, // Is Required
      { width: 30 }  // Email
    ];

    XLSX.utils.book_append_sheet(workbook, worksheet, 'Reviewers');
  }

  /**
   * Create Work Items sheet
   */
  private createWorkItemsSheet(workbook: XLSX.WorkBook, workItems: any[]): void {
    const data = [
      ['Work Item ID', 'Title', 'URL']
    ];

    workItems.forEach(workItem => {
      data.push([
        workItem.id?.toString() || '',
        workItem.title || '',
        workItem.url || ''
      ]);
    });

    const worksheet = XLSX.utils.aoa_to_sheet(data);
    
    // Set column widths
    worksheet['!cols'] = [
      { width: 15 }, // Work Item ID
      { width: 40 }, // Title
      { width: 50 }  // URL
    ];

    XLSX.utils.book_append_sheet(workbook, worksheet, 'Work Items');
  }

  /**
   * Helper method to format date
   */
  private formatDate(dateString: string): string {
    if (!dateString) return '';
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  }

  /**
   * Helper method to format branch name
   */
  private formatBranchName(refName: string): string {
    if (!refName) return '';
    return refName.replace('refs/heads/', '');
  }

  /**
   * Helper method to get pipeline status text
   */
  private getPipelineStatusText(status: number): string {
    switch (status) {
      case 1: return 'In Progress';
      case 2: return 'Succeeded';
      case 3: return 'Failed';
      case 0: return 'Not Started';
      default: return 'Unknown';
    }
  }

  /**
   * Helper method to calculate time lapse in hours
   */
  private calculateTimeLapse(creationDate: string, closedDate?: string): number {
    if (!creationDate) return 0;

    const created = new Date(creationDate);
    const end = closedDate ? new Date(closedDate) : new Date();
    const diffMs = end.getTime() - created.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);

    return Math.round(diffHours * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Helper method to get reviewer vote status text
   */
  private getReviewerVoteStatus(vote: number): string {
    switch (vote) {
      case 10: return 'Approved';
      case 5: return 'Approved with suggestions';
      case 0: return 'No vote';
      case -5: return 'Waiting for author';
      case -10: return 'Rejected';
      default: return 'Unknown';
    }
  }
}
