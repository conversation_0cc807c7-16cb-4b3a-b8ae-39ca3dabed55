﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using   RIB.PRMetrics.Application.Dto;
using   RIB.PRMetrics.Application.UseCases.Commons.Bases;
using System.ComponentModel.DataAnnotations;

namespace  RIB.PRMetrics.Application.UseCases.GitProjets.Queries.GetGitProjectById
{
    /// <summary>
    /// Query object used to request a Git project by its ID.
    /// Implements IRequest to be handled by MediatR with a generic response type.
    /// </summary>
    public class GetGitProjectByIdQuery : IRequest<BaseReponseGeneric<GitProjectDto>>
    {
        /// <summary>
        /// The unique identifier of the Git project to fetch.
        /// This field is required and validated using DataAnnotations.
        /// </summary>
        [Required(ErrorMessage = "Project Id is required.")]
        [FromQuery(Name = "id")]
        public string Id { get; set; }

        /// <summary>
        /// Personal Access Token used for authenticating the request with Azure DevOps REST API.
        /// This field is required and validated using DataAnnotations.
        /// </summary>
        [Required(ErrorMessage = "Token is required.")]
        [FromQuery(Name = "patToken")]
        public string PATToken { get; set; }
    }
}

