﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using   RIB.PRMetrics.Application.Dto;
using   RIB.PRMetrics.Application.UseCases.Commons.Bases;
using   RIB.PRMetrics.Domain.Entities;
using System.ComponentModel.DataAnnotations;

namespace  RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestById
{
    /// <summary>
    /// Query class for retrieving a specific Git Pull Request by its ID.
    /// Inherits from GitCommon to reuse common Git request parameters.
    /// </summary>
    public class GetGitPullRequestByIdQuery : GitCommon, IRequest<BaseReponseGeneric<GitPullRequestDto>>
    {
        /// <summary>
        /// Pull Request ID to fetch. Required field.
        /// </summary>
        [Required(ErrorMessage = "Pull Request Id is required.")]
        public int PullRequestId { get; set; } = 0;
    }
}
