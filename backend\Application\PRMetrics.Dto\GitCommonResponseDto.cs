﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto
{
    /// <summary>
    /// A generic class that represents a common response structure for Git-related data.
    /// This DTO (Data Transfer Object) can be used to hold both the count and the actual data (Value) in an array format.
    /// </summary>
    public class GitCommonResponseDto<T>
    {
        /// <summary>
        /// Gets or sets the total number of items in the response.
        /// This is typically used for pagination or simply to show how many items were retrieved.
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// Gets or sets the actual data of type T, stored as an array.
        /// The type T can be any Git-related data such as a Git commit, pull request, or other entities.
        /// </summary>
        public T[] Value { get; set; }
    }
}

