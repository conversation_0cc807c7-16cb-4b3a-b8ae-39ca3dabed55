﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace  RIB.PRMetrics.Application.Interface.Persistence
{
    /// <summary>
    /// A generic interface for repository operations, designed for use with any entity class.
    /// </summary>
    /// <typeparam name="T">The type of the entity class that the repository operates on.</typeparam>
    public interface IGenericRepository<T> where T : class
    {
        // Add CRUD operations here, for example:
        // Task<T> GetByIdAsync(int id);
        // Task<IEnumerable<T>> GetAllAsync();
        // Task AddAsync(T entity);
        // Task UpdateAsync(T entity);
        // Task DeleteAsync(int id);
    }
}
