﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace  RIB.PRMetrics.Application.Dto
{
    /// <summary>
    /// Represents a list of pull requests retrieved from Azure DevOps.
    /// </summary>
    public class GitPullRequestListDto
    {
        /// <summary>
        /// The total number of pull requests in the list.
        /// </summary>
        
        public int Count { get; set; }

        /// <summary>
        /// A collection of pull request objects.
        /// </summary>
        
        public List<GitPullRequestDto> PullRequests { get; set; }
    }
}
