﻿using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.ExcelReportExport
{
    public class ExcelReportExportCommandValidator : AbstractValidator<ExcelReportExportCommand>
    {
        public ExcelReportExportCommandValidator()
        {
            // Validate that Project is not empty or null
            RuleFor(x => x.Project)
                .NotEmpty()
                .WithMessage("Project is required.");

            // Validate that Repositories is not empty or null
            RuleFor(x => x.Repositories)
               .NotEmpty()
               .WithMessage("Repositories are required.");

            // You can add more validation rules based on other properties within PullRequestSearchCriteria
            // For example, if you want to validate date ranges, state filters, etc.
        }
    }
}
