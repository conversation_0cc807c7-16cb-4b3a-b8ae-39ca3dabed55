﻿using MediatR;
using Microsoft.Extensions.Caching.Memory;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using RIB.PRMetrics.Domain.Entities;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.ExcelReportExport
{
    public class ExcelReportExportCommandHandler : IRequestHandler<ExcelReportExportCommand, BaseReponseGeneric<Guid>>
    {
        private readonly IJobQueue _jobQueue;
        private readonly IMemoryCache _cache;

        public ExcelReportExportCommandHandler(IJobQueue jobQueue, IMemoryCache cache)
        {
            _jobQueue = jobQueue;
            _cache = cache;
        }

        public Task<BaseReponseGeneric<Guid>> Handle(ExcelReportExportCommand request, CancellationToken cancellationToken)
        {
            var uuid = Guid.NewGuid();
            var response = new BaseReponseGeneric<Guid>()
            {
                Succcess = false // Ensuring default to false in case of error
            };
            try
            {
                string fileName = $"prmetrics-{DateTime.Now:dd-MM-yyyy-HH-mm-ss}-{uuid}.xlsx";

                // Set initial progress in cache
                var progress = new ProcessProgress
                {
                    Uuid = uuid,
                    Percentage = 0,
                    Status = "Pending",
                    FileName = fileName,
                    FileUrl = string.Empty
                };

                _cache.Set(uuid.ToString(), progress);

                // Enqueue job
                _jobQueue.Enqueue(new ExportJob
                {
                    Uuid = uuid,
                    FileName = fileName,
                    pullRequestSearchCriteria = request
                });
                response.Data = uuid;
                response.Succcess = true;
                response.Message = "started the excel export process !";

            }
            catch (Exception ex)
            {
                response.Message = $"An error occurred while create excel export process: {ex.Message}";
                // You could log the exception here (e.g., _logger.LogError(ex, "Failed to fetch pull requests"));
            }

            return Task.FromResult(response);
            //return Task.FromResult(uuid);
        }
    }
}
