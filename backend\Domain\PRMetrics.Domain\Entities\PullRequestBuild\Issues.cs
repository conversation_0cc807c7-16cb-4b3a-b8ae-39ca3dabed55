﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Newtonsoft.Json;

namespace RIB.PRMetrics.Domain.Entities.PullRequestBuild
{
    public class Issues
    {
        /// <summary>
        /// Gets or sets the type of the issue (e.g., error, warning).
        /// </summary>
        [JsonProperty("type")]
        public string Type { get; set; }

        /// <summary>
        /// Gets or sets the category of the issue (e.g., code style, build failure).
        /// </summary>
        [JsonProperty("category")]
        public string Category { get; set; }

        /// <summary>
        /// Gets or sets the message describing the issue in detail.
        /// </summary>
        [JsonProperty("message")]
        public string Message { get; set; }
    }
}
