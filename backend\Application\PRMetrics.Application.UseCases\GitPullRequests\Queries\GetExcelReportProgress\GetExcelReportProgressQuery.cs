﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using RIB.PRMetrics.Domain.Entities;
using System.ComponentModel.DataAnnotations;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetExcelReport
{
    public class GetExcelReportProgressQuery : IRequest<BaseReponseGeneric<List<ProcessProgressDto>>>
    {
        [Required(ErrorMessage = "Uuid is required.")]
        [FromQuery(Name = "uuid")]
        public string[] Uuid { get; set; }

    }
}
