﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using Microsoft.AspNetCore.Mvc;
using RIB.PRMetrics.Application.UseCases.GitPullRequestBuildTimeLine.Queries.GetGitPullRequestBuildTimeLine;

namespace RIB.PRMetrics.WebApi.Controllers
{
    /// <summary>
    /// Controller to handle API requests related to Git Pull Request Build Timeline.
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class GitPullRequestBuildTimeLineController : ControllerBase
    {
        private readonly IMediator _mediator;

        /// <summary>
        /// Initializes a new instance of the <see cref="GitPullRequestBuildTimeLineController"/> class.
        /// </summary>
        /// <param name="mediator">The mediator used to send queries to the application layer.</param>
        public GitPullRequestBuildTimeLineController(IMediator mediator)
        {
            _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
        }

        /// <summary>
        /// Endpoint to get the build timeline for a specific Git Pull Request.
        /// </summary>
        /// <param name="query">The query parameters to retrieve the build timeline for the pull request.</param>
        /// <returns>An IActionResult containing the response from the application layer.</returns>
        [HttpGet("GetGitPullRequestBuildTimeLine")]
        public async Task<IActionResult> GetGitPullRequestBuildTimeLine([FromQuery] GetGitPullRequestBuildTimeLineQuery query)
        {
            // Send the query to the mediator to handle the request.
            var response = await _mediator.Send(query);

            // Return a successful response if the query was successful, otherwise return a BadRequest.
            if (response.Succcess)
            {
                return Ok(response);
            }

            return BadRequest(response);
        }
    }
}
