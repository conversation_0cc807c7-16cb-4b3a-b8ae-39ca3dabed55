﻿using AutoMapper;
using MediatR;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetExcelReport;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestById;
using RIB.PRMetrics.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetExcelReportProgress
{
    public class GetExcelReportProgressHandler : IRequestHandler<GetExcelReportProgressQuery, BaseReponseGeneric<List<ProcessProgressDto>>>
    {
        
        

        private readonly IUnitOfWork _unitOfWork;

        private readonly IMapper _mapper;

        public GetExcelReportProgressHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
           
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }


        public async Task<BaseReponseGeneric<List<ProcessProgressDto>>> Handle(GetExcelReportProgressQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<List<ProcessProgressDto>>();

            try
            {
                
                var excelExportReport = await _unitOfWork.GitPullRequestsRepository.GetExcelReportProgressAsync(request.Uuid);

                if (excelExportReport is not null)
                {
                    response.Data = _mapper.Map<List<ProcessProgressDto>>(excelExportReport);
                    response.Succcess = true;
                    response.Message = "Excel Report File Progress fetched successfully.";

                }
                else
                {
                    response.Succcess = false;
                    response.Message = "Excel Report File Progress not found.";
                }
            }
            catch (Exception ex)
            {
                response.Succcess = false;
                response.Message = $"Error occurred: {ex.Message}";
            }

            return response;
        }
    }
}
