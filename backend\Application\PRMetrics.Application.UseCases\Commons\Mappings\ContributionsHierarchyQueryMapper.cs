﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using RIB.PRMetrics.Application.Dto.Contributions.HierarchyQuery;
using RIB.PRMetrics.Domain.Entities.Contributions.HierarchyQuery;

namespace RIB.PRMetrics.Application.UseCases.Commons.Mappings
{
    /// <summary>
    /// AutoMapper Profile for mapping between domain entities and DTOs related to contributions hierarchy query.
    /// This class contains all the mappings that convert the domain model entities to the corresponding Data Transfer Objects (DTOs) and vice versa.
    /// </summary>
    public class ContributionsHierarchyQueryMapper : Profile
    {
        public ContributionsHierarchyQueryMapper()
        {
            // Mapping between QueryResponse (domain model) and QueryResponseDto (DTO)
            // Maps the DataProviders property from the source to the destination.
            CreateMap<QueryResponse, QueryResponseDto>()
                .ForMember(dest => dest.DataProviders, opt => opt.MapFrom(src => src.DataProviders));

            // Mapping between Policy (domain model) and PolicyDto (DTO)
            // ReverseMap allows both directions (Policy -> PolicyDto and PolicyDto -> Policy)
            CreateMap<Policy, PolicyDto>().ReverseMap();

            // Mapping between PolicyType (domain model) and PolicyTypeDto (DTO)
            // Each property from the source (PolicyType) is mapped to the destination (PolicyTypeDto)
            CreateMap<PolicyType, PolicyTypeDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.DisplayName, opt => opt.MapFrom(src => src.DisplayName))
                .ForMember(dest => dest.Url, opt => opt.MapFrom(src => src.Url));

            // Mapping between PullRequestDataProvider (domain model) and PullRequestDataProviderDto (DTO)
            // Maps Policies and Statuses from source to destination.
            CreateMap<PullRequestDataProvider, PullRequestDataProviderDto>()
                .ForMember(dest => dest.Policies, opt => opt.MapFrom(src => src.Policies))
                .ForMember(dest => dest.Statuses, opt => opt.MapFrom(src => src.Statuses));

            // Mapping between DataProviderContext (domain model) and DataProviderContextDto (DTO)
            // Maps the Properties field from the source to the destination.
            CreateMap<DataProviderContext, DataProviderContextDto>()
                .ForMember(dest => dest.Properties, opt => opt.MapFrom(src => src.Properties));

            // Mapping between DataProviderContextProperties (domain model) and DataProviderContextPropertiesDto (DTO)
            // Maps specific properties from the source to the destination.
            CreateMap<DataProviderContextProperties, DataProviderContextPropertiesDto>()
                .ForMember(dest => dest.PullRequestId, opt => opt.MapFrom(src => src.PullRequestId))
                .ForMember(dest => dest.RepositoryId, opt => opt.MapFrom(src => src.RepositoryId))
                .ForMember(dest => dest.Types, opt => opt.MapFrom(src => src.Types));

            // Mapping between DataProviders (domain model) and DataProvidersDto (DTO)
            // Maps prDetailDataProvider from source to destination.
            CreateMap<DataProviders, DataProvidersDto>()
                .ForMember(dest => dest.prDetailDataProvider, opt => opt.MapFrom(src => src.prDetailDataProvider));
        }
    }
}
