﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestConflicts
{
    /// <summary>
    /// Validator class for <see cref="GetGitPullRequestConflictsQuery"/>.
    /// This class uses FluentValidation to validate the fields of the query request.
    /// It ensures that required fields are provided and not null or empty before proceeding with the request.
    /// </summary>
    public class GetGitPullRequestConflictsValidator : AbstractValidator<GetGitPullRequestConflictsQuery>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="GetGitPullRequestConflictsValidator"/> class.
        /// Defines validation rules for the <see cref="GetGitPullRequestConflictsQuery"/> properties.
        /// </summary>
        public GetGitPullRequestConflictsValidator()
        {
            // Ensure the 'Project' field is neither empty nor null.
            RuleFor(x => x.Project)
                .NotEmpty().WithMessage("Project is required.")
                .NotNull().WithMessage("Project must not be null.");

            // Ensure the 'Repositories' field is neither empty nor null.
            RuleFor(x => x.Repositories)
                .NotEmpty().WithMessage("Repository is required.")
                .NotNull().WithMessage("Repository must not be null.");

            // Ensure the 'PullRequestId' field is neither empty nor null.
            RuleFor(x => x.PullRequestId)
                .NotEmpty().WithMessage("Pull Request ID is required.")
                .NotNull().WithMessage("Pull Request ID must not be null.");

            // Ensure the 'PAT Token' field is neither empty nor null.
            RuleFor(x => x.PATToken)
                .NotEmpty().WithMessage("PAT Token is required.")
                .NotNull().WithMessage("PAT Token must not be null.");
        }
    }
}
