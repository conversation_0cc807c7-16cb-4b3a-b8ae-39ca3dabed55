﻿using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.TakeActionExcelReport
{
    public class TakeActionExcelReportCommandValidator : AbstractValidator<TakeActionExcelReportCommand>
    {
        public TakeActionExcelReportCommandValidator()
        {
            // Validate that Uuid is not empty or null
            RuleFor(x => x.Uuid)
                .NotEmpty()
                .WithMessage("Uuid is required.");

            RuleFor(x => x.JobStatus)
                .NotEmpty()
                .WithMessage("JobStatus is required.");


        }
    }
}
