import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, catchError, shareReplay } from 'rxjs/operators';
import { ConfigService } from './config.service';

export interface UserIdentity {
  id: string;
  displayName: string;
  emailAddress: string;
  publicAlias?: string;
  coreRevision?: number;
  timeStamp?: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private currentUserSubject = new BehaviorSubject<UserIdentity | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  
  // Cache for user data to avoid repeated API calls
  private userCache = new Map<string, Observable<UserIdentity | null>>();

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {}

  /**
   * Get current user profile information using PAT token
   * @param patToken Personal Access Token
   * @returns Observable of user identity or null
   */
  getCurrentUser(patToken: string): Observable<UserIdentity | null> {
    if (!patToken) {
      return of(null);
    }

    // Check cache first
    if (this.userCache.has(patToken)) {
      return this.userCache.get(patToken)!;
    }

    // Create the API call
    const profileUrl = `${this.configService.API_BASE_URL}/User/GetCurrentUser?patToken=${patToken}`;
    
    const userRequest$ = this.http.get<any>(profileUrl).pipe(
      map(response => {
        if (response && response.succcess && response.data) {
          const userData: UserIdentity = {
            id: response.data.id || '',
            displayName: response.data.displayName || 'DevOps User',
            emailAddress: response.data.emailAddress || '',
            publicAlias: response.data.publicAlias || '',
            coreRevision: response.data.coreRevision || 0,
            timeStamp: response.data.timeStamp || ''
          };
          
          // Update the current user subject
          this.currentUserSubject.next(userData);
          return userData;
        }
        return null;
      }),
      catchError(error => {
        console.warn('Failed to get user profile:', error);
        return of(null);
      }),
      shareReplay(1) // Cache the result
    );

    // Cache the request
    this.userCache.set(patToken, userRequest$);
    
    // Clear cache after 5 minutes
    setTimeout(() => {
      this.userCache.delete(patToken);
    }, 5 * 60 * 1000);

    return userRequest$;
  }

  /**
   * Clear user cache and current user
   */
  clearUserData(): void {
    this.userCache.clear();
    this.currentUserSubject.next(null);
  }

  /**
   * Get the current user value synchronously
   */
  getCurrentUserValue(): UserIdentity | null {
    return this.currentUserSubject.value;
  }
}
