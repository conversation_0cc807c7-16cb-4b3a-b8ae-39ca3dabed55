﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Microsoft.AspNetCore.Mvc;
using MediatR;
using   RIB.PRMetrics.Application.UseCases.GitProjets.Queries.GetGitProjets;
using   RIB.PRMetrics.Application.UseCases.GitProjets.Queries.GetGitProjectById;

namespace  RIB.PRMetrics.WebApi.Controllers
{
    /// <summary>
    /// Controller responsible for handling Git project-related API requests.
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class GitProjectsController : ControllerBase
    {
        private readonly IMediator _mediator;

        /// <summary>
        /// Initializes a new instance of the <see cref="GitProjectsController"/> class.
        /// </summary>
        /// <param name="mediator">The mediator used to send queries to the application layer.</param>
        public GitProjectsController(IMediator mediator)
        {
            _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
        }

        /// <summary>
        /// Handles GET requests to retrieve a list of Git projects.
        /// </summary>
        /// <param name="query">The query object containing parameters for retrieving Git projects.</param>
        /// <returns>A response containing a list of Git projects, or a BadRequest if unsuccessful.</returns>
        [HttpGet("GetGitProjects")]
        public async Task<IActionResult> GetGitProjects([FromQuery] GetGitProjectsQuery query)
        {
            // Send the query to the mediator to handle the request.
            var response = await _mediator.Send(query);

            // Return a successful response if the query was successful, otherwise return a BadRequest.
            if (response.Succcess)
            {
                return Ok(response);
            }

            return BadRequest(response);
        }

        /// <summary>
        /// Handles GET requests to retrieve a specific Git project by its ID.
        /// </summary>
        /// <param name="query">The query object containing the ID of the Git project to retrieve.</param>
        /// <returns>A response containing the Git project details, or a BadRequest if unsuccessful.</returns>
        [HttpGet("GetGitProjectById")]
        public async Task<IActionResult> GetGitProjectById([FromQuery] GetGitProjectByIdQuery query)
        {
            // Send the query to the mediator to handle the request.
            var response = await _mediator.Send(query);

            // Return a successful response if the query was successful, otherwise return a BadRequest.
            if (response.Succcess)
            {
                return Ok(response);
            }

            return BadRequest(response);
        }
    }
}
