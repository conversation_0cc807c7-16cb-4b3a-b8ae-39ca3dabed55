﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
*/

namespace  RIB.PRMetrics.Application.Interface.Persistence
{
    /// <summary>
    /// Defines the contract for a unit of work, which encapsulates repository operations 
    /// and ensures that all changes are committed or rolled back as a single transaction.
    /// </summary>
    public interface IUnitOfWork : IDisposable
    {
        /// <summary>
        /// Gets the repository for Git repositories.
        /// </summary>
        IGitRepoRepository GitRepoRepository { get; }

        /// <summary>
        /// Gets the repository for Git pull requests.
        /// </summary>
        IGitPullRequestRepository GitPullRequestsRepository { get; }

        /// <summary>
        /// Gets the repository for Git projects.
        /// </summary>
        IGitProjectRepository GitProjectRepository { get; }

        /// <summary>
        /// Get the repository for Git Pull Request Threads.
        /// </summary>
        IGitPullRequestThreadsRepository GitPullRequestThreadsRepository { get; }

        /// <summary>
        /// Gets the repository for querying contributions hierarchy data related to Git pull requests.
        /// This repository allows access to contribution data organized in a hierarchical structure based on pull requests.
        /// </summary>
        IContributionsHierarchyQueryRepository ContributionsHierarchyQueryRepository { get; }

        /// <summary>
        /// Gets the repository for querying the build timeline of Git pull requests.
        /// This repository provides access to build-related timeline data for specific pull requests, including steps and events during the build process.
        /// </summary>
        IGitPullRequestBuildTimeLineRepository GitPullRequestBuildTimeLineRepository { get; }

        /// <summary>
        /// Gets the repository responsible for handling identity picker data operations.
        /// </summary>
        IIdentityPickerRepository IdentityPickerRepository { get; }

        
    }
}

