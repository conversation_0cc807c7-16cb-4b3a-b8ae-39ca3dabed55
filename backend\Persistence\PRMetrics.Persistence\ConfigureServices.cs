﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Microsoft.Extensions.DependencyInjection;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Persistence.Common;
using RIB.PRMetrics.Persistence.Contexts;
using RIB.PRMetrics.Persistence.Repositories;

namespace  RIB.PRMetrics.Persistence
{
    /// <summary>
    /// Provides extension methods to configure the dependency injection (DI) container for the persistence layer.
    /// This class registers all the services, repositories, and common utilities used in the persistence layer.
    /// </summary>
    public static class ConfigureServices
    {
        /// <summary>
        /// Registers all persistence-related services with the dependency injection container.
        /// This includes the configuration of repositories, common utilities, and contexts for Azure DevOps interactions.
        /// </summary>
        /// <param name="services">The <see cref="IServiceCollection"/> used to register services.</param>
        /// <returns>The updated <see cref="IServiceCollection"/> with persistence-related services added.</returns>
        public static IServiceCollection AddInjectionPersistence(this IServiceCollection services)
        {
            // Registering singleton services which are created only once during the application's lifetime
            services.AddSingleton<AZDevopsClientCommon>(); // Common client for Azure DevOps API requests
            services.AddSingleton<AzureServiceContext>();  // Context for Azure Key Vault interaction
            services.AddSingleton<AzureDevopsUrlBuilder>(); // Utility for building Azure DevOps API URLs

            // Registering scoped services which are created once per request/operation
            services.AddScoped<IGitRepoRepository, GitRepoRepositories>(); // Git repository related operations
            services.AddScoped<IGitPullRequestRepository, GitPullRequestRepository>(); // Git pull request operations
            services.AddScoped<IGitProjectRepository, GitProjectRepositories>(); // Git project related operations
            services.AddScoped<IGitPullRequestThreadsRepository, GitPullRequestThreadsRepositories>();// Registers the repository for handling Git pull request threads operations
            services.AddScoped<IContributionsHierarchyQueryRepository, ContributionsHierarchyQueryRepository>();// Registers the repository for querying Git contributions hierarchy
            services.AddScoped<IGitPullRequestBuildTimeLineRepository, GitPullRequestBuildTimeLineRepository>(); // Registers the repository for handling Git pull request build timeline operations
            services.AddScoped<IIdentityPickerRepository, IdentityPickerRepository>(); 
            services.AddScoped<IUnitOfWork, UnitOfWork>(); // Unit of work pattern to coordinate repositories

            // Return the services collection for chaining
            return services;
        }
    }
}
