// Variables
@import '../../styles.scss';

/* Layout Container */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 94.5vh;
  height: 94.5vh; /* Set fixed height */
  overflow: hidden; /* Prevent scrolling */
  font-family: $font-family;
  background: {
    image: url("https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80");
    size: cover;
    position: center;
    attachment: fixed; /* Keep background fixed when content changes */
  }
}

/* Main */
.login-main {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: $padding-xl $padding-md;
  height: 100%; /* Changed from 100vh to 100% */
  overflow: auto; /* Allow scrolling only if needed */
}

/* Dialog Styling */
.login-dialog {
  width: 100%;
  max-width: 500px;
  box-shadow: $box-shadow;
  border-radius: $border-radius;
  overflow: hidden;

  .login-content {
    display: flex;
    flex-direction: column;
    background-color: $white;
  }
}

/* Logo and Header */
.login-header {
  background-color: var(--primary-color);
  padding: $padding-md;
  display: flex;
  justify-content: start;
  align-items: center;

  .logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .rib-logo {
      height: 30px;
      width: auto;
      margin-right: 5px;
    }

    .version {
      font-size: 1.5rem;
      font-weight: 500;
      color: $white;
      margin-left: $padding-sm;
    }
  }
}

/* Login Form */
.login-form {
  padding: $padding-lg $padding-xl $padding-xl;
  background-color: $white;
  font-family: $font-family;
}

/* Inputs & Dropdowns */
.full-width {
  width: 100%;
  margin-top: 15px;
}

.form-input {
  margin-bottom: 8px;
}

.mat-form-field {
  font-size: 14px;
}

// Mixin for padding
@mixin horizontal-padding($padding: 6px) {
  padding-left: $padding;
  padding-right: $padding;
}

// Angular Material Overrides
::ng-deep {
  // Select field styling
  .mat-mdc-select-value {
    @include horizontal-padding(6px);
  }

  .mat-mdc-form-field-infix {
    @include horizontal-padding(6px);
  }

  // Project and Repository specific styles
  .project-field, .repo-field {
    .mat-mdc-select-value {
      @include horizontal-padding(6px);
    }
  }

  .project-select, .repo-select {
    .mat-mdc-select-trigger {
      @include horizontal-padding(6px);
    }
  }

  // Dropdown styling
  .mat-mdc-option {
    padding: 4px !important;
    font-family: $font-family;
  }

  // Dropdown panel styling
  .mat-mdc-select-panel {
    background-color: $white !important;
    padding: $padding-sm 0;
  }

  // Ensure white background for all dropdown elements
  .cdk-overlay-container {
    .cdk-overlay-connected-position-bounding-box {
      .cdk-overlay-pane {
        background-color: $white;
      }
    }
  }

  .cdk-overlay-pane {
    background-color: $white;
  }

  .mat-mdc-select-panel-wrap {
    background-color: $white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .mat-mdc-menu-panel {
    background-color: $white !important;
  }

  .mat-mdc-option-text {
    padding-left: 4px;
  }

  // Hover and active states
  .mat-mdc-option {
    &:hover:not(.mdc-list-item--disabled),
    &.mat-mdc-option-active {
      background-color: rgba($primary-color, 0.1) !important;
    }
  }

  // Disable all transitions for select elements and dropdown menus
  .login-form {
    .mat-mdc-select,
    .mat-mdc-select-trigger,
    .mat-mdc-select-arrow-wrapper,
    .mat-mdc-select-arrow,
    .mat-mdc-option,
    .mat-mdc-select-panel,
    .cdk-overlay-pane,
    .cdk-overlay-container .cdk-overlay-connected-position-bounding-box .cdk-overlay-pane {
      transition: none !important;
      animation: none !important;
    }
  }
  
  // Disable animation for Angular Material overlay
  .cdk-overlay-container {
    .cdk-overlay-pane {
      transition: none !important;
      animation: none !important;
    }
  }
}

/* Login Info Section */
.login-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
  font-size: 13px;
  color: $dark-gray;

  .sso-option {
    display: flex;
    align-items: center;
    color: $primary-color;
    cursor: pointer;

    span {
      margin-right: 4px;
    }
  }
}

/* PAT Input Styling */
.pat-input {
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  color: grey !important;
  text-overflow: clip;
  padding-right: 20px; /* Add space for scrolling */

  // Hide scrollbars
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
}

// Angular Material overrides for PAT input
::ng-deep .mat-mdc-form-field .mat-mdc-input-element.pat-input {
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
}

/* Scrollable field indicator */
.form-input.full-width {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    right: 40px; /* Space for the visibility toggle */
    top: 0;
    bottom: 0;
    width: 20px;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.9));
    pointer-events: none;
    z-index: 1;
  }
}

/* Show the gradient indicator when content overflows */
.pat-input:not([value=""]):not(:focus) + .form-input.full-width::after {
  display: block;
}

/* Messages */
%message-base {
  margin: $padding-sm 0;
  font-size: 13px;
  padding: $padding-sm $padding-md;
  border-radius: 2px;
  display: flex;
  align-items: center;
}

.error-message {
  @extend %message-base;
  color: $error-color;
  background-color: rgba($error-color, 0.1);
}

.info-message {
  @extend %message-base;
  color: #555;
  background-color: rgba(0, 0, 0, 0.05);
}

/* Button Styling */
// Mixin for button transitions
@mixin button-transition($duration: 0.2s) {
  transition: all $duration ease;
}

// Button group
.button-group {
  display: flex;
  justify-content: space-between;
  margin-top: 25px;
  gap: 10px;
}

// Get data button
.get-data-button {
  background: linear-gradient(45deg, $primary-hover, $secondary-color);
  color: $white !important;
  @include button-transition(0.3s);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: $border-radius;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: capitalize;

  &:hover {
    background: linear-gradient(45deg, $secondary-color, $primary-hover);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  mat-icon {
    margin-right: 4px;
  }
}

// Login header main
.login-header-main {
  background-color: var(--primary-color);
  padding: 2px;
  display: flex;
  color: $white;
  justify-content: start;
  align-items: center;
}

// Login and validate buttons
.login-button, .validate-button {
  width: 100%;
  border-radius: 0;
  padding: $padding-sm $padding-md;
  font-weight: 400;
  font-size: 14px;
  background-color: $primary-color;
  color: $white;
  @include button-transition;
  margin-top: 12px;
  text-transform: none;
  letter-spacing: 0.5px;
  border: 1px solid var(--fade-black);

  &:hover {
    background-color: $primary-hover;
    color: $white
  }
}

// General button styling
button[mat-raised-button] {
  border-radius: 2px;
  font-weight: 500;
  @include button-transition;
}

/* Version number */
.version-number {
  position: absolute;
  bottom: 10px;
  right: 10px;
  font-size: 12px;
  color: #999;
}

/* Footer */
.login-footer {
  padding: $padding-sm;
  text-align: left;
  font-size: 12px;
  color: $dark-gray;
  background-color: $light-gray;
  border-top: 1px solid $medium-gray;
}

/* Responsive */
@media (max-width: 500px) {
  .login-dialog {
    max-width: 100%;
  }

  .login-form {
    padding: $padding-lg $padding-md;
  }

  .button-group {
    flex-direction: column;
  }
}
