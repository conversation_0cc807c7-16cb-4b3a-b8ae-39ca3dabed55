import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialogModule,
} from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTabsModule } from '@angular/material/tabs';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import {
  BuildTimelineRecord,
  GitPullRequestBuildTimeLineResponse,
} from '../../models/pr-interfaces.model';
import { ApiUrlService } from '../../services/api-url.service';
import { ConfigService } from '../../services/config.service';
import { firstValueFrom } from 'rxjs';
import AnsiToHtml from 'ansi-to-html';

@Component({
  selector: 'app-build-logs-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatProgressSpinnerModule,
    MatTabsModule,
    MatButtonModule,
    MatIconModule,
  ],
  template: `
    <div class="build-logs-dialog">
      <h2 mat-dialog-title>
        <span>Pipeline Details</span>
        <button mat-icon-button class="close-button" (click)="close()">
          <mat-icon>close</mat-icon>
        </button>
      </h2>
      <mat-dialog-content>
        <!-- Error section for build errors from issues array -->
        <div *ngIf="buildErrors.length > 0" class="build-error-section">
          <mat-icon class="error-icon">error</mat-icon>
          <h3>Build Errors</h3>
          <div *ngFor="let err of buildErrors" class="build-error-message">
            <span>{{ err.message }}</span>
          </div>
        </div>
        <ng-container
          *ngIf="
            data.pipelinePolicies && data.pipelinePolicies.length;
            else buildTimelineBlock
          "
        >
          <h3>All Pipeline Policies</h3>
          <table class="timeline-table">
            <thead>
              <tr>
                <th>Policy Name</th>
                <th>Status</th>
                <th>Started</th>
                <th>Finished</th>
                <th>Details</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let policy of data.pipelinePolicies">
                <td>{{ policy.displayName }}</td>
                <td>
                  <span
                    class="status-badge"
                    [ngClass]="getPolicyStatusClass(policy.displayStatus)"
                  >
                    <mat-icon class="status-dot">{{
                      getPolicyStatusIcon(policy.displayStatus)
                    }}</mat-icon>
                    {{
                      policy.displayText ||
                        policy.statusText ||
                        policy.displayStatus
                    }}
                  </span>
                </td>
                <td>
                  {{
                    policy.startedOn
                      ? (policy.startedOn | date : 'MMM d, y, h:mm:ss a')
                      : '-'
                  }}
                </td>
                <td>
                  {{
                    policy.completedOn
                      ? (policy.completedOn | date : 'MMM d, y, h:mm:ss a')
                      : '-'
                  }}
                </td>
                <td>
                  <span *ngIf="policy.buildId">
                    <a
                      href="#"
                      (click)="showBuildTimeline(policy.buildId, $event)"
                      class="view-timeline-link"
                      >View Timeline</a
                    >
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </ng-container>
        <ng-template #buildTimelineBlock>
          <div *ngIf="isLoading" class="loading-container">
            <mat-spinner diameter="40"></mat-spinner>
            <p>Loading build timeline...</p>
          </div>
          <div *ngIf="!isLoading && error" class="error-container">
            <mat-icon class="error-icon">error</mat-icon>
            <h3>Error Loading Build Timeline</h3>
            <p>{{ error }}</p>
          </div>
          <div
            *ngIf="!isLoading && !error && records.length === 0"
            class="no-data-container"
          >
            <mat-icon>info</mat-icon>
            <p>No build timeline records found.</p>
          </div>
          <div
            *ngIf="!isLoading && !error && records.length > 0"
            class="build-timeline-container"
          >
            <!-- Log Content Section -->
            <div *ngIf="logContent" class="log-content">
              <h3>Log Details</h3>
              <div class="log-content-wrapper">
                <div [innerHTML]="formattedLogContent"></div>
              </div>
            </div>
            <!-- Build Timeline Summary -->
            <div *ngIf="!logContent" class="timeline-summary">
              <h3>Build Timeline</h3>
              <table class="timeline-table">
                <thead>
                  <tr>
                    <th>Task</th>
                    <th>Status</th>
                    <th>Duration</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="let record of taskRecords"
                    [ngClass]="getRecordClass(record)"
                  >
                    <td>{{ record.name }}</td>
                    <td>
                      <span
                        class="status-badge"
                        [ngClass]="'status-' + record.result"
                      >
                        {{ record.result }}
                      </span>
                    </td>
                    <td>{{ getDuration(record) }}</td>
                    <td>
                      <button
                        *ngIf="record.log"
                        mat-button
                        color="primary"
                        (click)="loadLogContent(record.log.url)"
                      >
                        View Log
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </ng-template>
      </mat-dialog-content>
      <mat-dialog-actions align="end">
        <button
          *ngIf="logContent"
          mat-raised-button
          color="accent"
          (click)="clearLogContent()"
        >
          Back to Summary
        </button>
        <button mat-raised-button color="primary" (click)="close()">
          Close
        </button>
      </mat-dialog-actions>
    </div>
  `,
  styles: [
    `
      .build-logs-dialog {
        width: 1000px;
        max-width: 95vw;
        max-height: 95vh;
      }

      h2 {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }

      .build-error-section {
        background: #fff3f3;
        border-left: 5px solid #e53935;
        color: #b71c1c;
        padding: 16px 24px;
        margin-bottom: 20px;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(229, 57, 53, 0.08);
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }

      .build-error-section .error-icon {
        color: #e53935;
        font-size: 32px;
        margin-bottom: 8px;
      }

      .build-error-message {
        margin: 4px 0 0 0;
        font-size: 15px;
        font-weight: 500;
        padding-left: 8px;
      }

      .view-timeline-link {
        color: #1976d2;
        cursor: pointer;
        text-decoration: underline;
        font-weight: 500;
      }

      .mat-dialog-actions {
        margin-top: 16px;
      }

      .mat-dialog-actions button {
        margin-left: 8px;
        min-width: 100px;
      }

      .loading-container,
      .error-container,
      .no-data-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 32px;
        text-align: center;
      }

      .error-icon {
        color: #f44336;
        font-size: 48px;
        height: 48px;
        width: 48px;
        margin-bottom: 16px;
      }

      .build-timeline-container {
        padding: 8px;
      }

      .log-content {
        background-color: #f5f5f5;
        padding: 16px;
        border-radius: 4px;
        font-family: monospace;
        white-space: pre-wrap;
        overflow-x: auto;
      }

      .log-content-wrapper {
        max-height: 600px;
        overflow-y: auto;
        background-color: #2d2d2d;
        color: #f8f8f8;
        padding: 16px;
        border-radius: 4px;
      }

      .timeline-table {
        width: 100%;
        border-collapse: collapse;
      }

      .timeline-table th,
      .timeline-table td {
        padding: 12px 8px;
        text-align: left;
        border-bottom: 1px solid #e0e0e0;
      }

      .timeline-table th {
        font-weight: 500;
        color: #757575;
        background-color: #f5f5f5;
      }

      .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        text-transform: uppercase;
        font-weight: 500;
        display: inline-block;
      }

      .status-success {
        background-color: rgba(76, 175, 80, 0.1);
        color: #388e3c;
      }

      .status-failed {
        background-color: rgba(244, 67, 54, 0.1);
        color: #d32f2f;
      }

      .status-succeededWithIssues {
        background-color: rgba(255, 152, 0, 0.1);
        color: #f57c00;
      }

      .status-skipped {
        background-color: rgba(158, 158, 158, 0.1);
        color: #616161;
      }

      .status-dot {
        font-size: 16px;
        vertical-align: middle;
        margin-right: 4px;
      }

      .status-pending {
        color: #ffa000;
      }

      .status-unknown {
        color: #9e9e9e;
      }
    `,
  ],
})
export class BuildLogsDialogComponent implements OnInit {
  isLoading = true;
  error: string | null = null;
  records: BuildTimelineRecord[] = [];

  // For displaying log content
  logContent: string | null = null;
  formattedLogContent: string | null = null;

  // Converter for converting ANSI escape sequences to HTML
  private converter = new AnsiToHtml();
  constructor(
    private dialogRef: MatDialogRef<BuildLogsDialogComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: {
      patToken: string;
      projectId: string;
      buildId: number;
      pipelinePolicies?: any[];
    },
    private http: HttpClient,
    private apiUrlService: ApiUrlService,
    private configService: ConfigService
  ) {}

  selectedBuildId: number | null = null;
  buildErrors: { message: string }[] = [];

  async ngOnInit(): Promise<void> {
    // If a buildId is provided, load its timeline
    if (this.data.buildId) {
      this.selectedBuildId = this.data.buildId;
      await this.loadBuildTimeline(this.selectedBuildId);
    }
  }

  async showBuildTimeline(buildId: number, event: Event): Promise<void> {
    event.preventDefault();
    this.selectedBuildId = buildId;
    await this.loadBuildTimeline(buildId);
    // Scroll to build errors if present
    setTimeout(() => {
      const errorSection = document.querySelector('.build-error-section');
      if (errorSection) {
        errorSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  }

  async loadBuildTimeline(buildId?: number): Promise<void> {
    this.isLoading = true;
    this.error = null;
    this.buildErrors = [];
    try {
      const url = this.apiUrlService.getGitPullRequestBuildTimeLineUrl(
        this.data.patToken,
        this.data.projectId,
        buildId || this.data.buildId
      );
      const response = await firstValueFrom(
        this.http.get<GitPullRequestBuildTimeLineResponse>(url)
      );
      if (response.succcess) {
        this.records = response.data.records;
        // Collect all error issues from all records
        this.buildErrors = this.records
          .flatMap((r) => r.issues || [])
          .filter((issue) => issue.type === 'error');
        // Sort records by start time
        this.records.sort(
          (a, b) =>
            new Date(a.startTime).getTime() - new Date(b.startTime).getTime()
        );
      } else {
        this.error = response.message || 'Failed to load build timeline';
      }
    } catch (err) {
      console.error('Error loading build timeline:', err);
      this.error =
        'An error occurred while loading the build timeline. Please try again.';
    } finally {
      this.isLoading = false;
    }
  }

  async loadLogContent(logUrl: string): Promise<void> {
    this.isLoading = true;

    try {
      const response = await firstValueFrom(
        this.http.get(logUrl, { responseType: 'text' })
      );
      this.logContent = response;
      this.formattedLogContent = this.converter.toHtml(response);
    } catch (err) {
      console.error('Error loading log content:', err);
      this.error =
        'An error occurred while loading the log content. Please try again.';
    } finally {
      this.isLoading = false;
    }
  }

  clearLogContent(): void {
    this.logContent = null;
    this.formattedLogContent = null;
  }

  close(): void {
    this.dialogRef.close();
  }

  get hasErrors(): boolean {
    return this.errorRecords.length > 0;
  }

  get hasWarnings(): boolean {
    return this.warningRecords.length > 0;
  }

  get errorRecords(): BuildTimelineRecord[] {
    return this.records.filter(
      (record) =>
        record.errorCount > 0 ||
        record.issues.some((issue) => issue.type === 'error') ||
        record.result === 'failed'
    );
  }

  get warningRecords(): BuildTimelineRecord[] {
    return this.records.filter(
      (record) =>
        record.warningCount > 0 ||
        record.issues.some((issue) => issue.type === 'warning') ||
        record.result === 'succeededWithIssues'
    );
  }

  get taskRecords(): BuildTimelineRecord[] {
    return this.records.filter((record) => record.type === 'Task');
  }

  getRecordClass(record: BuildTimelineRecord): string {
    return `record-${record.result}`;
  }

  getDuration(record: BuildTimelineRecord): string {
    const start = new Date(record.startTime).getTime();
    const end = new Date(record.finishTime).getTime();
    const durationMs = end - start;

    const seconds = Math.floor(durationMs / 1000) % 60;
    const minutes = Math.floor(durationMs / (1000 * 60)) % 60;
    const hours = Math.floor(durationMs / (1000 * 60 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  }
  getPolicyStatusIcon(status: number): string {
    switch (status) {
      case this.configService.PIPELINE_STATUS.IN_PROGRESS:
        return 'hourglass_empty'; // In progress
      case this.configService.PIPELINE_STATUS.SUCCEEDED:
        return 'check_circle'; // Success
      case this.configService.PIPELINE_STATUS.FAILED:
        return 'cancel'; // Failed
      default:
        return 'help_outline'; // Unknown
    }
  }
  getPolicyStatusClass(status: number): string {
    if (status === this.configService.PIPELINE_STATUS.SUCCEEDED) return 'status-success';
    if (status === this.configService.PIPELINE_STATUS.FAILED) return 'status-failed';
    if (status === this.configService.PIPELINE_STATUS.IN_PROGRESS) return 'status-pending';
    return 'status-unknown';
  }
}
