﻿
using RIB.PRMetrics.Domain.Entities.JobStatus;

namespace RIB.PRMetrics.Domain.Entities
{
    public class ProcessProgress
    {
        public Guid Uuid { get; set; }

        public int Percentage { get; set; } = 0;

        public string Status { get; set; } = "Pending";

        public string FileName { get; set; } = "";

        public string FileUrl { get; set; }

        public string JobStatus { get; set; }= "Pending";


    }
}
