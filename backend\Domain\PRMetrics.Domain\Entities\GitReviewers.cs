﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities
{
    /// <summary>
    /// Represents a reviewer for a Git pull request with voting and required status.
    /// </summary>
    public class ReviewerDto
    {
        /// <summary>
        /// Gets or sets the unique identifier of the reviewer.
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the display name of the reviewer.
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// Gets or sets the vote of the reviewer.
        /// A typical value can be 1 (approve), 0 (neutral), or -1 (reject).
        /// </summary>
        public int Vote { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the reviewer is required.
        /// </summary>
        public bool IsRequired { get; set; }
    }
}
