﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities.IdentityPicker
{
    /// <summary>
    /// Represents a result item returned from the Azure DevOps Identity Picker API.
    /// </summary>
    public class ResultsItem
    {
        /// <summary>
        /// Gets or sets the token representing the query that was executed.
        /// </summary>
        public string QueryToken { get; set; }

        /// <summary>
        /// Gets or sets the list of identities returned as a result of the query.
        /// </summary>
        public List<IdentitiesItem> Identities { get; set; }

        /// <summary>
        /// Gets or sets the paging token used for fetching additional results if available.
        /// </summary>
        public string PagingToken { get; set; }
    }
}

