﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using MediatR;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestStatusCount
{
    /// <summary>
    /// Handles the GetGitPullRequestStatusCountQuery to fetch the pull request status count.
    /// </summary>
    public class GetGitPullRequestStatusCountHandler : IRequestHandler<GetGitPullRequestStatusCountQuery, BaseReponseGeneric<GitPullRequestStatusCountDto>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetGitPullRequestStatusCountHandler"/> class.
        /// </summary>
        /// <param name="unitOfWork">Unit of Work interface for data access.</param>
        /// <param name="mapper">AutoMapper instance for mapping entities to DTOs.</param>
        /// <exception cref="ArgumentNullException">Thrown when dependencies are null.</exception>
        public GetGitPullRequestStatusCountHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        /// <summary>
        /// Handles the request to get the pull request status count.
        /// </summary>
        /// <param name="request">The query request.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>A response containing the Git pull request status count DTO.</returns>
        public async Task<BaseReponseGeneric<GitPullRequestStatusCountDto>> Handle(GetGitPullRequestStatusCountQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<GitPullRequestStatusCountDto>
            {
                Succcess = false // Default to false in case of errors
            };

            try
            {
                var gitPullRequestStatusCount = await _unitOfWork.GitPullRequestsRepository.GetGitPullRequestStatusCountAsync(request, request.Status);

                if (gitPullRequestStatusCount != null)
                {
                    response.Data = _mapper.Map<GitPullRequestStatusCountDto>(gitPullRequestStatusCount);
                    response.Succcess = true;
                    response.Message = "Git Pull Request Status Count fetch succeeded!";
                }
                else
                {
                    response.Message = "No pull request status count found for the given query.";
                }
            }
            catch (Exception ex)
            {
                response.Message = $"An error occurred while fetching pull requests status count: {ex.Message}";
                // Optionally log exception here, e.g. _logger.LogError(ex, "Failed to fetch pull requests status count");
            }

            return response;
        }
    }
}
