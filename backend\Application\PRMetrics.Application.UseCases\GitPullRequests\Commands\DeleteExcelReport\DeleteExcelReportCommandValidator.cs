﻿using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.DeleteExcelReport
{
    public class TakeActionExcelReportCommandValidator : AbstractValidator<DeleteExcelReportCommand>
    {
        public TakeActionExcelReportCommandValidator()
        {
            // Validate that Uuid is not empty or null
            RuleFor(x => x.Uuid)
                .NotEmpty()
                .WithMessage("Uuid is required.");

            
        }
    }
}
