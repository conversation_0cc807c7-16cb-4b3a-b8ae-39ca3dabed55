﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using MediatR;
using RIB.PRMetrics.Application.Dto.Contributions.HierarchyQuery;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;

namespace RIB.PRMetrics.Application.UseCases.ContributionsHierarchyQuery.Queries.GetContributionsHierarchy
{
    /// <summary>
    /// Handles the GetContributionsHierarchyQuery request.
    /// Responsible for retrieving the contributions hierarchy for a pull request,
    /// mapping it to a DTO, and wrapping it in a standardized response.
    /// </summary>
    public class GetContributionsHierarchyHandler : IRequestHandler<GetContributionsHierarchyQuery, BaseReponseGeneric<QueryResponseDto>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        /// <summary>
        /// Constructor to inject required dependencies.
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance to access repositories.</param>
        /// <param name="mapper">AutoMapper instance for mapping domain models to DTOs.</param>
        public GetContributionsHierarchyHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        /// <summary>
        /// Handles the GetContributionsHierarchyQuery.
        /// Retrieves pull request contribution hierarchy data and maps it to a DTO.
        /// </summary>
        /// <param name="request">The query object containing request parameters.</param>
        /// <param name="cancellationToken">Cancellation token for the async operation.</param>
        /// <returns>A standardized response containing the hierarchy data as a DTO.</returns>
        public async Task<BaseReponseGeneric<QueryResponseDto>> Handle(GetContributionsHierarchyQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<QueryResponseDto>();

            try
            {
                // Fetch data from the repository
                var queryResponse = await _unitOfWork.ContributionsHierarchyQueryRepository
                    .GetGitPullRequestContributionsHierarchyQueryAsync(
                        request.PullRequestId,
                        request.RepositoryId,
                        request.ProjectId,
                        request.PATToken
                    );

                if (queryResponse is not null)
                {
                    // Map domain entity to DTO
                    response.Data = _mapper.Map<QueryResponseDto>(queryResponse);
                    response.Succcess = true;
                    response.Message = "Git pull request contribution hierarchy fetched successfully.";
                }
                else
                {
                    response.Message = "No data found for the provided parameters.";
                }
            }
            catch (Exception ex)
            {
                // Capture and return exception message
                response.Message = $"An error occurred: {ex.Message}";
            }

            return response;
        }
    }
}
