import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ConfigService {
  // API URL configurations from environment
  readonly API_BASE_URL = environment.apiUrl;
  
  // Work hours configuration for time calculations
  readonly WORK_HOURS = {
    START_HOUR: 9,
    START_MINUTE: 30,
    END_HOUR: 18,
    END_MINUTE: 30,
    BREAK_START_HOUR: 13,
    BREAK_END_HOUR: 14,
    WORK_DAY_HOURS: 8,
    MS_PER_HOUR: 3600000,
  };
  
  // Cache duration settings
  readonly CACHE_DURATION = {
    PR_STATUS_COUNT: 5 * 60 * 1000, // 5 minutes
    PR_DATA: 1 * 60 * 1000, // 1 minute
  };
  
  // UI Configuration
  readonly BATCH_SIZE = 20;
  readonly API_DEBOUNCE_TIME = 300; // ms to prevent duplicate API calls
  readonly SEARCH_DEBOUNCE_TIME = 400; // ms for search input
  
  // PR Status options for dropdown
  readonly PR_STATUS_OPTIONS = [
    { value: 'all', label: 'All' },
    { value: 'active', label: 'Active' },
    { value: 'completed', label: 'Completed' },
    { value: 'abandoned', label: 'Abandoned' },
  ];
  
  // Local storage keys
  readonly STORAGE_KEYS = {
    CREDENTIALS: 'pr_metrics_credentials'
  };
  
  // API Endpoints
  readonly API_ENDPOINTS = {
    PULL_REQUESTS: `${this.API_BASE_URL}/GitPullRequests/GetGitPullRequest`,
    PULL_REQUEST_BY_ID: `${this.API_BASE_URL}/GitPullRequests/GetGitPullRequestById`,
    COMMENT_THREADS: `${this.API_BASE_URL}/GitPullRequestThreads/GetGitPullRequestThreads`,
    HIERARCHY_QUERY: `${this.API_BASE_URL}/AzureDevOps/GetContributionsHierarchyQuery`,
    PROJECTS: `${this.API_BASE_URL}/AzureDevOps/GetProjects`,
    REPOSITORIES: `${this.API_BASE_URL}/AzureDevOps/GetRepositories`,
  };
  
  // Pipeline status codes
  readonly PIPELINE_STATUS = {
    UNKNOWN: 0,
    IN_PROGRESS: 1,
    SUCCEEDED: 2,
    FAILED: 3,
  };
  
  // Reviewer vote values
  readonly REVIEWER_VOTE = {
    APPROVED: 10,
    APPROVED_WITH_SUGGESTIONS: 5,
    NO_VOTE: 0,
    WAITING_FOR_AUTHOR: -5,
    REJECTED: -10
  };
  
  constructor() { }
  
  // Helper methods for API endpoints
  getPullRequestsEndpoint(): string {
    return this.API_ENDPOINTS.PULL_REQUESTS;
  }
  
  getPullRequestByIdEndpoint(): string {
    return this.API_ENDPOINTS.PULL_REQUEST_BY_ID;
  }
  
  getCommentThreadsEndpoint(): string {
    return this.API_ENDPOINTS.COMMENT_THREADS;
  }
  
  getContributionsHierarchyQueryEndpoint(): string {
    return this.API_ENDPOINTS.HIERARCHY_QUERY;
  }
  
  getProjectsEndpoint(): string {
    return this.API_ENDPOINTS.PROJECTS;
  }
  
  getRepositoriesEndpoint(): string {
    return this.API_ENDPOINTS.REPOSITORIES;
  }
}
