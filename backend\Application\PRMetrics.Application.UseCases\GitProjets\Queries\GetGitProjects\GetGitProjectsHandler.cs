﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using MediatR;
using   RIB.PRMetrics.Application.Dto;
using   RIB.PRMetrics.Application.Interface.Persistence;
using   RIB.PRMetrics.Application.UseCases.Commons.Bases;

namespace  RIB.PRMetrics.Application.UseCases.GitProjets.Queries.GetGitProjets
{
    /// <summary>
    /// Handler for processing GetGitProjectsQuery using MediatR.
    /// Retrieves a list of Git projects from the repository and maps them to DTOs.
    /// </summary>
    public class GetGitProjectsHandler : IRequestHandler<GetGitProjectsQuery, BaseReponseGeneric<List<GitProjectDto>>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        /// <summary>
        /// Constructor to inject required dependencies.
        /// </summary>
        public GetGitProjectsHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        /// <summary>
        /// Handles the GetGitProjectsQuery request.
        /// Calls the repository to get Git projects and maps the result to DTOs.
        /// </summary>
        public async Task<BaseReponseGeneric<List<GitProjectDto>>> Handle(GetGitProjectsQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<List<GitProjectDto>>();

            try
            {
                // Retrieve list of Git projects using the provided PAT token
                var gitProjects = await _unitOfWork.GitProjectRepository.GetGitProjectsAsync(request.PATToken);

                if (gitProjects is not null)
                {
                    // Map domain entities to DTOs
                    response.Data = _mapper.Map<List<GitProjectDto>>(gitProjects);
                    response.Succcess = true;
                    response.Message = "Git Projects list fetch succeed!";
                }
            }
            catch (Exception ex)
            {
                response.Message = ex.Message;
            }

            return response;
        }
    }
}
