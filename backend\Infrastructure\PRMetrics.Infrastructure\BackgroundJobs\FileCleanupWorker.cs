﻿using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using RIB.PRMetrics.Application.Interface.Persistence;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace RIB.PRMetrics.Infrastructure.BackgroundJobs
{
    public class FileCleanupWorker : BackgroundService
    {
        private readonly ILogger<FileCleanupWorker> _logger;
        private readonly IDeleteJobQueue _queue;
        private readonly IMemoryCache _cache;

        // Optional: Limit max parallel deletes
        private readonly SemaphoreSlim _concurrencyLimiter = new SemaphoreSlim(3); // Adjust based on I/O capacity

        public FileCleanupWorker(IDeleteJobQueue queue, IMemoryCache cache, ILogger<FileCleanupWorker> logger)
        {
            _queue = queue;
            _cache = cache;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("FileCleanupWorker started.");

            while (!stoppingToken.IsCancellationRequested)
            {
                if (_queue.TryDequeue(out var job))
                {
                    _ = Task.Run(async () =>
                    {
                        await _concurrencyLimiter.WaitAsync(stoppingToken);
                        try
                        {
                            if (!string.IsNullOrEmpty(job.FileUrl) && File.Exists(job.FileUrl))
                            {
                                File.Delete(job.FileUrl);
                                _logger.LogInformation("Deleted file: {Path}", job.FileUrl);
                            }
                            else
                            {
                                _logger.LogWarning("File not found or path empty: {Path}", job.FileUrl);
                            }

                            _cache.Remove(job.Uuid.ToString());
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error deleting file {Path}", job.FileUrl);
                        }
                        finally
                        {
                            _concurrencyLimiter.Release();
                        }
                    }, stoppingToken);
                }
                else
                {
                    await Task.Delay(1000, stoppingToken); // Delay before next check
                }
            }

            _logger.LogInformation("FileCleanupWorker stopped.");
        }
    }
}
