﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="FluentValidation" Version="11.11.0" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.3.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Domain\PRMetrics.Domain\RIB.PRMetrics.Domain.csproj" />
    <ProjectReference Include="..\PRMetrics.Dto\RIB.PRMetrics.Application.Dto.csproj" />
    <ProjectReference Include="..\PRMetrics.Interface\RIB.PRMetrics.Application.Interface.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="ContributionsHierarchyQuery\Commands\" />
    <Folder Include="GitPullRequestBuildTimeLine\Commands\" />
    <Folder Include="GitProjets\Commands\" />
    <Folder Include="GitPullRequestThreads\Commands\" />
    <Folder Include="GitRepositories\Commands\" />
    <Folder Include="IdentityPicker\Commands\" />
  </ItemGroup>

</Project>
