﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace  RIB.PRMetrics.Application.UseCases.Commons.Bases
{
    /// <summary>
    /// Represents a generic response structure used for returning data and error information in API responses.
    /// </summary>
    /// <typeparam name="T">The type of data that will be returned in the response.</typeparam>
    public class BaseReponseGeneric<T>
    {
        /// <summary>
        /// Gets or sets a value indicating whether the request was successful.
        /// </summary>
        public bool Succcess { get; set; }

        /// <summary>
        /// Gets or sets the data returned in the response. It can be any type, depending on the response.
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// Gets or sets a message providing additional details about the response, such as success or failure reasons.
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// Gets or sets a list of errors if any occurred during the request. It contains error details for failed operations.
        /// </summary>
        public IEnumerable<BaseError>? Errors { get; set; }
    }
}
