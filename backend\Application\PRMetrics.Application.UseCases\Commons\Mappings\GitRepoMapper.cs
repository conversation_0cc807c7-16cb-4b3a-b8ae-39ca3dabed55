﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using   RIB.PRMetrics.Application.Dto;
using   RIB.PRMetrics.Domain;
using RIB.PRMetrics.Domain.Entities;

namespace  RIB.PRMetrics.Application.UseCases.Commons.Mappings
{
    /// <summary>
    /// AutoMapper Profile for mapping between GitRepository, GitRepositoryDto, and GitProjectDto.
    /// </summary>
    public class GitRepoMapper : Profile
    {
        /// <summary>
        /// Constructor that defines the mapping configurations for GitRepository and related entities.
        /// </summary>
        public GitRepoMapper()
        {
            // Mapping configuration between GitRepository and GitRepositoryDto
            CreateMap<GitRepository, GitRepositoryDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))  // Mapping 'Id' property from GitRepository to GitRepositoryDto
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))  // Mapping 'Name' property
                .ForMember(dest => dest.Project, opt => opt.MapFrom(src => src.Project))  // Mapping 'Project' from GitRepository to GitProjectDto
                .ForMember(dest => dest.Url, opt => opt.MapFrom(src => src.Url))  // Mapping 'Url' property
                .ForMember(dest => dest.DefaultBranch, opt => opt.MapFrom(src => src.DefaultBranch))  // Mapping 'DefaultBranch' property
                .ForMember(dest => dest.SshUrl, opt => opt.MapFrom(src => src.SshUrl))  // Mapping 'SshUrl' property
                .ForMember(dest => dest.WebUrl, opt => opt.MapFrom(src => src.WebUrl))  // Mapping 'WebUrl' property
                .ForMember(dest => dest.IsDisabled, opt => opt.MapFrom(src => src.IsDisabled));  // Mapping 'IsDisabled' property

            // Create reverse mapping (from GitRepositoryDto to GitRepository)
            CreateMap<GitRepository, GitRepositoryDto>().ReverseMap();

            // Mapping between GitProject and GitProjectDto
            CreateMap<GitProject, GitProjectDto>();  // Ensures that GitProject is properly mapped to GitProjectDto
        }
    }
}
