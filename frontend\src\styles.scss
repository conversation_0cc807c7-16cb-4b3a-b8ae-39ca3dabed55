/* You can add global styles to this file, and also import other style files */
// Global variables
$primary-color: #0068b3;
$primary-hover: #0078d4;
$secondary-color: #106ebe;
$white: #ffffff;
$light-gray: #f5f5f5;
$medium-gray: #eee;
$dark-gray: #666;
$error-color: #d32f2f;
$box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
$border-radius: 4px;
$padding-sm: 8px;
$padding-md: 16px;
$padding-lg: 20px;
$padding-xl: 30px;
$header-bg: #f8fafc;
$font-family: 'Source Sans Pro', 'Meiryo UI', 'Microsoft YaHei', 'STHeiti', 'Segoe UI', 'Roboto', sans-serif;

:root {
  --primary-color: #0067b1;
  --fade-black: #b6b6b6;
  --fade-blue: #bbcddb;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

  font-family: 'Source Sans Pro', 'Meiryo UI', 'Microsoft YaHei', 'STHeiti', 'Segoe UI', 'Roboto', sans-serif;
}

body {
  margin: 0;
}

html,
body {
  height: 100%;
}

// Remove global transition - animations will be applied selectively where needed
* {
  transition: none;
}

// Global styles for Material components
.mat-mdc-tooltip {
  background-color: $white !important;
  color: $dark-gray !important;
  font-size: 12px !important;
  padding: $padding-sm $padding-md !important;
  border-radius: $border-radius !important;
  box-shadow: $box-shadow !important;
  border: 1px solid $medium-gray !important;
  max-width: 300px !important;
}

// Global styles for dropdowns
.mat-mdc-select-panel {
  background-color: $white !important;
  border-radius: $border-radius !important;
  box-shadow: $box-shadow !important;
}

.mat-mdc-option {
  background-color: $white !important;

  &:hover:not(.mdc-list-item--disabled) {
    background-color: rgba($primary-color, 0.05) !important;
  }

  &.mat-mdc-option-active {
    background-color: rgba($primary-color, 0.1) !important;
  }
}

// Ensure white background for all overlay elements
.cdk-overlay-container {
  .cdk-overlay-pane {
    background-color: $white;

    .mat-mdc-select-panel {
      background-color: $white !important;
    }

    .mat-mdc-menu-panel {
      background-color: $white !important;
    }
  }
}

::ng-deep {

  // Style for Material select dropdown
  .mat-mdc-select-panel {
    background-color: white !important;
    border-radius: 4px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    padding: 8px 0 !important; // Add vertical padding

    // Add specific styling for paginator dropdown
    &.mat-mdc-select-panel-above,
    &.mat-mdc-select-panel-below {
      margin-left: 4px !important;
      margin-right: 4px !important;
    }
  }

  // Style for Material option items
  .mat-mdc-option {
    background-color: white !important;
    padding-left: 12px !important;
    padding-right: 12px !important;

    &:hover:not(.mdc-list-item--disabled) {
      background-color: rgba(0, 104, 179, 0.05) !important;
    }

    &.mat-mdc-option-active {
      background-color: rgba(0, 104, 179, 0.1) !important;
    }

    // Add padding to the option text
    .mdc-list-item__primary-text {
      padding-left: 4px !important;
      padding-right: 4px !important;
    }
  }

  // Style for Material tooltip
  .mat-mdc-tooltip {
    background-color: white !important;
    color: var(--text-color) !important;
    font-size: 12px !important;
    padding: 8px 12px !important;
    border-radius: 4px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid var(--border-color) !important;
    max-width: 300px !important;
  }

  // Style for paginator dropdown
  .mat-mdc-paginator-page-size-select {
    .mat-mdc-select-value {
      color: var(--text-color) !important;
      padding-left: 2px !important;
      padding-right: 2px !important;
    }

    // Add padding to the form field
    .mat-mdc-form-field-infix {
      padding-left: 2px !important;
      padding-right: 2px !important;
    }

    // Style the dropdown trigger
    .mat-mdc-select-trigger {
      padding-left: 2px !important;
      padding-right: 2px !important;
    }

    // Add padding to the select arrow
    .mat-mdc-select-arrow-wrapper {
      padding-right: 2px !important;
    }
  }

  // Style for the paginator items per page label
  .mat-mdc-paginator-page-size-label {
    margin-right: 2px !important;
  }

  // Style for overlay container
  .cdk-overlay-container {
    .cdk-overlay-pane {
      .mat-mdc-select-panel {
        background-color: white !important;
      }
    }
  }

  .mat-mdc-select-value {
    margin-left: 10px !important;
  }

  .mat-mdc-select-arrow {
    margin-right: 10px !important;
  }
}

// Loader styles (replaces mat-spinner with a modern spinner)
.loader-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  min-height: 80px;
}

.loader {
  width: 4rem;
  height: 4rem;
  border: 0.2rem solid #e5e7eb; // Tailwind's gray-200
  border-top: 0.2rem solid #3b82f6; // Tailwind's blue-500
  border-radius: 9999px;
  animation: loader-spin 1s linear infinite;
}

@keyframes loader-spin {
  to {
    transform: rotate(360deg);
  }
}

/* Global scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #bbb;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #999;
}