import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, Http<PERSON><PERSON><PERSON>, HttpResponse } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';

interface CacheEntry {
  response: HttpResponse<any>;
  timestamp: number;
}

@Injectable()
export class HttpCacheInterceptor implements HttpInterceptor {
  private cache = new Map<string, CacheEntry>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_CACHE_SIZE = 100;

  // URLs that should be cached
  private readonly CACHEABLE_URLS = [
    '/User/GetCurrentUser',
    '/GitRepositories/GetGitRepositories',
    '/IdentityPicker/GetIdentities'
  ];

  // URLs that should never be cached
  private readonly NON_CACHEABLE_URLS = [
    '/GitPullRequests/GetGitPullRequest',
    '/export',
    '/download'
  ];

  intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<any> {
    // Only cache GET requests
    if (req.method !== 'GET') {
      return next.handle(req);
    }

    // Check if URL should be cached
    if (!this.shouldCache(req.url)) {
      return next.handle(req);
    }

    const cacheKey = this.getCacheKey(req);
    const cachedResponse = this.getFromCache(cacheKey);

    if (cachedResponse) {
      console.log(`Cache hit for: ${req.url}`);
      return of(cachedResponse);
    }

    return next.handle(req).pipe(
      tap(event => {
        if (event instanceof HttpResponse) {
          this.addToCache(cacheKey, event);
        }
      })
    );
  }

  private shouldCache(url: string): boolean {
    // Check if explicitly non-cacheable
    if (this.NON_CACHEABLE_URLS.some(pattern => url.includes(pattern))) {
      return false;
    }

    // Check if explicitly cacheable
    return this.CACHEABLE_URLS.some(pattern => url.includes(pattern));
  }

  private getCacheKey(req: HttpRequest<any>): string {
    // Include URL and relevant query parameters
    return `${req.method}-${req.urlWithParams}`;
  }

  private getFromCache(key: string): HttpResponse<any> | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if cache entry is still valid
    const now = Date.now();
    if (now - entry.timestamp > this.DEFAULT_TTL) {
      this.cache.delete(key);
      return null;
    }

    return entry.response;
  }

  private addToCache(key: string, response: HttpResponse<any>): void {
    // Don't cache error responses
    if (response.status >= 400) {
      return;
    }

    // Manage cache size
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      this.evictOldestEntry();
    }

    this.cache.set(key, {
      response: response.clone(),
      timestamp: Date.now()
    });

    console.log(`Cached response for: ${key}`);
  }

  private evictOldestEntry(): void {
    let oldestKey = '';
    let oldestTimestamp = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Clear all cached entries
   */
  clearCache(): void {
    this.cache.clear();
    console.log('HTTP cache cleared');
  }

  /**
   * Clear cache entries matching a pattern
   */
  clearCacheByPattern(pattern: string): void {
    const keysToDelete: string[] = [];
    
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    console.log(`Cleared ${keysToDelete.length} cache entries matching pattern: ${pattern}`);
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}
