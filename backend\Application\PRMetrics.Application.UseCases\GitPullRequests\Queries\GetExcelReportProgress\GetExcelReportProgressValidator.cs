﻿using FluentValidation;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetExcelReport;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetExcelReportProgress
{
    public class GetExcelReportProgressValidator : AbstractValidator<GetExcelReportProgressQuery>
    {
        public GetExcelReportProgressValidator()
        {
            // Ensure 'Project' field is not empty or null
            RuleFor(x => x.Uuid)
                .NotEmpty().WithMessage("Uuid is required.")
                
                .NotNull().WithMessage("Uuid must not be null.");
        }
    }
}
