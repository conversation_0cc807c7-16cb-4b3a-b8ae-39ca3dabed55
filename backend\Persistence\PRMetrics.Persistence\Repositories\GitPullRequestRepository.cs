﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using System.Net;
using System.Text.Json;
using System.Text;
using Azure;
using Azure.Core;
using MediatR;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Domain.Entities;
using RIB.PRMetrics.Persistence.Common;

namespace RIB.PRMetrics.Persistence.Repositories
{
    /// <summary>
    /// This class is responsible for retrieving Git pull request data from Azure DevOps.
    /// It communicates with the Azure DevOps API to fetch pull requests and pull request details.
    /// </summary>
    public class GitPullRequestRepository : IGitPullRequestRepository
    {
        private readonly AzureDevopsUrlBuilder azureDevopsUrlBuilder;
        private readonly AZDevopsClientCommon aZDevopsClientCommon;
        private readonly IMemoryCache cache;



        /// <summary>
        /// Initializes an instance of the <see cref="GitPullRequestRepository"/> class.
        /// The constructor injects the necessary dependencies for interacting with Azure DevOps API.
        /// </summary>
        /// <param name="_azureDevopsUrlBuilder">An instance of <see cref="AzureDevopsUrlBuilder"/> used to build Azure DevOps API URLs.</param>
        /// <param name="aZDevopsClientCommon">An instance of <see cref="AZDevopsClientCommon"/> for sending HTTP requests to Azure DevOps API.</param>
        /// <param name="cache">An instance of <see cref="IMemoryCache"/> for get and stored the cache value .</param>

        public GitPullRequestRepository(AzureDevopsUrlBuilder _azureDevopsUrlBuilder, AZDevopsClientCommon aZDevopsClientCommon, IMemoryCache _cache)
        {
            // Assign the injected dependencies to the class fields
            azureDevopsUrlBuilder = _azureDevopsUrlBuilder ?? throw new ArgumentNullException(nameof(_azureDevopsUrlBuilder));
            this.aZDevopsClientCommon = aZDevopsClientCommon;
            this.cache = _cache;

        }


        /// <summary>
        /// Retrieves a list of Git pull requests from Azure DevOps based on the specified search criteria.
        /// </summary>
        /// <param name="pullRequestSearch">An instance of <see cref="PullRequestSearchCriteria"/> containing the search parameters for filtering pull requests.</param>
        /// <returns>A list of <see cref="GitPullRequest"/> representing the pull requests returned by Azure DevOps.</returns>
        /// <exception cref="Exception">Thrown when the request fails or an error occurs while retrieving the pull requests.</exception>
        public async Task<List<GitPullRequest>> GetGitPullRequestsAsync(PullRequestSearchCriteria pullRequestSearch)
        {
            var url = azureDevopsUrlBuilder.BuildPullRequestRequestUri(pullRequestSearch);
            var gitCommon = new GitCommon()
            {
                Project = pullRequestSearch.Project,
                Repositories = pullRequestSearch.Repositories,
                PATToken = pullRequestSearch.PATToken,
            };
            var response = await aZDevopsClientCommon.GetAsync(url, pullRequestSearch.PATToken);

            if (response.IsSuccessStatusCode && response.StatusCode == HttpStatusCode.OK)
            {
                var jsonResponse = await response.Content.ReadAsStringAsync();
                var pullRequestsResponse = JsonConvert.DeserializeObject<GitPullRequestList>(jsonResponse);

                return pullRequestsResponse?.Value ?? new List<GitPullRequest>();
            }

            // Handle error by throwing an exception if the response is not successful
            throw new Exception($"Error retrieving pull requests: {response.ReasonPhrase}");
        }


        /// <summary>
        /// Retrieves the details of a specific Git pull request from Azure DevOps.
        /// </summary>  
        /// <param name="gitCommon">An instance of <see cref="GitCommon"/> containing project and repository information, as well as the PAT token.</param>
        /// <param name="pullRequestId">The ID of the pull request to retrieve.</param>
        /// <returns>A <see cref="GitPullRequest"/> representing the details of the specified pull request.</returns>
        /// <exception cref="Exception">Thrown when the request fails or an error occurs while retrieving the pull request details.</exception>
        public async Task<GitPullRequest> GetGitPullRequestDetailsAsync(GitCommon gitCommon, int pullRequestId)
        {
            var pullRequestSerarch = new PullRequestSearchCriteria()
            {
                Project = gitCommon.Project,
                PATToken = gitCommon.PATToken,
                Repositories = gitCommon.Repositories,
            };

            var url = azureDevopsUrlBuilder.BuildPullRequestRequestUri(pullRequestSerarch, pullRequestId);
            var response = await aZDevopsClientCommon.GetAsync(url, gitCommon.PATToken);

            if (response.IsSuccessStatusCode && response.StatusCode == HttpStatusCode.OK)
            {
                var jsonResponse = await response.Content.ReadAsStringAsync();
                var pullRequestsResponse = JsonConvert.DeserializeObject<GitPullRequest>(jsonResponse);

                return pullRequestsResponse ?? new GitPullRequest();
            }

            // Handle error by throwing an exception if the response is not successful
            throw new Exception($"Error retrieving pull request details: {response.ReasonPhrase}");
        }

        /// <summary>
        /// Retrieves the details of a specific Git pull request conflicts from Azure DevOps.
        /// </summary>  
        /// <param name="gitCommon">An instance of <see cref="GitCommon"/> containing project and repository information, as well as the PAT token.</param>
        /// <param name="pullRequestId">The ID of the pull request to retrieve.</param>
        /// <returns>A list of <see cref="GitPullRequestConflict"/> representing the pull request conflicts.</returns>
        /// <exception cref="Exception">Thrown when the request fails or an error occurs while retrieving the pull request conflicts.</exception>
        public async Task<List<GitPullRequestConflict>> GetGitPullRequestConflictsAsync(GitCommon gitCommon, int pullRequestId)
        {


            var url = azureDevopsUrlBuilder.BuildGitPullRequestConflictsUri(gitCommon, pullRequestId);
            var response = await aZDevopsClientCommon.GetAsync(url, gitCommon.PATToken);

            if (response.IsSuccessStatusCode && response.StatusCode == HttpStatusCode.OK)
            {
                var jsonResponse = await response.Content.ReadAsStringAsync();
                var pullRequestsConflictsResponse = JsonConvert.DeserializeObject<GitCommonResponse<GitPullRequestConflict>>(jsonResponse);

                return pullRequestsConflictsResponse.Value ?? new List<GitPullRequestConflict>();
            }

            // Handle error by throwing an exception if the response is not successful
            throw new Exception($"Error retrieving pull request conflicts: {response.ReasonPhrase}");
        }

        /// <summary>
        /// Retrieves the count of Git pull requests from Azure DevOps for the specified status,
        /// using pagination with the given skip and top values.
        /// </summary>
        /// <param name="skip">The number of records to skip for pagination.</param>
        /// <param name="status">The status of the pull requests to retrieve (e.g., active, completed, abandoned).</param>
        /// <param name="common">Common configuration for the request including project, repository, and PAT token.</param>
        /// <param name="top">The number of records to fetch per API call. Default is 1000.</param>
        /// <returns>The count of pull requests for the given criteria.</returns>
        /// <exception cref="Exception">Thrown when the HTTP request fails or the response is invalid.</exception>
        private async Task<uint> GetGitPullRequestCount(int skip, string status, GitCommon common,int top=1000)
        {
            var searchCriteria = new PullRequestSearchCriteria()
            {
                Top = top,
                Skip = skip,
                Project = common.Project,
                Repositories = common.Repositories,
                PATToken = common.PATToken,
                Status = status
            };
            var url = azureDevopsUrlBuilder.BuildPullRequestRequestUri(searchCriteria);
            var response = await aZDevopsClientCommon.GetAsync(url, searchCriteria.PATToken);

            if (response.IsSuccessStatusCode && response.StatusCode == HttpStatusCode.OK)
            {
                var jsonResponse = await response.Content.ReadAsStringAsync();
                var pullRequestsResponse = JsonConvert.DeserializeObject<GitCommonResponse<GitPullRequest>>(jsonResponse);

                return (uint)(pullRequestsResponse?.Count ?? 0);
            }

            throw new Exception($"Error retrieving pull requests: {response.ReasonPhrase}");

        }

        /// <summary>
        /// Determines the total number of pull requests with a specific status (e.g., active, completed, abandoned)
        /// by efficiently paging through the Azure DevOps API. Utilizes caching to optimize repeated lookups.
        /// </summary>
        /// <param name="gitCommon">Configuration for the request including project, repository, and PAT token.</param>
        /// <param name="status">The status of the pull requests to count.</param>
        /// <returns>An object containing the count of pull requests for the specified status.</returns>
        /// <exception cref="Exception">Throws if the API call fails during execution.</exception>
        public async Task<GitPullRequestStatusCount> GetGitPullRequestStatusCountAsync(GitCommon gitCommon, string status)
        {
            string cacheKey = $"org_{azureDevopsUrlBuilder.Organization}";
            int skip = 0, increment = 1000, prCount = 0;
            bool isStop = false;

            // Cache read
            var cachedList = cache.Get<List<GitPullRequestStatusCount>>(cacheKey);
            var cachedStatus = cachedList?.FirstOrDefault(c =>
                c.ProjectName == gitCommon.Project &&
                c.RepositoryName == gitCommon.Repositories &&
                c.Status == status);

            if (cachedStatus?.Value > 0)
            {
                int cachedSkip = (int)((cachedStatus.Value / 5000) * 5000);
                if (cachedStatus.Value < 5000)
                    cachedSkip = (int)((cachedStatus.Value / 1000) * 1000);

                skip = cachedSkip;
                increment = skip;
                prCount = cachedSkip;
            }

            while (!isStop)
            {
                uint count = await GetGitPullRequestCount(skip, status, gitCommon, top: 1);

                if (skip == 1000 && count == 0)
                {
                    // No data at all
                    prCount = (int)await GetGitPullRequestCount(0, status, gitCommon, 1000);
                    break;
                }

                if (count == 1)
                {
                    increment = (skip == 0) ? 1000 : (increment == 1000 ? 4000 : 5000);
                }
                else if (count > 0 && count < 1000)
                {
                    prCount += (int)count;
                    break;
                }
                else if (count == 0)
                {
                    // Backtrack and scan final range
                    skip -= skip==5000?0:5000;
                    while (true)
                    {
                        uint rangeCount = await GetGitPullRequestCount(skip, status, gitCommon, top: 1);
                        if (rangeCount == 0)
                        {
                            skip -= 1000;
                        }
                        else
                        {
                            break;
                        }
                    }

                    // Do parallel scan from this point to skip + 1000
                    int batchStart = skip;
                    var tasks = Enumerable.Range(0, 10).Select(async i =>
                    {
                        int localSkip = batchStart + (i * 100);
                        return await GetGitPullRequestCount(localSkip, status, gitCommon, top: 100);
                    });

                    var results = await Task.WhenAll(tasks);
                    prCount = batchStart + results.Sum(x => (int)x);
                    isStop = true;
                    break;
                }

                skip += increment;
                prCount = skip;
            }

            var result = new GitPullRequestStatusCount
            {
                ProjectName = gitCommon.Project,
                RepositoryName = gitCommon.Repositories,
                Status = status,
                Value = (uint)prCount
            };

            await SetGitRepositoryStatusCount(gitCommon, status, result);
            return result;
        }

        /// <summary>
        /// Caches the computed pull request count for a given project, repository, and status.
        /// Replaces existing cache entries for the same project, repository, and status.
        /// </summary>
        /// <param name="gitCommon">Configuration for the request including project and repository identifiers.</param>
        /// <param name="status">The pull request status (e.g., active, completed, abandoned).</param>
        /// <param name="gitStatusCount">The count information to store in the cache.</param>
        private async Task SetGitRepositoryStatusCount(GitCommon gitCommon, string status, GitPullRequestStatusCount gitStatusCount)
        {
            string cacheKey = $"org_{azureDevopsUrlBuilder.Organization}";

            if (!cache.TryGetValue(cacheKey, out List<GitPullRequestStatusCount> cachedList))
            {
                cachedList = new List<GitPullRequestStatusCount>();
            }
            else
            {
                var existing = cachedList.FirstOrDefault(x =>
                    x.ProjectName == gitCommon.Project &&
                    x.RepositoryName == gitCommon.Repositories &&
                    x.Status == status);

                if (existing != null)
                {
                    cachedList.Remove(existing);
                }
            }

            cachedList.Add(gitStatusCount);

            cache.Set(cacheKey, cachedList, new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(30)
            });
        }

    }

}
