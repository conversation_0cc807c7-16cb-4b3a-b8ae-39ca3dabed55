﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities.CommentThreads
{
    /// <summary>
    /// Represents the comment counts for a specific reviewer.
    /// This includes the number of resolved comments and active comments for the reviewer.
    /// </summary>
    public class ReviewerCommentCount :  User
    {
        /// <summary>
        /// Gets or sets the count of resolved comments for the reviewer.
        /// Resolved comments are those that have been addressed and closed.
        /// </summary>
        public int ResolveCommentCount { get; set; }

        /// <summary>
        /// Gets or sets the count of active comments for the reviewer.
        /// Active comments are unresolved comments that are still open for discussion or action.
        /// </summary>
        public int ActiveCommentCount { get; set; }
    }
}
