﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using   RIB.PRMetrics.Application.Dto;
using   RIB.PRMetrics.Application.UseCases.Commons.Bases;
using   RIB.PRMetrics.Domain.Entities;

namespace  RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetPullRequests
{
    /// <summary>
    /// Query to retrieve a list of Git pull requests based on search criteria.
    /// Inherits from PullRequestSearchCriteria which includes properties like project name, repository, date range, etc.
    /// </summary>
    public class GetGitPullRequestQuery : PullRequestSearchCriteria, IRequest<BaseReponseGeneric<List<GitPullRequestDto>>>
    {
        // This class acts as a request DTO and inherits filtering properties from PullRequestSearchCriteria.
        // It's used by MediatR to trigger the corresponding handler and return a generic response with a list of pull requests.
    }
}
