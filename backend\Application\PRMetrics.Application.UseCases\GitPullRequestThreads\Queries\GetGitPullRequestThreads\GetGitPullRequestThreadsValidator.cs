﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequestThreads.Queries.GetGitPullRequestThreads
{
    /// <summary>
    /// Validator for the GetGitPullRequestThreadsQuery to ensure that the input fields meet specific requirements.
    /// Uses FluentValidation to define rules for validating the query parameters.
    /// </summary>
    public class GetGitPullRequestThreadsValidator : AbstractValidator<GetGitPullRequestThreadsQuery>
    {
        public GetGitPullRequestThreadsValidator()
        {
            // Validate that the 'Project' field is not null or empty
            RuleFor(x => x.Project)
                // Project must not be empty
                .NotEmpty().WithMessage("Project is required.")
                // Project must not be null
                .NotNull().WithMessage("Project must not be null.");

            // Validate that the 'Repositories' field is not null or empty
            RuleFor(x => x.Repositories)
                // Repository must not be empty
                .NotEmpty().WithMessage("Repository is required.")
                // Repository must not be null
                .NotNull().WithMessage("Repository must not be null.");

            // Validate that the 'PullRequestId' field is not null or empty
            RuleFor(x => x.PullRequestId)
                // PullRequestId must not be empty
                .NotEmpty().WithMessage("Pull Request ID is required.")
                // PullRequestId must not be null
                .NotNull().WithMessage("Pull Request ID must not be null.");
        }
    }
}
