﻿using RIB.PRMetrics.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace RIB.PRMetrics.Domain.Entities
{
    public class GitPullRequestComment
    {
        
        public int Id { get; set; }

        
        public int ParentCommentId { get; set; }

        
        public string Content { get; set; }

        
        public DateTime PublishedDate { get; set; }

        
        public DateTime LastUpdatedDate { get; set; }

        
        public User Author { get; set; }

        
        public string CommentType { get; set; }
    }
}
