﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities.IdentityPicker
{
    /// <summary>
    /// Represents the response returned from the Azure DevOps Identity Picker API,
    /// containing a list of result items.
    /// </summary>
    public class IdentitiesResponse
    {
        /// <summary>
        /// Gets or sets the list of result items returned by the Identity Picker API.
        /// </summary>
        public List<ResultsItem> Results { get; set; }
    }
}

