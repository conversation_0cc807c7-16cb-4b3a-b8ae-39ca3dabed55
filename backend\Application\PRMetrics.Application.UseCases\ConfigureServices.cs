﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using System.Reflection;
using Microsoft.Extensions.DependencyInjection;
using FluentValidation;
using MediatR;
using RIB.PRMetrics.Application.UseCases.Commons.Behaviours;

namespace  RIB.PRMetrics.Application.UseCases
{
    /// <summary>
    /// Static class to configure application services for Dependency Injection.
    /// This is where we register all the necessary services such as MediatR, AutoMapper, FluentValidation, and custom pipeline behaviors.
    /// </summary>
    public static class ConfigureServices
    {
        /// <summary>
        /// Extension method to register the application's services in the dependency injection container.
        /// </summary>
        /// <param name="services">The service collection to add the services to.</param>
        public static void AddInjectionApplication(this IServiceCollection services)
        {
            // Registers MediatR services and all handlers, notifications, etc., from all assemblies in the current domain.
            // This enables MediatR to work with all the commands and queries in the application.
            services.AddMediatR(cfg => cfg.RegisterServicesFromAssemblies(AppDomain.CurrentDomain.GetAssemblies()));

            // Registers AutoMapper for the current assembly to allow mapping between entities and DTOs.
            // This will scan the assembly for AutoMapper profiles and apply them.
            services.AddAutoMapper(Assembly.GetExecutingAssembly());

            // Registers AutoMapper for all assemblies in the current domain to allow mapping across different modules.
            services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());

            // Registers all FluentValidation validators from the current assembly.
            // This enables validation for command and query classes in the application.
            services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

            // Registers the ValidationBehavior in the pipeline as a transient service to validate requests before handling.
            // This ensures that any validation for commands/queries is performed before processing them.
            services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehaviour<,>));

            // Registers the LoggingBehavior as a singleton in the pipeline.
            // This allows logging of every request processed by MediatR.
            services.AddSingleton(typeof(IPipelineBehavior<,>), typeof(LoggingBehaviour<,>));

            // Registers the PerformanceBehavior as a singleton in the pipeline.
            // This measures and logs the performance (e.g., time taken) of every request processed by MediatR.
            services.AddSingleton(typeof(IPipelineBehavior<,>), typeof(PerformanceBehaviour<,>));
        }
    }
}
