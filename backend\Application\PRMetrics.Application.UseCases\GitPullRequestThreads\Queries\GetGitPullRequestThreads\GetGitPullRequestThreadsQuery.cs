﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using System.ComponentModel.DataAnnotations;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using RIB.PRMetrics.Domain.Entities;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequestThreads.Queries.GetGitPullRequestThreads
{
    /// <summary>
    /// Represents the query to get the Git pull request threads based on a specific pull request ID.
    /// This query is part of the CQRS (Command Query Responsibility Segregation) pattern and is used to fetch the data.
    /// </summary>
    public class GetGitPullRequestThreadsQuery : GitCommon, IRequest<BaseReponseGeneric<GitPullRequestThreadsResponseDto>>
    {
        /// <summary>
        /// Pull Request ID to fetch. This is a required field and will be used to retrieve the threads associated with the specified pull request.
        /// </summary>
        [Required(ErrorMessage = "Pull Request Id is required.")]
        [FromQuery(Name = "pullRequestId")]
        public int PullRequestId { get; set; } = 0;

        [FromQuery(Name = "prAuthorId")]
        public string PRAuthorId { get; set; } = "";
    }
}
