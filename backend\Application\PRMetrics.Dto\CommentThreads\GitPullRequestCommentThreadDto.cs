﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto.CommentThreads
{
    /// <summary>
    /// Data Transfer Object (DTO) representing a Git pull request comment thread.
    /// Contains details about the pull request, the user who created it, the comment counts,
    /// and associated comment threads.
    /// </summary>
    public class GitPullRequestCommentThreadDto
    {
      
        /// <summary>
        /// Gets or sets the list of comment threads associated with the pull request.
        /// </summary>
        public List<CommentThreadDto> CommentThreads { get; set; }
    }
}
