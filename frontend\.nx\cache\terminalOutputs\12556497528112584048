[33m❯[39m Building...
[32m✔[39m Building...
[37mApplication bundle generation failed. [9.619 seconds][39m
[37m[39m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1m7 repetitive deprecation warnings omitted.[39m[22m
[1m[33mRun in verbose mode to see all warnings.[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m  null[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/header/header.component.scss:2:8:[39m[22m
[1m[33m[37m      2 │ @import [32m[37m'../../styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pat-login/pat-login.component.scss:2:8:[39m[22m
[1m[33m[37m      2 │ @import [32m[37m'../../styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mTS-998113: HeaderComponent is not used within the template of PatLoginComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pat-login/pat-login.component.ts:37:4:[39m[22m
[1m[33m[37m      37 │     [32mHeaderComponent[37m,[39m[22m
[1m[33m         ╵     [32m~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-data/pr-data.component.scss:2:8:[39m[22m
[1m[33m[37m      2 │ @import [32m[37m'../../styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8107: The left side of this optional chain operation does not include 'null' or 'undefined' in its type, therefore the '?.' operator can be replaced with the '.' operator.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-de[39m[22m[1m[33mtails.component.html:43:70:[39m[22m
[1m[33m[37m      43 │ ...lass="author-name">{{ pullRequest.createdBy?.[32mdisplayName[37m }}</span>[39m[22m
[1m[33m         ╵                                                 [32m~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  Error occurs in the template of component PrDetailsComponent.[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.ts:50:15:[39m[22m
[1m[33m[37m      50 │   templateUrl: [32m'./pr-details.component.html'[37m,[39m[22m
[1m[33m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8107: The left side of this optional chain operation does not include 'null' or 'undefined' in its type, therefore the '?.' operator can be replaced with the '.' operator.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.html:233:78:[39m[22m
[1m[33m[37m      233 │ ...n class="comment-author">{{ comment.author?.[32mdisplayName[37m }}</span>[39m[22m
[1m[33m          ╵                                                [32m~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  Error occurs in the template of component PrDetailsComponent.[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.ts:50:15:[39m[22m
[1m[33m[37m      50 │   templateUrl: [32m'./pr-details.component.html'[37m,[39m[22m
[1m[33m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:2:8:[39m[22m
[1m[33m[37m      2 │ @import [32m[37m'../../styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:3:8:[39m[22m
[1m[33m[37m      3 │ @import [32m[37m'../../styles/components.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and[39m[22m[1m[33m automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:4:8:[39m[22m
[1m[33m[37m      4 │ @import [32m[37m'../../styles/pr-components.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:5:8:[39m[22m
[1m[33m[37m      5 │ @import [32m[37m'../../styles/pr-variables.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:213:68:[39m[22m
[1m[33m[37m      213 │ ...gradient(135deg, $primary-color, [32m[37mdarken($primary-color, 10%)); }[39m[22m
[1m[33m          ╵                                     [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Global built-in functions are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  Use color.adjust instead.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:213:68:[39m[22m
[1m[33m[37m      213 │ ...gradient(135deg, $primary-color, [32m[37mdarken($primary-color, 10%)); }[39m[22m
[1m[33m          ╵                                     [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  darken() is deprecated. Suggestions:[39m[22m
[1m[33m  [39m[22m
[1m[33m  color.scale($color, $lightness: -28.4916201117%)[39m[22m
[1m[33m  color.adjust($color, $lightness: -10%)[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info: [4mhttps://sass-lang.com/d/color-functions[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1[39m[22m[1m[33mm[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:214:68:[39m[22m
[1m[33m[37m      214 │ ...r-gradient(135deg, $active-color, [32m[37mdarken($active-color, 10%)); }[39m[22m
[1m[33m          ╵                                      [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Global built-in functions are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  Use color.adjust instead.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:214:68:[39m[22m
[1m[33m[37m      214 │ ...r-gradient(135deg, $active-color, [32m[37mdarken($active-color, 10%)); }[39m[22m
[1m[33m          ╵                                      [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  darken() is deprecated. Suggestions:[39m[22m
[1m[33m  [39m[22m
[1m[33m  color.scale($color, $lightness: -20%)[39m[22m
[1m[33m  color.adjust($color, $lightness: -10%)[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info: [4mhttps://sass-lang.com/d/color-functions[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:215:69:[39m[22m
[1m[33m[37m      215 │ ...ear-gradient(135deg, $fixed-color, [32m[37mdarken($fixed-color, 10%)); }[39m[22m
[1m[33m          ╵                                       [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Global built-in functions are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  Use color.adjust instead.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:215:69:[39m[22m
[1m[33m[37m      215 │ ...ear-gradient(135deg, $fixed-color, [32m[37mdarken($fixed-color, 10%)); }[39m[22m
[1m[33m          ╵                                       [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  darken() is deprecated. Suggestions:[39m[22m
[1m[33m  [39m[22m
[1m[33m  color.scale($color, $lightness: -20.3187250996%)[39m[22m
[1m[33m  color.adjust($color, $lightness: -10%)[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info: [4mhttps://sass-lang.com/d/color-functions[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[3[39m[22m[1m[33m3m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:376:24:[39m[22m
[1m[33m[37m      376 │       background-color: [32m[37mmap-get($thread-status-colors, active);[39m[22m
[1m[33m          ╵                         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Global built-in functions are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  Use map.get instead.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:377:13:[39m[22m
[1m[33m[37m      377 │       color: [32m[37mmap-get($thread-status-text-colors, active);[39m[22m
[1m[33m          ╵              [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Global built-in functions are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  Use map.get instead.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:593:4:[39m[22m
[1m[33m[37m      593 │     [32m[37mgrid-template-columns: repeat(auto-fill, minmax(350px, 1fr));[39m[22m
[1m[33m          ╵     [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass's behavior for declarations that appear after nested[39m[22m
[1m[33m  rules will be changing to match the behavior specified by CSS in an upcoming[39m[22m
[1m[33m  version. To keep the existing behavior, move the declaration above the nested[39m[22m
[1m[33m  rule. To opt into the new behavior, wrap the declaration in `& {}`.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info: [4mhttps://sass-lang.com/d/mixed-decls[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:785:4:[39m[22m
[1m[33m[37m      785 │     [32m[37mgrid-template-columns: repeat(auto-fill, minmax(200px, 1fr));[39m[22m
[1m[33m          ╵     [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass's behavior for declarations that appear after nested[39m[22m
[1m[33m  rules will be changing to match the behavior spec[39m[22m[1m[33mified by CSS in an upcoming[39m[22m
[1m[33m  version. To keep the existing behavior, move the declaration above the nested[39m[22m
[1m[33m  rule. To opt into the new behavior, wrap the declaration in `& {}`.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info: [4mhttps://sass-lang.com/d/mixed-decls[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/styles/pr-components.scss:4:8:[39m[22m
[1m[33m[37m      4 │ @import [32m[37m'./components.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mD:\iTwo4.0_CodeBase\tools-git-analytics\frontend\src\services\excel-export.service.ts: Unexpected token (130:27)[39m[22m
[1m[31m[39m[22m
[1m[31m[0m [90m 128 |[39m[31m     [36mconst[39m[31m[33m:[39m[31m worksheet [33m=[39m[31m [33mXLSX[39m[31m[33m.[39m[31mutils[33m.[39m[31maoa_to_sheet(data)[33m,[39m[31m[39m[22m
[1m[31m [90m 129 |[39m[31m     [90m// Set column widths[39m[31m[39m[22m
[1m[31m[31m[1m>[22m[1m[39m[31m[90m 130 |[39m[31m     worksheet[33m,[39m[31m [[32m'!cols'[39m[31m][33m:[39m[31m  [33m=[39m[31m [[39m[22m
[1m[31m [90m     |[39m[31m                            [31m[1m^[22m[1m[39m[31m[39m[22m
[1m[31m [90m 131 |[39m[31m         { width[33m:[39m[31m [35m20[39m[31m }[33m,[39m[31m[39m[22m
[1m[31m [90m 132 |[39m[31m         { width[33m:[39m[31m [35m50[39m[31m }[39m[22m
[1m[31m [90m 133 |[39m[31m     ][33m,[39m[31m[0m[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/components/pr-data/pr-data.component.ts:21:35:[39m[22m
[1m[31m[37m      21 │ ...{ ExcelExportService } from [32m'../../services/excel-export.service'[37m;[39m[22m
[1m[31m         ╵                                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m  This error came from the "onLoad" callback registered here:[39m[22m
[1m[31m[39m[22m
[1m[31m    node_modules/@angular/build/src/tools/esbuild/angular/compiler-plugin.js:323:18:[39m[22m
[1m[31m[37m      323 │             build.[32monLoad[37m({ filter: /\.[cm]?[jt]sx?$/ }, async (ar...[39m[22m
[1m[31m          ╵                   [32m~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m    at setup (D:\iTwo4.0_CodeBase\tools-git-analytics\frontend\node_modules\@angular\build\src\tools\esbuild\angular\compiler-plugin.js:323:19)[39m[22m
[1m[31m    at async handlePlugins (D:\iTwo4.0_CodeBase\tools-git-analytics\frontend\node_modules\@angular\build\node_modules\esbuild\lib\main.js:1195:20)[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2339: Property 'getCurrentConfig' does not exist on type 'PrDataCommonService'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/components/pr-data/pr-data.component.ts:615:39:[39m[22m
[1m[31m[37m      615 │       const config = this.prDataCommon.[32mgetCurrentConfig[37m();[39m[22m
[1m[31m          ╵                                        [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41[39m[22m[1m[31m;31m][0m [1mTS1128: Declaration or statement expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:124:2:[39m[22m
[1m[31m[37m      124 │   [32mprivate[37m createPROverviewSheet([39m[22m
[1m[31m          ╵   [32m~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'createPROverviewSheet'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:124:10:[39m[22m
[1m[31m[37m      124 │   private [32mcreatePROverviewSheet[37m([39m[22m
[1m[31m          ╵           [32m~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'workbook'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:125:4:[39m[22m
[1m[31m[37m      125 │     [32mworkbook[37m: XLSX.WorkBook,[39m[22m
[1m[31m          ╵     [32m~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:125:12:[39m[22m
[1m[31m[37m      125 │     workbook[32m:[37m XLSX.WorkBook,[39m[22m
[1m[31m          ╵             [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'XLSX'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:125:14:[39m[22m
[1m[31m[37m      125 │     workbook: [32mXLSX[37m.WorkBook,[39m[22m
[1m[31m          ╵               [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'pullRequest'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:126:4:[39m[22m
[1m[31m[37m      126 │     [32mpullRequest[37m: PullRequestDetails,[39m[22m
[1m[31m          ╵     [32m~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:126:15:[39m[22m
[1m[31m[37m      126 │     pullRequest[32m:[37m PullRequestDetails,[39m[22m
[1m[31m          ╵                [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'PullRequestDetails'.[0m [1m[35m[plu[39m[22m[1m[31mgin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:126:17:[39m[22m
[1m[31m[37m      126 │     pullRequest: [32mPullRequestDetails[37m,[39m[22m
[1m[31m          ╵                  [32m~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'reviewCycleMetrics'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:127:4:[39m[22m
[1m[31m[37m      127 │     [32mreviewCycleMetrics[37m: ReviewCycleMetrics | null,[39m[22m
[1m[31m          ╵     [32m~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:127:22:[39m[22m
[1m[31m[37m      127 │     reviewCycleMetrics[32m:[37m ReviewCycleMetrics | null,[39m[22m
[1m[31m          ╵                       [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'ReviewCycleMetrics'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:127:24:[39m[22m
[1m[31m[37m      127 │     reviewCycleMetrics: [32mReviewCycleMetrics[37m | null,[39m[22m
[1m[31m          ╵                         [32m~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18050: The value 'null' cannot be used here.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:127:45:[39m[22m
[1m[31m[37m      127 │     reviewCycleMetrics: ReviewCycleMetrics | [32mnull[37m,[39m[22m
[1m[31m          ╵                                              [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'draftModeMetrics'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:128:4:[39m[22m
[1m[31m[37m      128 │     [32mdraftModeMetrics[37m: any | null[39m[22m
[1m[31m          ╵     [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:128:20:[39m[22m
[1m[31m[37m      128 │     draftModeMetrics[32m:[37m any | null[39m[22m
[1m[31m          ╵              [39m[22m[1m[31m       [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2693: 'any' only refers to a type, but is being used as a value here.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:128:22:[39m[22m
[1m[31m[37m      128 │     draftModeMetrics: [32many[37m | null[39m[22m
[1m[31m          ╵                       [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18050: The value 'null' cannot be used here.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:128:28:[39m[22m
[1m[31m[37m      128 │     draftModeMetrics: any | [32mnull[37m[39m[22m
[1m[31m          ╵                             [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ';' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:129:3:[39m[22m
[1m[31m[37m      129 │   )[32m:[37m void {[39m[22m
[1m[31m          ╵    [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ':' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:130:10:[39m[22m
[1m[31m[37m      130 │     const [32mdata[37m = [[39m[22m
[1m[31m          ╵           [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'data'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:130:10:[39m[22m
[1m[31m[37m      130 │     const [32mdata[37m = [[39m[22m
[1m[31m          ╵           [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'pullRequest'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:132:16:[39m[22m
[1m[31m[37m      132 │       ['PR ID', [32mpullRequest[37m.pullRequestId],[39m[22m
[1m[31m          ╵                 [32m~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'pullRequest'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:133:16:[39m[22m
[1m[31m[37m      133 │       ['Title', [32mpullRequest[37m.title],[39m[22m
[1m[31m          ╵                 [32m~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[39m[22m[1m[31m[41;31m][0m [1mTS2304: Cannot find name 'pullRequest'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:134:17:[39m[22m
[1m[31m[37m      134 │       ['Status', [32mpullRequest[37m.status],[39m[22m
[1m[31m          ╵                  [32m~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'pullRequest'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:135:19:[39m[22m
[1m[31m[37m      135 │       ['Is Draft', [32mpullRequest[37m.isDraft === 'true' ? 'Yes' : 'No'],[39m[22m
[1m[31m          ╵                    [32m~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'pullRequest'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:136:21:[39m[22m
[1m[31m[37m      136 │       ['Created By', [32mpullRequest[37m.createdBy?.displayName || ''],[39m[22m
[1m[31m          ╵                      [32m~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2532: Object is possibly 'undefined'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:137:24:[39m[22m
[1m[31m[37m      137 │       ['Creation Date', [32mthis[37m.formatDate(pullRequest.creationDate)],[39m[22m
[1m[31m          ╵                         [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'pullRequest'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:137:40:[39m[22m
[1m[31m[37m      137 │       ['Creation Date', this.formatDate([32mpullRequest[37m.creationDate)],[39m[22m
[1m[31m          ╵                                         [32m~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2532: Object is possibly 'undefined'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:138:24:[39m[22m
[1m[31m[37m      138 │       ['Source Branch', [32mthis[37m.formatBranchName(pullRequest.sourceR...[39m[22m
[1m[31m          ╵                         [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name [39m[22m[1m[31m'pullRequest'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:138:46:[39m[22m
[1m[31m[37m      138 │ ...ource Branch', this.formatBranchName([32mpullRequest[37m.sourceRefName)],[39m[22m
[1m[31m          ╵                                         [32m~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2532: Object is possibly 'undefined'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:139:24:[39m[22m
[1m[31m[37m      139 │       ['Target Branch', [32mthis[37m.formatBranchName(pullRequest.targetR...[39m[22m
[1m[31m          ╵                         [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'pullRequest'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:139:46:[39m[22m
[1m[31m[37m      139 │ ...arget Branch', this.formatBranchName([32mpullRequest[37m.targetRefName)],[39m[22m
[1m[31m          ╵                                         [32m~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'pullRequest'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:140:23:[39m[22m
[1m[31m[37m      140 │       ['Merge Status', [32mpullRequest[37m.mergeStatus || ''],[39m[22m
[1m[31m          ╵                        [32m~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'pullRequest'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:141:22:[39m[22m
[1m[31m[37m      141 │       ['Description', [32mpullRequest[37m.description || 'No description ...[39m[22m
[1m[31m          ╵                       [32m~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:144:5:[39m[22m
[1m[31m[37m      144 │     ][32m;[37m[39m[22m
[1m[31m          ╵      [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS7006: Parameter 'draftModeMetrics' implicitly has an 'any' type.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    sr[39m[22m[1m[31mc/services/excel-export.service.ts:147:8:[39m[22m
[1m[31m[37m      147 │     if ([32mdraftModeMetrics[37m) {[39m[22m
[1m[31m          ╵         [32m~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'data'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:148:6:[39m[22m
[1m[31m[37m      148 │       [32mdata[37m.push([39m[22m
[1m[31m          ╵       [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:155:4:[39m[22m
[1m[31m[37m      155 │     [32mif[37m (reviewCycleMetrics) {[39m[22m
[1m[31m          ╵     [32m~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS7006: Parameter 'reviewCycleMetrics' implicitly has an 'any' type.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:155:8:[39m[22m
[1m[31m[37m      155 │     if ([32mreviewCycleMetrics[37m) {[39m[22m
[1m[31m          ╵         [32m~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'data'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:156:6:[39m[22m
[1m[31m[37m      156 │       [32mdata[37m.push([39m[22m
[1m[31m          ╵       [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:162:4:[39m[22m
[1m[31m[37m      162 │     [32mconst[37m worksheet = XLSX.utils.aoa_to_sheet(data);[39m[22m
[1m[31m          ╵     [32m~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ':' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:162:10:[39m[22m
[1m[31m[37m      162 │     const [32mworksheet[37m = XLSX.utils.aoa_to_sheet(data);[39m[22m
[1m[31m          ╵           [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'worksheet'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:162:10:[39m[22m
[1m[31m[37m      162 │     const [32mworksheet[37m = XLSX.u[39m[22m[1m[31mtils.aoa_to_sheet(data);[39m[22m
[1m[31m          ╵           [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'XLSX'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:162:22:[39m[22m
[1m[31m[37m      162 │     const worksheet = [32mXLSX[37m.utils.aoa_to_sheet(data);[39m[22m
[1m[31m          ╵                       [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'data'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:162:46:[39m[22m
[1m[31m[37m      162 │     const worksheet = XLSX.utils.aoa_to_sheet([32mdata[37m);[39m[22m
[1m[31m          ╵                                               [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:162:51:[39m[22m
[1m[31m[37m      162 │     const worksheet = XLSX.utils.aoa_to_sheet(data)[32m;[37m[39m[22m
[1m[31m          ╵                                                    [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'worksheet'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:165:4:[39m[22m
[1m[31m[37m      165 │     [32mworksheet[37m['!cols'] = [[39m[22m
[1m[31m          ╵     [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:165:13:[39m[22m
[1m[31m[37m      165 │     worksheet[32m[[37m'!cols'] = [[39m[22m
[1m[31m          ╵              [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ':' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:165:23:[39m[22m
[1m[31m[37m      165 │     worksheet['!cols'] [32m=[37m [[39m[22m
[1m[31m          ╵                        [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m   [39m[22m[1m[31m src/services/excel-export.service.ts:168:5:[39m[22m
[1m[31m[37m      168 │     ][32m;[37m[39m[22m
[1m[31m          ╵      [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'XLSX'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:170:4:[39m[22m
[1m[31m[37m      170 │     [32mXLSX[37m.utils.book_append_sheet(workbook, worksheet, 'PR Overvie...[39m[22m
[1m[31m          ╵     [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:170:8:[39m[22m
[1m[31m[37m      170 │     XLSX[32m.[37mutils.book_append_sheet(workbook, worksheet, 'PR Overvie...[39m[22m
[1m[31m          ╵         [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'workbook'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:170:33:[39m[22m
[1m[31m[37m      170 │ ...XLSX.utils.book_append_sheet([32mworkbook[37m, worksheet, 'PR Overview');[39m[22m
[1m[31m          ╵                                 [32m~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'worksheet'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:170:43:[39m[22m
[1m[31m[37m      170 │ ...XLSX.utils.book_append_sheet(workbook, [32mworksheet[37m, 'PR Overview');[39m[22m
[1m[31m          ╵                                           [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:170:68:[39m[22m
[1m[31m[37m      170 │ ...XLSX.utils.book_append_sheet(workbook, worksheet, 'PR Overview')[32m;[37m[39m[22m
[1m[31m          ╵                                                                    [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1128: Declaration or statement expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:176:2:[39m[22m
[1m[31m[39m[22m[1m[31m[37m      176 │   [32mprivate[37m createCommentsSheet(workbook: XLSX.WorkBook, commentThr...[39m[22m
[1m[31m          ╵   [32m~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'createCommentsSheet'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:176:10:[39m[22m
[1m[31m[37m      176 │   private [32mcreateCommentsSheet[37m(workbook: XLSX.WorkBook, commentThr...[39m[22m
[1m[31m          ╵           [32m~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'workbook'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:176:30:[39m[22m
[1m[31m[37m      176 │   private createCommentsSheet([32mworkbook[37m: XLSX.WorkBook, commentThr...[39m[22m
[1m[31m          ╵                               [32m~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:176:38:[39m[22m
[1m[31m[37m      176 │ ...te createCommentsSheet(workbook[32m:[37m XLSX.WorkBook, commentThreads...[39m[22m
[1m[31m          ╵                                   [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'XLSX'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:176:40:[39m[22m
[1m[31m[37m      176 │ ...reateCommentsSheet(workbook: [32mXLSX[37m.WorkBook, commentThreads: Co...[39m[22m
[1m[31m          ╵                                 [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'commentThreads'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:176:55:[39m[22m
[1m[31m[37m      176 │ ...workbook: XLSX.WorkBook, [32mcommentThreads[37m: CommentThread[]): void {[39m[22m
[1m[31m          ╵                             [32m~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:176:69:[39m[22m
[1m[31m[37m      176 │ ...workbook: XLSX.WorkBook, co[39m[22m[1m[31mmmentThreads[32m:[37m CommentThread[]): void {[39m[22m
[1m[31m          ╵                                           [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'CommentThread'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:176:71:[39m[22m
[1m[31m[37m      176 │ ...workbook: XLSX.WorkBook, commentThreads: [32mCommentThread[37m[]): void {[39m[22m
[1m[31m          ╵                                             [32m~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1011: An element access expression should take an argument.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:176:85:[39m[22m
[1m[31m[37m      176 │ ...workbook: XLSX.WorkBook, commentThreads: CommentThread[[32m[37m]): void {[39m[22m
[1m[31m          ╵                                                           [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ';' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:176:87:[39m[22m
[1m[31m[37m      176 │ ...workbook: XLSX.WorkBook, commentThreads: CommentThread[])[32m:[37m void {[39m[22m
[1m[31m          ╵                                                             [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ':' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:177:10:[39m[22m
[1m[31m[37m      177 │     const [32mdata[37m = [[39m[22m
[1m[31m          ╵           [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'data'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:177:10:[39m[22m
[1m[31m[37m      177 │     const [32mdata[37m = [[39m[22m
[1m[31m          ╵           [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:179:5:[39m[22m
[1m[31m[37m      179 │     ][32m;[37m[39m[22m
[1m[31m          ╵      [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists i[39m[22m[1m[31mn scope for the shorthand property 'commentThreads'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:181:4:[39m[22m
[1m[31m[37m      181 │     [32mcommentThreads[37m.forEach(thread => {[39m[22m
[1m[31m          ╵     [32m~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:181:18:[39m[22m
[1m[31m[37m      181 │     commentThreads[32m.[37mforEach(thread => {[39m[22m
[1m[31m          ╵                   [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS7006: Parameter 'thread' implicitly has an 'any' type.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:181:27:[39m[22m
[1m[31m[37m      181 │     commentThreads.forEach([32mthread[37m => {[39m[22m
[1m[31m          ╵                            [32m~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS7006: Parameter 'comment' implicitly has an 'any' type.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:185:31:[39m[22m
[1m[31m[37m      185 │       thread.comments?.forEach([32mcomment[37m => {[39m[22m
[1m[31m          ╵                                [32m~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'data'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:186:8:[39m[22m
[1m[31m[37m      186 │         [32mdata[37m.push([[39m[22m
[1m[31m          ╵         [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2532: Object is possibly 'undefined'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:193:10:[39m[22m
[1m[31m[37m      193 │           [32mthis[37m.formatDate(comment.publishedDate),[39m[22m
[1m[31m          ╵           [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:197:6:[39m[22m
[1m[31m[37m      197 │     })[32m;[37m[39m[22m
[1m[31m          ╵       [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[[39m[22m[1m[31m31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ':' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:199:10:[39m[22m
[1m[31m[37m      199 │     const [32mworksheet[37m = XLSX.utils.aoa_to_sheet(data);[39m[22m
[1m[31m          ╵           [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'worksheet'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:199:10:[39m[22m
[1m[31m[37m      199 │     const [32mworksheet[37m = XLSX.utils.aoa_to_sheet(data);[39m[22m
[1m[31m          ╵           [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'XLSX'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:199:22:[39m[22m
[1m[31m[37m      199 │     const worksheet = [32mXLSX[37m.utils.aoa_to_sheet(data);[39m[22m
[1m[31m          ╵                       [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'data'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:199:46:[39m[22m
[1m[31m[37m      199 │     const worksheet = XLSX.utils.aoa_to_sheet([32mdata[37m);[39m[22m
[1m[31m          ╵                                               [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:199:51:[39m[22m
[1m[31m[37m      199 │     const worksheet = XLSX.utils.aoa_to_sheet(data)[32m;[37m[39m[22m
[1m[31m          ╵                                                    [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'worksheet'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:202:4:[39m[22m
[1m[31m[37m      202 │     [32mworksheet[37m['!cols'] = [[39m[22m
[1m[31m          ╵     [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    s[39m[22m[1m[31mrc/services/excel-export.service.ts:202:13:[39m[22m
[1m[31m[37m      202 │     worksheet[32m[[37m'!cols'] = [[39m[22m
[1m[31m          ╵              [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ':' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:202:23:[39m[22m
[1m[31m[37m      202 │     worksheet['!cols'] [32m=[37m [[39m[22m
[1m[31m          ╵                        [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:211:5:[39m[22m
[1m[31m[37m      211 │     ][32m;[37m[39m[22m
[1m[31m          ╵      [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'XLSX'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:213:4:[39m[22m
[1m[31m[37m      213 │     [32mXLSX[37m.utils.book_append_sheet(workbook, worksheet, 'Comments');[39m[22m
[1m[31m          ╵     [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:213:8:[39m[22m
[1m[31m[37m      213 │     XLSX[32m.[37mutils.book_append_sheet(workbook, worksheet, 'Comments');[39m[22m
[1m[31m          ╵         [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'workbook'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:213:33:[39m[22m
[1m[31m[37m      213 │     XLSX.utils.book_append_sheet([32mworkbook[37m, worksheet, 'Comments');[39m[22m
[1m[31m          ╵                                  [32m~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'worksheet'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:213:43:[39m[22m
[1m[31m[37m      213 │     XLSX.utils.book_append_sheet(workbook, [32mworksheet[37m, 'Comments');[39m[22m
[1m[31m          ╵                                            [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97[39m[22m[1m[31mmERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:213:65:[39m[22m
[1m[31m[37m      213 │     XLSX.utils.book_append_sheet(workbook, worksheet, 'Comments')[32m;[37m[39m[22m
[1m[31m          ╵                                                                  [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1128: Declaration or statement expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:219:2:[39m[22m
[1m[31m[37m      219 │   [32mprivate[37m createReviewersSheet(workbook: XLSX.WorkBook, reviewers...[39m[22m
[1m[31m          ╵   [32m~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'createReviewersSheet'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:219:10:[39m[22m
[1m[31m[37m      219 │   private [32mcreateReviewersSheet[37m(workbook: XLSX.WorkBook, reviewers...[39m[22m
[1m[31m          ╵           [32m~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'workbook'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:219:31:[39m[22m
[1m[31m[37m      219 │ ...ivate createReviewersSheet([32mworkbook[37m: XLSX.WorkBook, reviewers:...[39m[22m
[1m[31m          ╵                               [32m~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:219:39:[39m[22m
[1m[31m[37m      219 │ ...e createReviewersSheet(workbook[32m:[37m XLSX.WorkBook, reviewers: any...[39m[22m
[1m[31m          ╵                                   [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'XLSX'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:219:41:[39m[22m
[1m[31m[37m      219 │ ...eateReviewersSheet(workbook: [32mXLSX[37m.WorkBook, reviewers: any[]):...[39m[22m
[1m[31m          ╵                                 [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1[39m[22m[1m[31mmTS2304: Cannot find name 'reviewers'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:219:56:[39m[22m
[1m[31m[37m      219 │ ...ReviewersSheet(workbook: XLSX.WorkBook, [32mreviewers[37m: any[]): void {[39m[22m
[1m[31m          ╵                                            [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:219:65:[39m[22m
[1m[31m[37m      219 │ ...ReviewersSheet(workbook: XLSX.WorkBook, reviewers[32m:[37m any[]): void {[39m[22m
[1m[31m          ╵                                                     [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2693: 'any' only refers to a type, but is being used as a value here.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:219:67:[39m[22m
[1m[31m[37m      219 │ ...ReviewersSheet(workbook: XLSX.WorkBook, reviewers: [32many[37m[]): void {[39m[22m
[1m[31m          ╵                                                       [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1011: An element access expression should take an argument.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:219:71:[39m[22m
[1m[31m[37m      219 │ ...ReviewersSheet(workbook: XLSX.WorkBook, reviewers: any[[32m[37m]): void {[39m[22m
[1m[31m          ╵                                                           [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ';' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:219:73:[39m[22m
[1m[31m[37m      219 │ ...ReviewersSheet(workbook: XLSX.WorkBook, reviewers: any[])[32m:[37m void {[39m[22m
[1m[31m          ╵                                                             [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ':' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:220:10:[39m[22m
[1m[31m[37m      220 │     const [32mdata[37m = [[39m[22m
[1m[31m          ╵           [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[[39m[22m[1m[31m31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'data'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:220:10:[39m[22m
[1m[31m[37m      220 │     const [32mdata[37m = [[39m[22m
[1m[31m          ╵           [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:222:5:[39m[22m
[1m[31m[37m      222 │     ][32m;[37m[39m[22m
[1m[31m          ╵      [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'reviewers'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:224:4:[39m[22m
[1m[31m[37m      224 │     [32mreviewers[37m.forEach(reviewer => {[39m[22m
[1m[31m          ╵     [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:224:13:[39m[22m
[1m[31m[37m      224 │     reviewers[32m.[37mforEach(reviewer => {[39m[22m
[1m[31m          ╵              [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS7006: Parameter 'reviewer' implicitly has an 'any' type.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:224:22:[39m[22m
[1m[31m[37m      224 │     reviewers.forEach([32mreviewer[37m => {[39m[22m
[1m[31m          ╵                       [32m~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2532: Object is possibly 'undefined'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:225:25:[39m[22m
[1m[31m[37m      225 │       const voteStatus = [32mthis[37m.getReviewerVoteStatus(reviewer.vote);[39m[22m
[1m[31m          ╵                          [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'data'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:226:6:[39m[22m
[1m[31m[37m      226 │       [32mdata[37m.push([[39m[22m
[1m[31m          ╵       [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[3[39m[22m[1m[31m1mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:233:6:[39m[22m
[1m[31m[37m      233 │     })[32m;[37m[39m[22m
[1m[31m          ╵       [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ':' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:235:10:[39m[22m
[1m[31m[37m      235 │     const [32mworksheet[37m = XLSX.utils.aoa_to_sheet(data);[39m[22m
[1m[31m          ╵           [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'worksheet'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:235:10:[39m[22m
[1m[31m[37m      235 │     const [32mworksheet[37m = XLSX.utils.aoa_to_sheet(data);[39m[22m
[1m[31m          ╵           [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'XLSX'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:235:22:[39m[22m
[1m[31m[37m      235 │     const worksheet = [32mXLSX[37m.utils.aoa_to_sheet(data);[39m[22m
[1m[31m          ╵                       [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'data'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:235:46:[39m[22m
[1m[31m[37m      235 │     const worksheet = XLSX.utils.aoa_to_sheet([32mdata[37m);[39m[22m
[1m[31m          ╵                                               [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:235:51:[39m[22m
[1m[31m[37m      235 │     const worksheet = XLSX.utils.aoa_to_sheet(data)[32m;[37m[39m[22m
[1m[31m          ╵                                                    [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'worksheet'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.s[39m[22m[1m[31mervice.ts:238:4:[39m[22m
[1m[31m[37m      238 │     [32mworksheet[37m['!cols'] = [[39m[22m
[1m[31m          ╵     [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:238:13:[39m[22m
[1m[31m[37m      238 │     worksheet[32m[[37m'!cols'] = [[39m[22m
[1m[31m          ╵              [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ':' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:238:23:[39m[22m
[1m[31m[37m      238 │     worksheet['!cols'] [32m=[37m [[39m[22m
[1m[31m          ╵                        [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:244:5:[39m[22m
[1m[31m[37m      244 │     ][32m;[37m[39m[22m
[1m[31m          ╵      [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'XLSX'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:246:4:[39m[22m
[1m[31m[37m      246 │     [32mXLSX[37m.utils.book_append_sheet(workbook, worksheet, 'Reviewers');[39m[22m
[1m[31m          ╵     [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:246:8:[39m[22m
[1m[31m[37m      246 │     XLSX[32m.[37mutils.book_append_sheet(workbook, worksheet, 'Reviewers');[39m[22m
[1m[31m          ╵         [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'workbook'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:246:33:[39m[22m
[1m[31m[37m      246 │     XLSX.utils.book_append_sheet([32mworkbook[37m, worksheet, 'Reviewers');[39m[22m
[1m[31m          ╵                                  [32m~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'worksheet'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/servi[39m[22m[1m[31mces/excel-export.service.ts:246:43:[39m[22m
[1m[31m[37m      246 │     XLSX.utils.book_append_sheet(workbook, [32mworksheet[37m, 'Reviewers');[39m[22m
[1m[31m          ╵                                            [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:246:66:[39m[22m
[1m[31m[37m      246 │     XLSX.utils.book_append_sheet(workbook, worksheet, 'Reviewers')[32m;[37m[39m[22m
[1m[31m          ╵                                                                   [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1128: Declaration or statement expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:252:2:[39m[22m
[1m[31m[37m      252 │   [32mprivate[37m createWorkItemsSheet(workbook: XLSX.WorkBook, workItems...[39m[22m
[1m[31m          ╵   [32m~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'createWorkItemsSheet'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:252:10:[39m[22m
[1m[31m[37m      252 │   private [32mcreateWorkItemsSheet[37m(workbook: XLSX.WorkBook, workItems...[39m[22m
[1m[31m          ╵           [32m~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'workbook'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:252:31:[39m[22m
[1m[31m[37m      252 │ ...ivate createWorkItemsSheet([32mworkbook[37m: XLSX.WorkBook, workItems:...[39m[22m
[1m[31m          ╵                               [32m~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:252:39:[39m[22m
[1m[31m[37m      252 │ ...e createWorkItemsSheet(workbook[32m:[37m XLSX.WorkBook, workItems: any...[39m[22m
[1m[31m          ╵                                   [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'XLSX'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/ex[39m[22m[1m[31mcel-export.service.ts:252:41:[39m[22m
[1m[31m[37m      252 │ ...eateWorkItemsSheet(workbook: [32mXLSX[37m.WorkBook, workItems: any[]):...[39m[22m
[1m[31m          ╵                                 [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'workItems'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:252:56:[39m[22m
[1m[31m[37m      252 │ ...WorkItemsSheet(workbook: XLSX.WorkBook, [32mworkItems[37m: any[]): void {[39m[22m
[1m[31m          ╵                                            [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:252:65:[39m[22m
[1m[31m[37m      252 │ ...WorkItemsSheet(workbook: XLSX.WorkBook, workItems[32m:[37m any[]): void {[39m[22m
[1m[31m          ╵                                                     [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2693: 'any' only refers to a type, but is being used as a value here.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:252:67:[39m[22m
[1m[31m[37m      252 │ ...WorkItemsSheet(workbook: XLSX.WorkBook, workItems: [32many[37m[]): void {[39m[22m
[1m[31m          ╵                                                       [32m~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1011: An element access expression should take an argument.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:252:71:[39m[22m
[1m[31m[37m      252 │ ...WorkItemsSheet(workbook: XLSX.WorkBook, workItems: any[[32m[37m]): void {[39m[22m
[1m[31m          ╵                                                           [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ';' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:252:73:[39m[22m
[1m[31m[37m      252 │ ...WorkItemsSheet(workbook: XLSX.WorkBook, workItems: any[])[32m:[37m void {[39m[22m
[1m[31m          ╵                                                             [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[39m[22m[1m[31m[41;97mERROR[41;31m][0m [1mTS1005: ':' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:253:10:[39m[22m
[1m[31m[37m      253 │     const [32mdata[37m = [[39m[22m
[1m[31m          ╵           [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'data'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:253:10:[39m[22m
[1m[31m[37m      253 │     const [32mdata[37m = [[39m[22m
[1m[31m          ╵           [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:255:5:[39m[22m
[1m[31m[37m      255 │     ][32m;[37m[39m[22m
[1m[31m          ╵      [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'workItems'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:257:4:[39m[22m
[1m[31m[37m      257 │     [32mworkItems[37m.forEach(workItem => {[39m[22m
[1m[31m          ╵     [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:257:13:[39m[22m
[1m[31m[37m      257 │     workItems[32m.[37mforEach(workItem => {[39m[22m
[1m[31m          ╵              [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS7006: Parameter 'workItem' implicitly has an 'any' type.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:257:22:[39m[22m
[1m[31m[37m      257 │     workItems.forEach([32mworkItem[37m => {[39m[22m
[1m[31m          ╵                       [32m~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'data'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:258:6:[39m[22m
[1m[31m[37m      258 │       [32mdata[37m.push([[39m[22m
[1m[31m          ╵       [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-c[39m[22m[1m[31mompiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:263:6:[39m[22m
[1m[31m[37m      263 │     })[32m;[37m[39m[22m
[1m[31m          ╵       [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ':' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:265:10:[39m[22m
[1m[31m[37m      265 │     const [32mworksheet[37m = XLSX.utils.aoa_to_sheet(data);[39m[22m
[1m[31m          ╵           [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'worksheet'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:265:10:[39m[22m
[1m[31m[37m      265 │     const [32mworksheet[37m = XLSX.utils.aoa_to_sheet(data);[39m[22m
[1m[31m          ╵           [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'XLSX'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:265:22:[39m[22m
[1m[31m[37m      265 │     const worksheet = [32mXLSX[37m.utils.aoa_to_sheet(data);[39m[22m
[1m[31m          ╵                       [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'data'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:265:46:[39m[22m
[1m[31m[37m      265 │     const worksheet = XLSX.utils.aoa_to_sheet([32mdata[37m);[39m[22m
[1m[31m          ╵                                               [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:265:51:[39m[22m
[1m[31m[37m      265 │     const worksheet = XLSX.utils.aoa_to_sheet(data)[32m;[37m[39m[22m
[1m[31m          ╵                                                    [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'worksheet'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:268:4:[39m[22m
[1m[31m[37m      268 │     [32mworksheet[37m['!cols'] = [[39m[22m
[1m[31m          ╵     [32m~~~~[39m[22m[1m[31m~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:268:13:[39m[22m
[1m[31m[37m      268 │     worksheet[32m[[37m'!cols'] = [[39m[22m
[1m[31m          ╵              [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ':' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:268:23:[39m[22m
[1m[31m[37m      268 │     worksheet['!cols'] [32m=[37m [[39m[22m
[1m[31m          ╵                        [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:272:5:[39m[22m
[1m[31m[37m      272 │     ][32m;[37m[39m[22m
[1m[31m          ╵      [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS18004: No value exists in scope for the shorthand property 'XLSX'. Either declare one or provide an initializer.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:274:4:[39m[22m
[1m[31m[37m      274 │     [32mXLSX[37m.utils.book_append_sheet(workbook, worksheet, 'Work Items');[39m[22m
[1m[31m          ╵     [32m~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:274:8:[39m[22m
[1m[31m[37m      274 │     XLSX[32m.[37mutils.book_append_sheet(workbook, worksheet, 'Work Items');[39m[22m
[1m[31m          ╵         [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'workbook'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:274:33:[39m[22m
[1m[31m[37m      274 │     XLSX.utils.book_append_sheet([32mworkbook[37m, worksheet, 'Work Items');[39m[22m
[1m[31m          ╵                                  [32m~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2304: Cannot find name 'worksheet'.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:274:43:[39m[22m
[1m[31m[37m      274 │     XLSX.utils.book_append_sheet(workboo[39m[22m[1m[31mk, [32mworksheet[37m, 'Work Items');[39m[22m
[1m[31m          ╵                                            [32m~~~~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS1005: ',' expected.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/services/excel-export.service.ts:274:67:[39m[22m
[1m[31m[37m      274 │     XLSX.utils.book_append_sheet(workbook, worksheet, 'Work Items')[32m;[37m[39m[22m
[1m[31m          ╵                                                                    [32m^[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
