// File: pat-login.component.ts
import { Component } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule, MAT_SELECT_CONFIG } from '@angular/material/select';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { PrDataService } from '../../services/pr-data.service';
import { HeaderComponent } from '../header/header.component';
import {
  ApiResponseSingle,
  Project,
  Repository,
  PullRequestData,
} from '../../models/pr-interfaces.model';
import { ApiUrlService } from '../../services/api-url.service';

@Component({
  selector: 'app-pat-login',
  templateUrl: './pat-login.component.html',
  styleUrls: ['./pat-login.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatCardModule,
    MatIconModule,
    HeaderComponent,
  ],
  providers: [
    {
      provide: MAT_SELECT_CONFIG,
      useValue: { disableOptionCentering: true, disableRipple: true },
    },
  ],
})
export class PatLoginComponent {
  patToken = '';
  tokenError = false;
  projects: Project[] = [];
  selectedProject = '';
  selectedProjectId = '';
  repositories: Repository[] = [];
  selectedRepo = '';
  selectedRepoId = '';
  hideToken = true;

  constructor(
    private http: HttpClient,
    private router: Router,
    private prDataService: PrDataService,
    private apiUrlService: ApiUrlService
  ) {}

  ngOnInit() {
    ('PatLoginComponent initialized');

    // Explicitly load credentials from localStorage first
    this.prDataService.loadCredentialsFromStorage();

    // Check if we already have credentials stored
    this.prDataService.credentials$.subscribe((credentials) => {
      if (credentials) {
        ('Credentials found in login component, navigating to PR data');
        // If credentials exist, auto-navigate to pull-requests page
        this.router.navigate(['/pull-requests']);
      } else {
        ('No credentials found, staying on login page');
        // Reset form fields when returning to login page (e.g., after logout)
        this.patToken = '';
        this.projects = [];
        this.repositories = [];
        this.selectedProject = '';
        this.selectedRepo = '';
      }
    });
  }

  validateToken() {
    const url = this.apiUrlService.getGitProjectsUrl(this.patToken);

    this.http.get<ApiResponseSingle<Project[]>>(url).subscribe({
      next: (res: ApiResponseSingle<Project[]>) => {
        if (res && res.data && res.data.length > 0) {
          this.tokenError = false;
          this.projects = res.data;
          // Token is valid, projects are loaded
          // The button will hide automatically due to our *ngIf condition
        } else {
          this.tokenError = true;
          this.projects = []; // Clear projects if response is empty
        }
      },
      error: () => {
        this.tokenError = true;
        this.projects = []; // Clear projects on error
      },
    });
  }
  loadRepos() {
    if (!this.selectedProject) {
      // Return early if no project is selected
      return;
    }

    // Store the projectId for the selected project
    const selectedProjectObj = this.projects.find(
      (p) => p.name === this.selectedProject
    );
    if (selectedProjectObj) {
      this.selectedProjectId = selectedProjectObj.id;
    }

    const url = this.apiUrlService.getGitRepositoriesUrl(
      this.selectedProject,
      this.patToken
    );

    this.http.get<ApiResponseSingle<Repository[]>>(url).subscribe({
      next: (res: ApiResponseSingle<Repository[]>) => {
        if (res && res.data && res.data.length > 0) {
          this.repositories = res.data; // Update repositories with the fetched list
        } else {
          this.repositories = []; // No repositories found, clear the array
        }
      },
      error: (error) => {
        console.error('Error fetching repositories:', error);
        this.repositories = []; // Clear repositories if there's an error
      },
    });
  }
  getData() {
    // Store the repositoryId for the selected repository
    const selectedRepoObj = this.repositories.find(
      (r) => r.name === this.selectedRepo
    );
    if (selectedRepoObj) {
      this.selectedRepoId = selectedRepoObj.id;
    }

    // Store credentials in service for later use with pagination
    this.prDataService.setCredentials(
      this.patToken,
      this.selectedProject,
      this.selectedRepo,
      this.selectedProjectId,
      this.selectedRepoId
    );

    // Navigate to PR data page - initial data will be loaded there
    this.router.navigate(['/pull-requests']);
  }
  cancel() {
    this.patToken = '';
    this.projects = [];
    this.repositories = [];
    this.selectedProject = '';
    this.selectedRepo = '';
    this.selectedProjectId = '';
    this.selectedRepoId = '';
    this.tokenError = false; // Reset error state
  }
}
