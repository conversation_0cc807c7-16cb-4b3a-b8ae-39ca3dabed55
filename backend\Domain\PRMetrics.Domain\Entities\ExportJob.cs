﻿using RIB.PRMetrics.Domain.Entities.JobStatus;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.PRMetrics.Domain.Entities
{
    public class ExportJob
    {
        public Guid Uuid { get; set; }
        public string FileName { get; set; }

        public PullRequestSearchCriteria pullRequestSearchCriteria { get; set; } = null;

        public string Status { get; set; } = "Pending";
        public CancellationTokenSource TokenSource { get; set; } = new();
        public ManualResetEventSlim PauseHandle { get; set; } = new(true); // initially running
    }
}
