﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using RIB.PRMetrics.Application.UseCases.GitPullRequestThreads.Queries.GetGitPullRequestThreads;
using RIB.PRMetrics.Application.UseCases.IdentityPicker.Queries.GetIdentities;

namespace RIB.PRMetrics.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class IdentityPickerController : ControllerBase
    {
        private readonly IMediator _mediator;

        /// <summary>
        /// Initializes a new instance of the <see cref="IdentityPickerController"/> class.
        /// </summary>
        /// <param name="mediator">The mediator used to send queries to the application layer.</param>
        public IdentityPickerController(IMediator mediator)
        {
            _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
        }


        /// <summary>
        /// Retrieves a list of identities (users/groups) based on query parameters.
        /// </summary>
        /// <param name="query">The identity search query with optional filtering and pagination.</param>
        /// <returns>A list of identities wrapped in a success response or an error message.</returns>
        /// 
        [HttpGet("GetIdentities")]
        public async Task<IActionResult> GetIdentities([FromQuery] GetIdentitiesQuery query)
        {
            // Send the query to the mediator for processing, which will handle the request and retrieve the data.
            var response = await _mediator.Send(query);

            // Check if the response was successful
            if (response.Succcess)
            {
                // Return an OK response with the data if the request was successful
                return Ok(response);
            }

            // Return a BadRequest response with the error message if the request failed
            return BadRequest(response);
        }
    }
}
