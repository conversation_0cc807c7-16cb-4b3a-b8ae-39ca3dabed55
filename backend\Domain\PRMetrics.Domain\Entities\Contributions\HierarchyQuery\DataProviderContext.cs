﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Newtonsoft.Json;

namespace RIB.PRMetrics.Domain.Entities.Contributions.HierarchyQuery
{
    /// <summary>
    /// Represents the context of a data provider, which contains properties
    /// related to a specific data provider's configuration or context.
    /// </summary>
    public class DataProviderContext
    {
        /// <summary>
        /// Gets or sets the properties related to the data provider context.
        /// </summary>
        [JsonProperty("properties")] // This attribute ensures that the property is serialized/deserialized with the "properties" JSON key
        public DataProviderContextProperties Properties { get; set; }
    }
}
