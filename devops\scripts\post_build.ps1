# Some time between Angular 16 and 17, the output path was changed to include an extra /browser/ directory level.
# This issue is described in https://github.com/angular/angular-cli/issues/26304 .
# While a work-around is mentioned there - transforming
#
# "outputPath": "..."
#
# into
#
# "outputPath": {
#   "base": "...",
#   "browser": ""
# }
#
# - this is ignored when specifying the --output-path command line option.
# Therefore, we must copy the generated files to the correct location after the build.

$rootDir = [System.IO.Path]::Combine("$PSScriptRoot", '..', '..')

$destPath = [System.IO.Path]::Combine("$rootDir", 'frontend', 'dist', 'web')
if (Test-Path "$destPath") {
	$generatedPath = [System.IO.Path]::Combine("$destPath", 'browser')
	if (Test-Path "$generatedPath") {
		Get-ChildItem -Path "$generatedPath/*" -Recurse | Move-Item -Destination "$destPath" | Out-Null
		Remove-Item -Path "$generatedPath" -Force | Out-Null
		
		Write-Host "Front-end files location fixed to $destPath."
	}
}
# otherwise, this is probably being run post-deployment, not post-build
