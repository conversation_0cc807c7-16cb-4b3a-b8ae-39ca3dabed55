# Dashboard Component

The Dashboard component displays various analytics and visualizations for Pull Request (PR) data.

## Features

- **Summary Statistics**: Shows total PRs, completed PRs, active PRs, and draft PRs
- **PR Status Distribution**: A pie chart showing the distribution of PRs by status
- **PR Creation Timeline**: A bar chart showing PR creation activity over the last 30 days
- **PR Review Time Distribution**: A doughnut chart showing how long PRs take to be reviewed
- **Repository Activity**: A radar chart showing various repository activity metrics

## Implementation

The component uses the Chart.js library via the ng2-charts wrapper to render visualizations. Data is fetched from the PR Data service and processed for display.

### Charts

1. **PR Status Distribution (Pie Chart)**
   - Shows the proportion of PRs in different statuses (Active, Completed, Abandoned, Draft)
   - Uses different colors to distinguish each status category

2. **PR Creation Timeline (Bar Chart)**
   - Shows PR creation activity over the last 30 days
   - Helps identify patterns in team activity

3. **PR Review Time Distribution (Doughnut Chart)**
   - Categorizes PRs by review time (< 1 day, 1-3 days, 3-7 days, > 7 days)
   - Helps identify bottlenecks in the review process

4. **Repository Activity (Radar Chart)**
   - Multi-dimensional view of repository metrics
   - Shows PRs created, merged, abandoned, comments, and iterations

## Usage

The dashboard automatically loads data when a user is logged in with valid credentials. Users can refresh the data using the "Refresh Data" button.

## Dependencies

- ng2-charts
- chart.js
- Angular Material components
