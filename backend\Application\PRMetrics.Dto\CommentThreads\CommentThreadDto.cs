﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto.CommentThreads
{
    /// <summary>
    /// Data Transfer Object (DTO) representing a comment thread.
    /// Contains information about the thread itself as well as the list of comments within it.
    /// </summary>
    public class CommentThreadDto
    {
        /// <summary>
        /// Gets or sets the unique identifier of the comment thread.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the list of comments within this comment thread.
        /// </summary>
        public List<CommentDto> Comments { get; set; }

        
        /// <summary>
        /// Gets or sets the date and time when the comment thread was first published.
        /// </summary>
        public DateTime PublishedDate { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the comment thread was last updated.
        /// </summary>
        public DateTime LastUpdatedDate { get; set; }

        /// <summary>
        /// Gets or sets the status of the comment thread (e.g., active, closed, resolved).
        /// </summary>
        public string Status { get; set; }
    }
}
