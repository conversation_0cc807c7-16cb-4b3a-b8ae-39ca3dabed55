import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from './config.service';

export interface ExportRequest {
  project: string;
  repository: string;
  patToken: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  searchTerm?: string;
}

export interface ExportResponse {
  succcess: boolean;
  message: string;
  data: string; // UUID of the export job
}

export interface ProcessProgressDto {
  uuid: string;
  percentage: number;
  status: string;
  jobStatus: string;
  fileName: string;
  fileUrl?: string;
}

export interface ProgressResponse {
  succcess: boolean;
  message: string;
  data: ProcessProgressDto[];
}

export interface ActionResponse {
  succcess: boolean;
  message: string;
  data: ProcessProgressDto;
}

@Injectable({
  providedIn: 'root'
})
export class ExportApiService {
  private readonly baseUrl: string;

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    this.baseUrl = `${this.configService.API_BASE_URL}/GitPullRequests`;
  }

  /**
   * Start Excel export process
   * @param request Export request parameters
   * @returns Observable with export job UUID
   */
  startExport(request: ExportRequest): Observable<ExportResponse> {
    const params = new HttpParams()
      .set('project', request.project)
      .set('repository', request.repository)
      .set('patToken', request.patToken)
      .set('status', request.status || 'all')
      .set('startDate', request.startDate || '')
      .set('endDate', request.endDate || '')
      .set('searchTerm', request.searchTerm || '');

    return this.http.get<ExportResponse>(`${this.baseUrl}/ExcelReport`, { params });
  }

  /**
   * Get export progress for specific UUIDs
   * @param uuids Array of export job UUIDs
   * @returns Observable with progress information
   */
  getExportProgress(uuids: string[]): Observable<ProgressResponse> {
    let params = new HttpParams();
    uuids.forEach(uuid => {
      params = params.append('uuid', uuid);
    });

    return this.http.get<ProgressResponse>(`${this.baseUrl}/GetExcelReportProgress`, { params });
  }

  /**
   * Take action on export job (pause, resume, cancel)
   * @param uuid Export job UUID
   * @param action Action to take ('Paused', 'Running', 'Cancelled')
   * @returns Observable with action result
   */
  takeAction(uuid: string, action: string): Observable<ActionResponse> {
    const body = {
      uuid: uuid,
      jobStatus: action
    };

    return this.http.patch<ActionResponse>(`${this.baseUrl}/TakeActionExcelReportExport`, body);
  }

  /**
   * Pause export job
   * @param uuid Export job UUID
   * @returns Observable with action result
   */
  pauseExport(uuid: string): Observable<ActionResponse> {
    return this.takeAction(uuid, 'Paused');
  }

  /**
   * Resume export job
   * @param uuid Export job UUID
   * @returns Observable with action result
   */
  resumeExport(uuid: string): Observable<ActionResponse> {
    return this.takeAction(uuid, 'Running');
  }

  /**
   * Cancel export job
   * @param uuid Export job UUID
   * @returns Observable with action result
   */
  cancelExport(uuid: string): Observable<ActionResponse> {
    return this.takeAction(uuid, 'Cancelled');
  }

  /**
   * Delete export job
   * @param uuid Export job UUID
   * @returns Observable with deletion result
   */
  deleteExport(uuid: string): Observable<ActionResponse> {
    const params = new HttpParams().set('uuid', uuid);
    return this.http.delete<ActionResponse>(`${this.baseUrl}/DeleteExcelReportExport`, { params });
  }

  /**
   * Download completed export file
   * @param uuid Export job UUID
   * @returns Observable with file blob
   */
  downloadExport(uuid: string): Observable<Blob> {
    const params = new HttpParams().set('uuid', uuid);
    return this.http.get(`${this.baseUrl}/ExcelReportDownload`, {
      params,
      responseType: 'blob'
    });
  }

  /**
   * Download export file and trigger browser download
   * @param uuid Export job UUID
   * @param fileName File name for download
   */
  async downloadAndSaveFile(uuid: string, fileName: string): Promise<void> {
    try {
      const blob = await this.downloadExport(uuid).toPromise();
      if (blob) {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Error downloading file:', error);
      throw error;
    }
  }
}
