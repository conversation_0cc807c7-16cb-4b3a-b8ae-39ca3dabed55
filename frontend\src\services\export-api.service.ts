import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from './config.service';

export interface ExportRequest {
  project: string;
  repositories: string;
  patToken: string;
  status?: string;
  minTime?: string;
  maxTime?: string;
  top?: number;
  skip?: number;
  includeLinks?: boolean;
  reviewerId?: string;
  sourceRefName?: string;
  sourceRepositoryId?: string;
  creatorId?: string;
  assigneeId?: string;
  targetRefName?: string;
}

export interface ExportResponse {
  succcess: boolean;
  message: string;
  data: string; // UUID of the export job
}

export interface ProcessProgressDto {
  uuid: string;
  percentage: number;
  status: string;
  jobStatus: string;
  fileName: string;
  fileUrl?: string;
}

export interface ProgressResponse {
  succcess: boolean;
  message: string;
  data: ProcessProgressDto[];
}

export interface ActionResponse {
  succcess: boolean;
  message: string;
  data: ProcessProgressDto;
}

@Injectable({
  providedIn: 'root'
})
export class ExportApiService {
  private readonly baseUrl: string;

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    this.baseUrl = `${this.configService.API_BASE_URL}/GitPullRequests`;
  }

  /**
   * Start Excel export process
   * @param request Export request parameters
   * @returns Observable with export job UUID
   */
  startExport(request: ExportRequest): Observable<ExportResponse> {
    let params = new HttpParams()
      .set('project', request.project)
      .set('repositories', request.repositories)
      .set('patToken', request.patToken);

    // Add optional parameters only if they have values
    if (request.status && request.status !== 'all') {
      params = params.set('status', request.status);
    }
    if (request.minTime) {
      params = params.set('minTime', request.minTime);
    }
    if (request.maxTime) {
      params = params.set('maxTime', request.maxTime);
    }
    if (request.top) {
      params = params.set('top', request.top.toString());
    }
    if (request.skip) {
      params = params.set('skip', request.skip.toString());
    }
    if (request.includeLinks !== undefined) {
      params = params.set('includeLinks', request.includeLinks.toString());
    }
    if (request.reviewerId) {
      params = params.set('reviewerId', request.reviewerId);
    }
    if (request.sourceRefName) {
      params = params.set('sourceRefName', request.sourceRefName);
    }
    if (request.sourceRepositoryId) {
      params = params.set('sourceRepositoryId', request.sourceRepositoryId);
    }
    if (request.creatorId) {
      params = params.set('creatorId', request.creatorId);
    }
    if (request.assigneeId) {
      params = params.set('assigneeId', request.assigneeId);
    }
    if (request.targetRefName) {
      params = params.set('targetRefName', request.targetRefName);
    }

    return this.http.get<ExportResponse>(`${this.baseUrl}/ExcelReport`, { params });
  }

  /**
   * Get export progress for specific UUIDs
   * @param uuids Array of export job UUIDs
   * @returns Observable with progress information
   */
  getExportProgress(uuids: string[]): Observable<ProgressResponse> {
    let params = new HttpParams();
    uuids.forEach(uuid => {
      params = params.append('uuid', uuid);
    });

    return this.http.get<ProgressResponse>(`${this.baseUrl}/GetExcelReportProgress`, { params });
  }

  /**
   * Take action on export job (pause, resume, cancel)
   * @param uuid Export job UUID
   * @param action Action to take ('Paused', 'Resume', 'Cancelled')
   * @returns Observable with action result
   */
  takeAction(uuid: string, action: string): Observable<ActionResponse> {
    const body = {
      uuid: uuid,
      jobStatus: action
    };

    return this.http.patch<ActionResponse>(`${this.baseUrl}/TakeActionExcelReportExport`, body);
  }

  /**
   * Pause export job
   * @param uuid Export job UUID
   * @returns Observable with action result
   */
  pauseExport(uuid: string): Observable<ActionResponse> {
    return this.takeAction(uuid, 'Paused');
  }

  /**
   * Resume export job
   * @param uuid Export job UUID
   * @returns Observable with action result
   */
  resumeExport(uuid: string): Observable<ActionResponse> {
    return this.takeAction(uuid, 'Resume');
  }

  /**
   * Cancel export job
   * @param uuid Export job UUID
   * @returns Observable with action result
   */
  cancelExport(uuid: string): Observable<ActionResponse> {
    return this.takeAction(uuid, 'Cancelled');
  }

  /**
   * Delete export job
   * @param uuid Export job UUID
   * @returns Observable with deletion result
   */
  deleteExport(uuid: string): Observable<ActionResponse> {
    const params = new HttpParams().set('uuid', uuid);
    return this.http.delete<ActionResponse>(`${this.baseUrl}/DeleteExcelReportExport`, { params });
  }

  /**
   * Download completed export file
   * @param uuid Export job UUID
   * @returns Observable with file blob
   */
  downloadExport(uuid: string): Observable<Blob> {
    const params = new HttpParams().set('uuid', uuid);
    return this.http.get(`${this.baseUrl}/ExcelReportDownload`, {
      params,
      responseType: 'blob'
    });
  }

  /**
   * Download export file and trigger browser download
   * @param uuid Export job UUID
   * @param fileName File name for download
   */
  async downloadAndSaveFile(uuid: string, fileName: string): Promise<void> {
    try {
      const blob = await this.downloadExport(uuid).toPromise();
      if (blob) {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Error downloading file:', error);
      throw error;
    }
  }
}
