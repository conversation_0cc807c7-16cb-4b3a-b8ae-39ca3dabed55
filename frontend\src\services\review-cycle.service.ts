import { Injectable } from '@angular/core';
import { CommentThread } from '../models/pr-interfaces.model';
import { ReviewCycleMetrics } from '../models/review-cycle.model';

@Injectable({
  providedIn: 'root'
})
export class ReviewCycleService {
  
  constructor() { }
  
  /**
   * Calculate the number of review cycles for a pull request based on comment threads
   * 
   * Review cycles are defined as:
   * - Zero review cycles: The author merges a PR without any review.
   * - One review cycle: The author opens a PR, and the reviewer approves the merge.
   * - Two review cycles: Author opens a PR, reviewer provides suggestions.
   *   Author resubmits PR for a second review. Reviewer approves and merges.
   * 
   * @param commentThreads Array of comment threads for the pull request
   * @returns Review cycle metrics
   */
  calculateReviewCycles(commentThreads: CommentThread[]): ReviewCycleMetrics {
    // If no threads, it's likely zero cycles (author merged without review)
    if (!commentThreads || commentThreads.length === 0) {
      return {
        cycleCount: 0,
        description: 'Zero review cycles (merged without review)'
      };
    }
    
    // Sort threads by date to analyze them in chronological order
    const sortedThreads = [...commentThreads].sort(
      (a, b) => new Date(a.publishedDate).getTime() - new Date(b.publishedDate).getTime()
    );
    
    // Group comments by participants to detect review cycles
    const threadGroups = this.groupThreadsByInteraction(sortedThreads);
    
    // Calculate review cycles based on interaction patterns
    if (threadGroups.length === 0) {
      return {
        cycleCount: 1,
        description: 'One review cycle (approved with no comments)'
      };
    }
    
    // Each group of back-and-forth comments + 1 constitutes a review cycle
    const cycleCount = threadGroups.length;
    
    let description = '';
    if (cycleCount === 1) {
      description = 'One review cycle (reviewer provided comments, author addressed them)';
    } else {
      description = `${cycleCount} review cycles (multiple rounds of review)`;
    }
    
    return {
      cycleCount,
      description
    };
  }
  
  /**
   * Group threads by interaction patterns to identify review cycles
   * 
   * A new review cycle typically starts when:
   * 1. Comments are made by reviewers
   * 2. Author responds to the comments
   * 3. Code is updated
   * 4. Reviewers respond again
   * 
   * @param threads Sorted comment threads
   * @returns Array of thread groups representing review cycles
   */
  private groupThreadsByInteraction(threads: CommentThread[]): CommentThread[][] {
    const threadGroups: CommentThread[][] = [];
    let currentGroup: CommentThread[] = [];
    let reviewersResponded = false;
    let authorResponded = false;
    
    // Extract unique authors from all threads
    const allAuthors = new Set<string>();
    threads.forEach(thread => {
      thread.comments?.forEach(comment => {
        if (comment.author?.id) {
          allAuthors.add(comment.author.id);
        }
      });
    });
    
    // If only one author in all threads (author talking to themselves), it's just one cycle
    if (allAuthors.size <= 1) {
      if (threads.length > 0) {
        threadGroups.push([...threads]);
      }
      return threadGroups;
    }

    // Find PR author (usually the person who created the first thread)
    const firstThread = threads[0];
    const prAuthorId = firstThread?.comments?.[0]?.author?.id;
    
    threads.forEach(thread => {
      // Start a new group if necessary
      if (currentGroup.length === 0) {
        currentGroup.push(thread);
        // Determine if this thread has reviewer comments
        const hasReviewerComments = thread.comments?.some(
          c => c.author?.id !== prAuthorId
        ) || false;
        reviewersResponded = hasReviewerComments;
        return;
      }
      
      // Check if this thread has author responses after reviewer comments
      const authorResponses = thread.comments?.filter(
        c => c.author?.id === prAuthorId
      ) || [];
      
      // Check if this thread has reviewer responses
      const reviewerResponses = thread.comments?.filter(
        c => c.author?.id !== prAuthorId
      ) || [];
      
      // If thread status is fixed, it indicates a cycle completed
      if (thread.status === 'fixed' && reviewersResponded) {
        authorResponded = true;
      }
      
      // If reviewers have responded and author has responded, we may have a complete cycle
      if (reviewersResponded && authorResponded && reviewerResponses.length > 0 && 
          authorResponses.length > 0) {
        // Finish the current group and start a new one
        threadGroups.push([...currentGroup]);
        currentGroup = [thread];
        reviewersResponded = reviewerResponses.length > 0;
        authorResponded = false;
        return;
      }
      
      // Add to current group
      currentGroup.push(thread);
      
      // Update flags based on this thread
      if (reviewerResponses.length > 0) {
        reviewersResponded = true;
      }
      if (authorResponses.length > 0 && reviewersResponded) {
        authorResponded = true;
      }
    });
    
    // Add any remaining threads to a group
    if (currentGroup.length > 0) {
      threadGroups.push([...currentGroup]);
    }
    
    return threadGroups;
  }
}
