﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using MediatR;
using   RIB.PRMetrics.Application.Dto;
using   RIB.PRMetrics.Application.Interface.Persistence;
using   RIB.PRMetrics.Application.UseCases.Commons.Bases;
using   RIB.PRMetrics.Application.UseCases.GitRepositories.Queries.GetGitRepositories;

/// <summary>
/// Hand<PERSON> for processing the GetGitRepositoriesQuery to fetch a list of Git repositories.
/// Uses the unit of work pattern to interact with the persistence layer and AutoMapper for DTO mapping.
/// </summary>
public class GetGitRepositoryHandler : IRequestHandler<GetGitRepositoriesQuery, BaseReponseGeneric<List<GitRepositoryDto>>>
{
    private readonly IUnitOfWork _unitOfWork;  // The unit of work to access repositories
    private readonly IMapper _mapper;  // AutoMapper instance to map domain entities to DTOs

    /// <summary>
    /// Constructor for the handler to initialize dependencies.
    /// </summary>
    /// <param name="unitOfWork">The unit of work instance</param>
    /// <param name="mapper">The AutoMapper instance for mapping objects</param>
    public GetGitRepositoryHandler(IUnitOfWork unitOfWork, IMapper mapper)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));  // Ensure unitOfWork is not null
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));  // Ensure mapper is not null
    }

    /// <summary>
    /// Handles the GetGitRepositoriesQuery to fetch a list of Git repositories based on the provided project and token.
    /// </summary>
    /// <param name="request">The GetGitRepositoriesQuery containing the project and PAT token</param>
    /// <param name="cancellationToken">Cancellation token to cancel the request if needed</param>
    /// <returns>A BaseReponseGeneric containing a list of GitRepositoryDto objects</returns>
    public async Task<BaseReponseGeneric<List<GitRepositoryDto>>> Handle(GetGitRepositoriesQuery request, CancellationToken cancellationToken)
    {
        var response = new BaseReponseGeneric<List<GitRepositoryDto>>();  // Initialize the response object

        try
        {
            // Fetch the Git repositories using the unit of work and provided project and PAT token
            var gitRepos = await _unitOfWork.GitRepoRepository.GetGitRepositoriesAsync(request.Project, request.PATToken);

            // Check if the repositories were successfully fetched
            if (gitRepos is not null)
            {
                // Map the domain entities to DTOs
                response.Data = _mapper.Map<List<GitRepositoryDto>>(gitRepos);
                response.Succcess = true;  // Indicate success
                response.Message = "Git Repositories list fetch succeed!";  // Success message
            }
        }
        catch (Exception ex)
        {
            // If an error occurs, capture the message in the response
            response.Message = ex.Message;
        }

        return response;  // Return the response containing the list of Git repositories
    }
}
