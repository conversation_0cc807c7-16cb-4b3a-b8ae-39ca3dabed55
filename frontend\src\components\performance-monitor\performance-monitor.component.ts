import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { PerformanceService } from '../../services/performance.service';
import { interval, Subject, takeUntil } from 'rxjs';

interface PerformanceMetrics {
  memoryUsage: number;
  loadTime: number;
  renderTime: number;
  bundleSize: number;
  cacheHitRate: number;
}

@Component({
  selector: 'app-performance-monitor',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatProgressBarModule
  ],
  template: `
    <mat-card class="performance-card" *ngIf="showMonitor">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>speed</mat-icon>
          Performance Monitor
        </mat-card-title>
        <button mat-icon-button (click)="toggleMonitor()" class="close-btn">
          <mat-icon>close</mat-icon>
        </button>
      </mat-card-header>
      
      <mat-card-content>
        <div class="metrics-grid">
          <!-- Memory Usage -->
          <div class="metric-item">
            <div class="metric-label">Memory Usage</div>
            <div class="metric-value">{{ metrics.memoryUsage.toFixed(1) }} MB</div>
            <mat-progress-bar 
              mode="determinate" 
              [value]="getMemoryPercentage()"
              [color]="getMemoryColor()">
            </mat-progress-bar>
          </div>

          <!-- Load Time -->
          <div class="metric-item">
            <div class="metric-label">Page Load Time</div>
            <div class="metric-value">{{ metrics.loadTime.toFixed(0) }} ms</div>
            <mat-progress-bar 
              mode="determinate" 
              [value]="getLoadTimePercentage()"
              [color]="getLoadTimeColor()">
            </mat-progress-bar>
          </div>

          <!-- Render Time -->
          <div class="metric-item">
            <div class="metric-label">Render Time</div>
            <div class="metric-value">{{ metrics.renderTime.toFixed(0) }} ms</div>
            <mat-progress-bar 
              mode="determinate" 
              [value]="getRenderTimePercentage()"
              [color]="getRenderTimeColor()">
            </mat-progress-bar>
          </div>

          <!-- Cache Hit Rate -->
          <div class="metric-item">
            <div class="metric-label">Cache Hit Rate</div>
            <div class="metric-value">{{ metrics.cacheHitRate.toFixed(1) }}%</div>
            <mat-progress-bar 
              mode="determinate" 
              [value]="metrics.cacheHitRate"
              color="primary">
            </mat-progress-bar>
          </div>
        </div>

        <div class="actions">
          <button mat-button (click)="clearCaches()">
            <mat-icon>clear_all</mat-icon>
            Clear Caches
          </button>
          <button mat-button (click)="runOptimizations()">
            <mat-icon>tune</mat-icon>
            Optimize
          </button>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Performance Toggle Button -->
    <button 
      mat-fab 
      mini 
      class="performance-toggle"
      (click)="toggleMonitor()"
      *ngIf="!showMonitor"
      matTooltip="Performance Monitor">
      <mat-icon>speed</mat-icon>
    </button>
  `,
  styles: [`
    .performance-card {
      position: fixed;
      top: 20px;
      right: 20px;
      width: 300px;
      z-index: 1000;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
    }

    .performance-toggle {
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 1000;
      background: #1976d2;
      color: white;
    }

    .close-btn {
      margin-left: auto;
    }

    .metrics-grid {
      display: grid;
      gap: 16px;
      margin-bottom: 16px;
    }

    .metric-item {
      .metric-label {
        font-size: 0.875rem;
        color: #666;
        margin-bottom: 4px;
      }

      .metric-value {
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 8px;
      }
    }

    .actions {
      display: flex;
      gap: 8px;
      justify-content: space-between;
    }

    @media (max-width: 768px) {
      .performance-card {
        width: calc(100vw - 40px);
        right: 20px;
      }
    }
  `]
})
export class PerformanceMonitorComponent implements OnInit, OnDestroy {
  showMonitor = false;
  metrics: PerformanceMetrics = {
    memoryUsage: 0,
    loadTime: 0,
    renderTime: 0,
    bundleSize: 0,
    cacheHitRate: 0
  };

  private destroy$ = new Subject<void>();

  constructor(private performanceService: PerformanceService) {}

  ngOnInit() {
    // Update metrics every 5 seconds
    interval(5000)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateMetrics();
      });

    // Initial metrics load
    this.updateMetrics();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleMonitor() {
    this.showMonitor = !this.showMonitor;
  }

  private updateMetrics() {
    // Memory usage
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.metrics.memoryUsage = memory.usedJSHeapSize / (1024 * 1024);
    }

    // Load time
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigation) {
      this.metrics.loadTime = navigation.loadEventEnd - navigation.fetchStart;
    }

    // Render time (First Contentful Paint)
    const paintEntries = performance.getEntriesByType('paint');
    const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    if (fcp) {
      this.metrics.renderTime = fcp.startTime;
    }

    // Cache hit rate (simulated - would need actual implementation)
    this.metrics.cacheHitRate = Math.random() * 100;
  }

  getMemoryPercentage(): number {
    // Assume 100MB as max reasonable memory usage
    return Math.min((this.metrics.memoryUsage / 100) * 100, 100);
  }

  getMemoryColor(): string {
    const percentage = this.getMemoryPercentage();
    if (percentage > 80) return 'warn';
    if (percentage > 60) return 'accent';
    return 'primary';
  }

  getLoadTimePercentage(): number {
    // Assume 3000ms as max acceptable load time
    return Math.min((this.metrics.loadTime / 3000) * 100, 100);
  }

  getLoadTimeColor(): string {
    const time = this.metrics.loadTime;
    if (time > 2000) return 'warn';
    if (time > 1000) return 'accent';
    return 'primary';
  }

  getRenderTimePercentage(): number {
    // Assume 1000ms as max acceptable render time
    return Math.min((this.metrics.renderTime / 1000) * 100, 100);
  }

  getRenderTimeColor(): string {
    const time = this.metrics.renderTime;
    if (time > 800) return 'warn';
    if (time > 400) return 'accent';
    return 'primary';
  }

  clearCaches() {
    this.performanceService.clearCaches();
    // Clear browser caches if possible
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => caches.delete(name));
      });
    }
  }

  runOptimizations() {
    // Trigger garbage collection if available
    if ('gc' in window) {
      (window as any).gc();
    }

    // Clear performance entries
    performance.clearMarks();
    performance.clearMeasures();

    // Clear service caches
    this.performanceService.clearCaches();
  }
}
