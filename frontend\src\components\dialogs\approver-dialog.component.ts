import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  MatDialogModule,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { ConfigService } from '../../services/config.service';

@Component({
  selector: 'app-approver-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
  ],
  template: `
    <h2 mat-dialog-title>Pull Request Approvers</h2>
    <mat-dialog-content>
      <div class="approver-cards-container">
        <mat-card *ngFor="let reviewer of data.reviewers" class="approver-card">
          <mat-card-content>
            <div class="approver-info">
              <mat-icon>person</mat-icon>
              <span class="approver-name"
                >{{ reviewer.displayName
                }}<span style="color: red;">{{
                  reviewer.isRequired ? '*' : ''
                }}</span></span
              >
            </div>              <div class="approver-vote" *ngIf="reviewer.vote !== undefined">
              <mat-icon
                [ngClass]="{
                  approved: reviewer.vote === configService.REVIEWER_VOTE.APPROVED,
                  'approved-with-suggestions': reviewer.vote === configService.REVIEWER_VOTE.APPROVED_WITH_SUGGESTIONS,
                  'no-vote': reviewer.vote === configService.REVIEWER_VOTE.NO_VOTE,
                  waiting: reviewer.vote === configService.REVIEWER_VOTE.WAITING_FOR_AUTHOR,
                  rejected: reviewer.vote === configService.REVIEWER_VOTE.REJECTED
                }"
              >
                {{ getVoteIcon(reviewer.vote) }}
              </mat-icon>
              <span
                [ngClass]="{
                  approved: reviewer.vote === configService.REVIEWER_VOTE.APPROVED,
                  'approved-with-suggestions': reviewer.vote === configService.REVIEWER_VOTE.APPROVED_WITH_SUGGESTIONS,
                  'no-vote': reviewer.vote === configService.REVIEWER_VOTE.NO_VOTE,
                  waiting: reviewer.vote === configService.REVIEWER_VOTE.WAITING_FOR_AUTHOR,
                  rejected: reviewer.vote === configService.REVIEWER_VOTE.REJECTED
                }"
              >
                {{ getVoteStatus(reviewer.vote) }}
              </span>
            </div>
          </mat-card-content>
        </mat-card>
        <div *ngIf="data.reviewers.length === 0" class="no-approvers">
          <mat-icon>people_outline</mat-icon>
          <p>No approvers assigned to this pull request.</p>
        </div>
      </div>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button mat-dialog-close>Close</button>
    </mat-dialog-actions>
  `,
  styles: [
    `
      .approver-cards-container {
        display: flex;
        flex-direction: column;
        gap: 10px;
        max-height: 60vh;
        overflow-y: auto;
      }
      .approver-card {
        margin-bottom: 8px;
        border-left: 4px solid #ccc;
      }
      .approver-info {
        display: flex;
        align-items: center;
        gap: 8px;
      }
      .approver-name {
        font-weight: 500;
      }
      .approver-vote {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 8px;
        padding-left: 32px;
      }
      .approved {
        color: #28a745; /* Green */
      }
      .approved-with-suggestions {
        color: #4caf50; /* Light Green */
      }
      .no-vote {
        color: #9e9e9e; /* Gray */
      }
      .waiting {
        color: #ffc107; /* Yellow/Amber */
      }
      .rejected {
        color: #dc3545; /* Red */
      }
      .no-approvers {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        color: #666;
      }
      .no-approvers mat-icon {
        font-size: 48px;
        height: 48px;
        width: 48px;
        margin-bottom: 16px;
      }
    `,
  ],
})
export class ApproverDialogComponent {  constructor(
    public dialogRef: MatDialogRef<ApproverDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { reviewers: any[] },
    public configService: ConfigService
  ) {}getVoteIcon(vote: number): string {
    switch (vote) {
      case this.configService.REVIEWER_VOTE.APPROVED:
        return 'check_circle';
      case this.configService.REVIEWER_VOTE.APPROVED_WITH_SUGGESTIONS:
        return 'thumb_up';
      case this.configService.REVIEWER_VOTE.NO_VOTE:
        return 'radio_button_unchecked';
      case this.configService.REVIEWER_VOTE.WAITING_FOR_AUTHOR:
        return 'hourglass_empty';
      case this.configService.REVIEWER_VOTE.REJECTED:
        return 'cancel';
      default:
        return 'help_outline';
    }
  }

  getVoteStatus(vote: number): string {
    switch (vote) {
      case this.configService.REVIEWER_VOTE.APPROVED:
        return 'Approved';
      case this.configService.REVIEWER_VOTE.APPROVED_WITH_SUGGESTIONS:
        return 'Approved with suggestions';
      case this.configService.REVIEWER_VOTE.NO_VOTE:
        return 'No vote';
      case this.configService.REVIEWER_VOTE.WAITING_FOR_AUTHOR:
        return 'Waiting for author';
      case this.configService.REVIEWER_VOTE.REJECTED:
        return 'Rejected';
      default:
        return 'Unknown';
    }
  }
}
