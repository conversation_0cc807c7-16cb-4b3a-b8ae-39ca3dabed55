﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using System.Net;
using Newtonsoft.Json;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Domain.Entities.PullRequestBuild;
using RIB.PRMetrics.Persistence.Common;

namespace RIB.PRMetrics.Persistence.Repositories
{
    /// <summary>
    /// Repository class for interacting with the Git Pull Request Build Timeline API.
    /// </summary>
    public class GitPullRequestBuildTimeLineRepository : IGitPullRequestBuildTimeLineRepository
    {
        private readonly AzureDevopsUrlBuilder azureDevopsUrlBuilder;
        private readonly AZDevopsClientCommon aZDevopsClientCommon;

        /// <summary>
        /// Constructor that initializes dependencies required for making API calls.
        /// </summary>
        /// <param name="_azureDevopsUrlBuilder">The URL builder used for constructing API URIs.</param>
        /// <param name="httpClientFactory">Factory used for creating HTTP clients.</param>
        /// <param name="_aZDevopsClientCommon">The client used for making HTTP requests to Azure DevOps services.</param>
        public GitPullRequestBuildTimeLineRepository(AzureDevopsUrlBuilder _azureDevopsUrlBuilder, IHttpClientFactory httpClientFactory, AZDevopsClientCommon _aZDevopsClientCommon)
        {
            azureDevopsUrlBuilder = _azureDevopsUrlBuilder ?? throw new ArgumentNullException(nameof(_azureDevopsUrlBuilder));
            aZDevopsClientCommon = _aZDevopsClientCommon ?? throw new ArgumentNullException(nameof(_aZDevopsClientCommon));
        }

        /// <summary>
        /// Fetches the Git Pull Request Build Timeline from the Azure DevOps API.
        /// </summary>
        /// <param name="ProjectId">The ID of the project containing the pull request build.</param>
        /// <param name="buildId">The ID of the specific build to retrieve the timeline for.</param>
        /// <param name="PATToken">The Personal Access Token used for authentication with Azure DevOps.</param>
        /// <returns>Returns a <see cref="TimeLineResponse"/> object containing the build timeline information.</returns>
        /// <exception cref="Exception">Throws an exception if the request to the API fails.</exception>
        public async Task<TimeLineResponse> GetGitPullRequestBuildTimeLineAsync(string ProjectId, string buildId, string PATToken)
        {
            // Build the API URL using the project and build ID
            var url = azureDevopsUrlBuilder.BuildGitPullRequestBuildTimeLineUri(ProjectId, buildId);

            // Send the HTTP GET request to the Azure DevOps API
            var response = await aZDevopsClientCommon.GetAsync(url, PATToken);

            // If the response is successful, parse the JSON response into the expected data structure
            if (response.IsSuccessStatusCode && response.StatusCode == HttpStatusCode.OK)
            {
                var jsonResponse = await response.Content.ReadAsStringAsync();
                var template = new TimeLineResponse();

                // Deserialize the JSON response into the TimeLineResponse object
                var pullRequestsResponse = JsonConvert.DeserializeAnonymousType(jsonResponse, template);
                return pullRequestsResponse ?? new TimeLineResponse();
            }

            // Throw an exception if the API request was unsuccessful
            throw new Exception($"Error retrieving Git Pull Request Build Timeline: {response.ReasonPhrase}");
        }
    }
}
