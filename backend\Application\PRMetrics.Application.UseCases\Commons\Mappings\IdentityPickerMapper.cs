﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using RIB.PRMetrics.Application.Dto.IdentityPicker;
using RIB.PRMetrics.Domain.Entities.IdentityPicker;

namespace RIB.PRMetrics.Application.UseCases.Commons.Mappings
{
    /// <summary>
    /// AutoMapper profile for mapping between IdentityPicker domain entities and DTOs.
    /// </summary>
    public class IdentityPickerMapper : Profile
    {
        public IdentityPickerMapper()
        {
            // Configure mapping between IdentitiesItem (domain model) and IdentitiesItemDto (data transfer object)
            // ReverseMap() allows mapping in both directions (DTO to domain model and vice versa)
            CreateMap<IdentitiesItem, IdentitiesItemDto>().ReverseMap();

            // You can customize mappings here if property names differ
            // For example:
            // .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
        }
    }
}
