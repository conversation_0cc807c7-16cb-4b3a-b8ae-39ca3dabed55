﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using RIB.PRMetrics.Domain.Entities;

namespace RIB.PRMetrics.Application.Interface.Persistence
{
    /// <summary>
    /// Interface for a repository that handles Git pull request thread data operations.
    /// This extends a generic repository to include specific operations for Git pull request threads.
    /// </summary>
    public interface IGitPullRequestThreadsRepository : IGenericRepository<GitPullRequestThreadsResponse>
    {
        /// <summary>
        /// Asynchronously retrieves the Git pull request threads for a specific pull request.
        /// This method fetches the threads related to a given pull request, along with any associated data (e.g., comments, state).
        /// </summary>
        /// <param name="gitCommon">An instance of GitCommon that contains common Git-related parameters, such as repository information.</param>
        /// <param name="pullRequestId">The unique identifier for the pull request whose threads need to be retrieved.</param>
        /// <param name="prAuthorId">The unique identifier for the pull request Author.</param>
        /// <returns>A task that represents the asynchronous operation, containing the response with pull request threads data.</returns>
        Task<GitPullRequestThreadsResponse> GetGitPullRequestThreadsAsync(GitCommon gitCommon, int pullRequestId,string prAuthorId);
    }
}
