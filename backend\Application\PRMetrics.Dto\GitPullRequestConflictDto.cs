﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto
{
    /// <summary>
    /// Represents a Git pull request conflict.
    /// This class contains the details about a specific conflict that occurred during a pull request.
    /// It includes the unique identifier, type, path, and the URL to access more information about the conflict.
    /// </summary>
    public class GitPullRequestConflictDto
    {
        /// <summary>
        /// Gets or sets the unique identifier for the conflict.
        /// This ID is used to uniquely identify the conflict in the system.
        /// </summary>
        public int ConflictId { get; set; }

        /// <summary>
        /// Gets or sets the type of the conflict.
        /// This could represent the type of conflict (e.g., merge conflict, file conflict, etc.).
        /// </summary>
        public string ConflictType { get; set; }

        /// <summary>
        /// Gets or sets the file path where the conflict occurred.
        /// This represents the file in the repository where the conflict was detected.
        /// </summary>
        public string ConflictPath { get; set; }

        /// <summary>
        /// Gets or sets the URL to the conflict details.
        /// This URL provides a direct link to the conflict in the Git hosting platform, where users can review or resolve it.
        /// </summary>
        public string Url { get; set; }
    }

}
