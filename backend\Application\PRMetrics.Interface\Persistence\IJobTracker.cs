﻿using RIB.PRMetrics.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.PRMetrics.Application.Interface.Persistence
{
    public interface IJobTracker
    {
        ExportJob Get(Guid uuid);
        void Add(ExportJob job);
        void Pause(Guid uuid, ProcessProgress process=null);
        void Resume(Guid uuid, ProcessProgress process=null);
        void Cancel(Guid uuid, ProcessProgress process=null);
    }

}
