﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto.PullRequestBuild
{
    /// <summary>
    /// Represents a log entry associated with a pull request build.
    /// This DTO contains the log ID and a URL to access the log details.
    /// </summary>
    public class LogDto
    {
        /// <summary>
        /// Gets or sets the unique identifier for the log entry.
        /// This ID can be used to track or reference a specific log in the system.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the URL to access the log details.
        /// This URL links to the location where the log data can be reviewed or inspected.
        /// </summary>
        public string Url { get; set; }
    }
}
