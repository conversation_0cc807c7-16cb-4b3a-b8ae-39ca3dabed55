﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;

namespace  RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetPullRequests
{
    /// <summary>
    /// Validator for GetGitPullRequestQuery to ensure all required fields are present.
    /// </summary>
    public class GetGitPullRequestByIdValidator : AbstractValidator<GetGitPullRequestQuery>
    {
        public GetGitPullRequestByIdValidator()
        {
            // Validate that Project is not empty or null
            RuleFor(x => x.Project)
                .NotEmpty()
                .WithMessage("Project is required.");

            // Validate that Repositories is not empty or null
            RuleFor(x => x.Repositories)
               .NotEmpty()
               .WithMessage("Repositories are required.");

            // You can add more validation rules based on other properties within PullRequestSearchCriteria
            // For example, if you want to validate date ranges, state filters, etc.
        }
    }
}
