{"version": 3, "file": "signalr.min.js", "mappings": "AAAA,IAA2CA,EAAMC,EAAND,EASxCE,KAT8CD,EASxC,I,MCRT,IAAIE,EAAsB,CCA1BA,EAAwB,CAACC,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXF,EAAoBI,EAAEF,EAAYC,KAASH,EAAoBI,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,GCNDH,EAAoBS,EAAI,WACvB,GAA0B,iBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOC,MAAQ,IAAIC,SAAS,cAAb,EAGhB,CAFE,MAAOC,GACR,GAAsB,iBAAXC,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxBd,EAAoBI,EAAI,CAACW,EAAKC,IAAUX,OAAOY,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFhB,EAAoBoB,EAAKnB,IACH,oBAAXoB,QAA0BA,OAAOC,aAC1CjB,OAAOC,eAAeL,EAASoB,OAAOC,YAAa,CAAEC,MAAO,WAE7DlB,OAAOC,eAAeL,EAAS,IAAc,CAAEsB,OAAO,GAAO,E,ICGlDC,E,+VCFL,MAAMC,UAAkBC,MAa3BC,YAAYC,EAAsBC,GAC9B,MAAMC,aAAuBb,UAC7Bc,MAAM,GAAGH,mBAA8BC,MACvClB,KAAKkB,WAAaA,EAIlBlB,KAAKqB,UAAYF,CACrB,EAIG,MAAMG,UAAqBP,MAS9BC,YAAYC,EAAuB,uBAC/B,MAAME,aAAuBb,UAC7Bc,MAAMH,GAINjB,KAAKqB,UAAYF,CACrB,EAIG,MAAMI,UAAmBR,MAS5BC,YAAYC,EAAuB,sBAC/B,MAAME,aAAuBb,UAC7Bc,MAAMH,GAINjB,KAAKqB,UAAYF,CACrB,EAKG,MAAMK,UAAkCT,MAgB3CC,YAAYS,EAAiBC,GACzB,MAAMP,aAAuBb,UAC7Bc,MAAMK,GACNzB,KAAK0B,UAAYA,EACjB1B,KAAK2B,UAAY,4BAIjB3B,KAAKqB,UAAYF,CACrB,EAKG,MAAMS,UAA+Bb,MAgBxCC,YAAYS,EAAiBC,GACzB,MAAMP,aAAuBb,UAC7Bc,MAAMK,GACNzB,KAAK0B,UAAYA,EACjB1B,KAAK2B,UAAY,yBAIjB3B,KAAKqB,UAAYF,CACrB,EAKG,MAAMU,UAAoCd,MAgB7CC,YAAYS,EAAiBC,GACzB,MAAMP,aAAuBb,UAC7Bc,MAAMK,GACNzB,KAAK0B,UAAYA,EACjB1B,KAAK2B,UAAY,8BAIjB3B,KAAKqB,UAAYF,CACrB,EAKG,MAAMW,UAAyCf,MAYlDC,YAAYS,GACR,MAAMN,aAAuBb,UAC7Bc,MAAMK,GACNzB,KAAK2B,UAAY,mCAIjB3B,KAAKqB,UAAYF,CACrB,EAKG,MAAMY,UAAwBhB,MAajCC,YAAYS,EAAiBO,GACzB,MAAMb,aAAuBb,UAC7Bc,MAAMK,GAENzB,KAAKgC,YAAcA,EAInBhC,KAAKqB,UAAYF,CACrB,EC/KG,MAAMc,EAqCTjB,YACoBE,EACAgB,EACAC,GAFA,KAAAjB,WAAAA,EACA,KAAAgB,WAAAA,EACA,KAAAC,QAAAA,CACpB,EAOG,MAAeC,EAeXvC,IAAIwC,EAAaC,GACpB,OAAOtC,KAAKuC,KAAK,IACVD,EACHE,OAAQ,MACRH,OAER,CAgBOI,KAAKJ,EAAaC,GACrB,OAAOtC,KAAKuC,KAAK,IACVD,EACHE,OAAQ,OACRH,OAER,CAgBOK,OAAOL,EAAaC,GACvB,OAAOtC,KAAKuC,KAAK,IACVD,EACHE,OAAQ,SACRH,OAER,CAeOM,gBAAgBN,GACnB,MAAO,EACX,GF5JJ,SAAYxB,GAER,qBAEA,qBAEA,iCAEA,yBAEA,qBAEA,2BAEA,kBACH,CAfD,CAAYA,IAAAA,EAAQ,KGFb,MAAM+B,EAIT,cAAuB,CAIhBC,IAAIC,EAAqBC,GAChC,EAPc,EAAAC,SAAoB,IAAIJ,ECKnC,MAAMK,EAAkB,kBAExB,MAAMC,EACFC,kBAAkBC,EAAUC,GAC/B,GAAID,QACA,MAAM,IAAIrC,MAAM,QAAQsC,2BAEhC,CACOF,kBAAkBC,EAAaC,GAClC,IAAKD,GAAOA,EAAIE,MAAM,SAClB,MAAM,IAAIvC,MAAM,QAAQsC,mCAEhC,CAEOF,YAAYC,EAAUG,EAAaF,GAEtC,KAAMD,KAAOG,GACT,MAAM,IAAIxC,MAAM,WAAWsC,YAAeD,KAElD,EAIG,MAAMI,EAESC,uBACd,OAAQD,EAASE,QAA4B,iBAAXvD,QAAkD,iBAApBA,OAAOwD,QAC3E,CAGkBC,yBACd,OAAQJ,EAASE,QAA0B,iBAATtE,MAAqB,kBAAmBA,IAC9E,CAGWyE,2BACP,OAAQL,EAASE,QAA4B,iBAAXvD,aAAkD,IAApBA,OAAOwD,QAC3E,CAIkBD,oBACd,MAA0B,oBAAZI,SAA2BA,QAAQC,SAAoC,SAAzBD,QAAQC,QAAQV,IAChF,EAIG,SAASW,EAAcC,EAAWC,GACrC,IAAIC,EAAS,GAYb,OAXIC,EAAcH,IACdE,EAAS,yBAAyBF,EAAKI,aACnCH,IACAC,GAAU,eAYf,SAA2BF,GAC9B,MAAMK,EAAO,IAAIC,WAAWN,GAG5B,IAAIO,EAAM,GAOV,OANAF,EAAKG,SAASC,IAEVF,GAAO,KADKE,EAAM,GAAK,IAAM,KACXA,EAAIC,SAAS,MAAM,IAIlCH,EAAII,OAAO,EAAGJ,EAAIK,OAAS,EACtC,CAxBqCC,CAAkBb,QAExB,iBAATA,IACdE,EAAS,yBAAyBF,EAAKY,SACnCX,IACAC,GAAU,eAAeF,OAG1BE,CACX,CAmBO,SAASC,EAAchB,GAC1B,OAAOA,GAA8B,oBAAhB2B,cAChB3B,aAAe2B,aAEX3B,EAAIpC,aAAwC,gBAAzBoC,EAAIpC,YAAYqC,KAChD,CAGO2B,eAAeC,EAAYC,EAAiBC,EAAuBC,EAAwB/C,EAChEF,EAA+BG,GAC7D,MAAM+C,EAAiC,CAAC,GAEjChC,EAAMzC,GAAS0E,IACtBD,EAAQhC,GAAQzC,EAEhBsE,EAAOrC,IAAIhC,EAAS0E,MAAO,IAAIJ,8BAA0CnB,EAAc7B,EAASG,EAAQkD,uBAExG,MAAMC,EAAerB,EAAcjC,GAAW,cAAgB,OACxDuD,QAAiBN,EAAW3C,KAAKJ,EAAK,CACxCF,UACAkD,QAAS,IAAKA,KAAY/C,EAAQ+C,SAClCI,eACAE,QAASrD,EAAQqD,QACjBC,gBAAiBtD,EAAQsD,kBAG7BV,EAAOrC,IAAIhC,EAAS0E,MAAO,IAAIJ,mDAA+DO,EAASxE,cAC3G,CAoBO,MAAM2E,EAIT7E,YAAY8E,EAAqBC,GAC7B/F,KAAKgG,EAAWF,EAChB9F,KAAKiG,EAAYF,CACrB,CAEOG,UACH,MAAMC,EAAgBnG,KAAKgG,EAASI,UAAUC,QAAQrG,KAAKiG,GACvDE,GAAS,GACTnG,KAAKgG,EAASI,UAAUE,OAAOH,EAAO,GAGH,IAAnCnG,KAAKgG,EAASI,UAAUvB,QAAgB7E,KAAKgG,EAASO,gBACtDvG,KAAKgG,EAASO,iBAAiBC,OAAOC,IAAD,GAE7C,EAIG,MAAMC,EAWT1F,YAAY2F,GACR3G,KAAK4G,EAAYD,EACjB3G,KAAK6G,IAAMC,OACf,CAEOjE,IAAIkE,EAAoBtF,GAC3B,GAAIsF,GAAY/G,KAAK4G,EAAW,CAC5B,MAAMI,EAAM,KAAI,IAAIC,MAAOC,kBAAkBrG,EAASkG,OAActF,IACpE,OAAQsF,GACJ,KAAKlG,EAASsG,SACd,KAAKtG,EAASE,MACVf,KAAK6G,IAAIO,MAAMJ,GACf,MACJ,KAAKnG,EAASwG,QACVrH,KAAK6G,IAAIS,KAAKN,GACd,MACJ,KAAKnG,EAAS0G,YACVvH,KAAK6G,IAAIW,KAAKR,GACd,MACJ,QAEIhH,KAAK6G,IAAIhE,IAAImE,G,CAI7B,EAIG,SAAS1B,IACZ,IAAImC,EAAsB,uBAI1B,OAHIjE,EAASE,SACT+D,EAAsB,cAEnB,CAAEA,EAAqBC,EAAmBzE,EAAS0E,IAyDtDnE,EAASE,OACF,SAEA,UA5D0EkE,KACzF,CAGO,SAASF,EAAmBG,EAAiBC,EAAYC,EAAiBC,GAE7E,IAAIC,EAAoB,qBAExB,MAAMC,EAAgBL,EAAQM,MAAM,KAmBpC,OAlBAF,GAAa,GAAGC,EAAc,MAAMA,EAAc,KAClDD,GAAa,KAAKJ,MAGdI,GADAH,GAAa,KAAPA,EACO,GAAGA,MAEH,eAGjBG,GAAa,GAAGF,IAGZE,GADAD,EACa,KAAKA,IAEL,4BAGjBC,GAAa,IACNA,CACX,CAGc,SAASN,IACnB,IAAInE,EAASE,OAYT,MAAO,GAXP,OAAQI,QAAQsE,UACZ,IAAK,QACD,MAAO,aACX,IAAK,SACD,MAAO,QACX,IAAK,QACD,MAAO,QACX,QACI,OAAOtE,QAAQsE,SAK/B,CAGc,SAASR,IACnB,GAAIpE,EAASE,OACT,OAAOI,QAAQuE,SAASC,IAGhC,CAWO,SAASC,EAAerI,GAC3B,OAAIA,EAAEsI,MACKtI,EAAEsI,MACFtI,EAAEuB,QACFvB,EAAEuB,QAEN,GAAGvB,GACd,CC5QO,MAAMuI,UAAwBrG,EAOjC,YAAmB8C,GAMf,GALA9D,QACApB,KAAK0I,EAAUxD,EAIM,oBAAVyD,OAAyBnF,EAASE,OAAQ,CAGjD,MAAMkF,EAA0D,QAGhE5I,KAAK6I,EAAO,IAAKD,EAAY,gBAAiBE,WAEzB,oBAAVH,MACP3I,KAAK+I,EAAaH,EAAY,cAG9B5I,KAAK+I,EAAaJ,MAKtB3I,KAAK+I,EAAaH,EAAY,eAAZA,CAA4B5I,KAAK+I,EAAY/I,KAAK6I,E,MAEpE7I,KAAK+I,EAAaJ,MAAMK,KD+O7B,WAEH,GAA0B,oBAAfjJ,WACP,OAAOA,WAEX,GAAoB,oBAATX,KACP,OAAOA,KAEX,GAAsB,oBAAXe,OACP,OAAOA,OAEX,QAAsB,IAAX,EAAAL,EACP,OAAO,EAAAA,EAEX,MAAM,IAAIiB,MAAM,wBACpB,CC9PyCkI,IAEjC,GAA+B,oBAApBC,gBAAiC,CAGxC,MAAMN,EAA0D,QAGhE5I,KAAKmJ,EAAuBP,EAAY,mB,MAExC5I,KAAKmJ,EAAuBD,eAEpC,CAGOlE,WAAWoE,GAEd,GAAIA,EAAQC,aAAeD,EAAQC,YAAYC,QAC3C,MAAM,IAAI/H,EAGd,IAAK6H,EAAQ5G,OACT,MAAM,IAAIzB,MAAM,sBAEpB,IAAKqI,EAAQ/G,IACT,MAAM,IAAItB,MAAM,mBAGpB,MAAMwI,EAAkB,IAAIvJ,KAAKmJ,EAEjC,IAAI/B,EAEAgC,EAAQC,cACRD,EAAQC,YAAYG,QAAU,KAC1BD,EAAgBE,QAChBrC,EAAQ,IAAI7F,CAAY,GAMhC,IAuBImE,EAvBAgE,EAAiB,KACrB,GAAIN,EAAQzD,QAAS,CACjB,MAAMgE,EAAYP,EAAQzD,QAC1B+D,EAAYE,YAAW,KACnBL,EAAgBE,QAChBzJ,KAAK0I,EAAQ7F,IAAIhC,EAASwG,QAAS,8BACnCD,EAAQ,IAAI9F,CAAc,GAC3BqI,E,CAGiB,KAApBP,EAAQjH,UACRiH,EAAQjH,aAAU0H,GAElBT,EAAQjH,UAERiH,EAAQ/D,QAAU+D,EAAQ/D,SAAW,CAAC,EAClCjB,EAAcgF,EAAQjH,SACtBiH,EAAQ/D,QAAQ,gBAAkB,2BAElC+D,EAAQ/D,QAAQ,gBAAkB,4BAK1C,IACIK,QAAiB1F,KAAK+I,EAAWK,EAAQ/G,IAAM,CAC3CyH,KAAMV,EAAQjH,QACd4H,MAAO,WACPC,aAAyC,IAA5BZ,EAAQxD,gBAA2B,UAAY,cAC5DP,QAAS,CACL,mBAAoB,oBACjB+D,EAAQ/D,SAEf7C,OAAQ4G,EAAQ5G,OAChByH,KAAM,OACNC,SAAU,SACVC,OAAQZ,EAAgBY,Q,CAE9B,MAAOjK,GACL,GAAIkH,EACA,MAAMA,EAMV,MAJApH,KAAK0I,EAAQ7F,IACThC,EAASwG,QACT,4BAA4BnH,MAE1BA,C,SAEFwJ,GACAU,aAAaV,GAEbN,EAAQC,cACRD,EAAQC,YAAYG,QAAU,K,CAItC,IAAK9D,EAAS2E,GAAI,CACd,MAAMpJ,QAAqBqJ,EAAmB5E,EAAU,QACxD,MAAM,IAAI5E,EAAUG,GAAgByE,EAASxD,WAAYwD,EAAS6E,O,CAGtE,MAAMpI,EAAUmI,EAAmB5E,EAAU0D,EAAQ3D,cAC/C+E,QAAgBrI,EAEtB,OAAO,IAAIF,EACPyD,EAAS6E,OACT7E,EAASxD,WACTsI,EAER,CAEO7H,gBAAgBN,GACnB,IAAIoI,EAAkB,GAKtB,OAJIjH,EAASE,QAAU1D,KAAK6I,GAExB7I,KAAK6I,EAAK6B,WAAWrI,GAAK,CAACnC,EAAGyK,IAAMF,EAAUE,EAAEC,KAAK,QAElDH,CACX,EAGJ,SAASH,EAAmB5E,EAAoBD,GAC5C,IAAItD,EACJ,OAAQsD,GACJ,IAAK,cACDtD,EAAUuD,EAASmF,cACnB,MACJ,IAAK,OAOL,QACI1I,EAAUuD,EAASoF,OACnB,MANJ,IAAK,OACL,IAAK,WACL,IAAK,OACD,MAAM,IAAI/J,MAAM,GAAG0E,uBAM3B,OAAOtD,CACX,CChLO,MAAM4I,UAAsB3I,EAG/B,YAAmB8C,GACf9D,QACApB,KAAK0I,EAAUxD,CACnB,CAGO3C,KAAK6G,GAER,OAAIA,EAAQC,aAAeD,EAAQC,YAAYC,QACpC0B,QAAQC,OAAO,IAAI1J,GAGzB6H,EAAQ5G,OAGR4G,EAAQ/G,IAIN,IAAI2I,SAAsB,CAACE,EAASD,KACvC,MAAME,EAAM,IAAIC,eAEhBD,EAAIE,KAAKjC,EAAQ5G,OAAS4G,EAAQ/G,KAAM,GACxC8I,EAAIvF,qBAA8CiE,IAA5BT,EAAQxD,iBAAuCwD,EAAQxD,gBAC7EuF,EAAIG,iBAAiB,mBAAoB,kBACjB,KAApBlC,EAAQjH,UACRiH,EAAQjH,aAAU0H,GAElBT,EAAQjH,UAEJiC,EAAcgF,EAAQjH,SACtBgJ,EAAIG,iBAAiB,eAAgB,4BAErCH,EAAIG,iBAAiB,eAAgB,6BAI7C,MAAMjG,EAAU+D,EAAQ/D,QACpBA,GACA3F,OAAO6L,KAAKlG,GACPZ,SAAS+G,IACNL,EAAIG,iBAAiBE,EAAQnG,EAAQmG,GAAQ,IAIrDpC,EAAQ3D,eACR0F,EAAI1F,aAAe2D,EAAQ3D,cAG3B2D,EAAQC,cACRD,EAAQC,YAAYG,QAAU,KAC1B2B,EAAI1B,QACJwB,EAAO,IAAI1J,EAAa,GAI5B6H,EAAQzD,UACRwF,EAAIxF,QAAUyD,EAAQzD,SAG1BwF,EAAIM,OAAS,KACLrC,EAAQC,cACRD,EAAQC,YAAYG,QAAU,MAG9B2B,EAAIZ,QAAU,KAAOY,EAAIZ,OAAS,IAClCW,EAAQ,IAAIjJ,EAAakJ,EAAIZ,OAAQY,EAAIjJ,WAAYiJ,EAAIzF,UAAYyF,EAAIO,eAEzET,EAAO,IAAInK,EAAUqK,EAAIzF,UAAYyF,EAAIO,cAAgBP,EAAIjJ,WAAYiJ,EAAIZ,Q,EAIrFY,EAAIQ,QAAU,KACV3L,KAAK0I,EAAQ7F,IAAIhC,EAASwG,QAAS,4BAA4B8D,EAAIZ,WAAWY,EAAIjJ,eAClF+I,EAAO,IAAInK,EAAUqK,EAAIjJ,WAAYiJ,EAAIZ,QAAQ,EAGrDY,EAAIS,UAAY,KACZ5L,KAAK0I,EAAQ7F,IAAIhC,EAASwG,QAAS,8BACnC4D,EAAO,IAAI3J,EAAe,EAG9B6J,EAAI5I,KAAK6G,EAAQjH,QAAQ,IAlElB6I,QAAQC,OAAO,IAAIlK,MAAM,oBAHzBiK,QAAQC,OAAO,IAAIlK,MAAM,sBAuExC,ECpFG,MAAM8K,UAA0BzJ,EAInC,YAAmB8C,GAGf,GAFA9D,QAEqB,oBAAVuH,OAAyBnF,EAASE,OACzC1D,KAAK8L,EAAc,IAAIrD,EAAgBvD,OACpC,IAA8B,oBAAnBkG,eAGd,MAAM,IAAIrK,MAAM,+BAFhBf,KAAK8L,EAAc,IAAIf,EAAc7F,E,CAI7C,CAGO3C,KAAK6G,GAER,OAAIA,EAAQC,aAAeD,EAAQC,YAAYC,QACpC0B,QAAQC,OAAO,IAAI1J,GAGzB6H,EAAQ5G,OAGR4G,EAAQ/G,IAINrC,KAAK8L,EAAYvJ,KAAK6G,GAHlB4B,QAAQC,OAAO,IAAIlK,MAAM,oBAHzBiK,QAAQC,OAAO,IAAIlK,MAAM,sBAOxC,CAEO4B,gBAAgBN,GACnB,OAAOrC,KAAK8L,EAAYnJ,gBAAgBN,EAC5C,ECzCG,MAAM0J,EAIF5I,aAAa6I,GAChB,MAAO,GAAGA,IAASD,EAAkBE,iBACzC,CAEO9I,aAAa+I,GAChB,GAAIA,EAAMA,EAAMrH,OAAS,KAAOkH,EAAkBE,gBAC9C,MAAM,IAAIlL,MAAM,0BAGpB,MAAMoL,EAAWD,EAAM/D,MAAM4D,EAAkBE,iBAE/C,OADAE,EAASC,MACFD,CACX,EAfc,EAAAE,oBAAsB,GACtB,EAAAJ,gBAAkBK,OAAOC,aAAaR,EAAkBM,qBCYnE,MAAMG,EAEFC,sBAAsBC,GACzB,OAAOX,EAAkBY,MAAMC,KAAKC,UAAUH,GAClD,CAEOI,uBAAuB7I,GAC1B,IAAI8I,EACAC,EAEJ,GAAI5I,EAAcH,GAAO,CAErB,MAAMgJ,EAAa,IAAI1I,WAAWN,GAC5BiJ,EAAiBD,EAAW5G,QAAQ0F,EAAkBM,qBAC5D,IAAwB,IAApBa,EACA,MAAM,IAAInM,MAAM,0BAKpB,MAAMoM,EAAiBD,EAAiB,EACxCH,EAAcT,OAAOC,aAAaa,MAAM,KAAMC,MAAM/M,UAAUgN,MAAM9M,KAAKyM,EAAWK,MAAM,EAAGH,KAC7FH,EAAiBC,EAAW5I,WAAa8I,EAAkBF,EAAWK,MAAMH,GAAgBI,OAAS,I,KAClG,CACH,MAAMC,EAAmBvJ,EACnBiJ,EAAiBM,EAASnH,QAAQ0F,EAAkBE,iBAC1D,IAAwB,IAApBiB,EACA,MAAM,IAAInM,MAAM,0BAKpB,MAAMoM,EAAiBD,EAAiB,EACxCH,EAAcS,EAASC,UAAU,EAAGN,GACpCH,EAAiBQ,EAAS3I,OAASsI,EAAkBK,EAASC,UAAUN,GAAkB,I,CAI9F,MAAMhB,EAAWJ,EAAkB2B,MAAMX,GACnCrH,EAAWkH,KAAKc,MAAMvB,EAAS,IACrC,GAAIzG,EAASiI,KACT,MAAM,IAAI5M,MAAM,kDAMpB,MAAO,CAACiM,EAJ0CtH,EAKtD,EC5DJ,IAAYkI,ECYAC,GDZZ,SAAYD,GAER,+BAEA,+BAEA,+BAEA,2CAEA,2CAEA,mBAEA,qBACA,iBACA,0BACH,CAjBD,CAAYA,IAAAA,EAAW,KEAhB,MAAME,EAOT9M,cACIhB,KAAKoG,UAAY,EACrB,CAEO2H,KAAKC,GACR,IAAK,MAAMjI,KAAY/F,KAAKoG,UACxBL,EAASgI,KAAKC,EAEtB,CAEO5G,MAAM6G,GACT,IAAK,MAAMlI,KAAY/F,KAAKoG,UACpBL,EAASqB,OACTrB,EAASqB,MAAM6G,EAG3B,CAEOC,WACH,IAAK,MAAMnI,KAAY/F,KAAKoG,UACpBL,EAASmI,UACTnI,EAASmI,UAGrB,CAEOC,UAAUpI,GAEb,OADA/F,KAAKoG,UAAUgI,KAAKrI,GACb,IAAIF,EAAoB7F,KAAM+F,EACzC,ECnCG,MAAMsI,EAkBTrN,YAAYsN,EAAwBC,EAAyBC,GAd5C,KAAAC,EAAsB,IAE/B,KAAAC,EAA4B,GAC5B,KAAAC,EAA6B,EAC7B,KAAAC,GAAmC,EAGnC,KAAAC,EAA2B,EAC3B,KAAAC,EAA4B,EAC5B,KAAAC,EAA6B,EAC7B,KAAAC,GAAgC,EAKpChP,KAAKiP,EAAYX,EACjBtO,KAAKkP,EAAcX,EACnBvO,KAAKyO,EAAcD,CACvB,CAEOxJ,QAAYvD,GACf,MAAM0N,EAAoBnP,KAAKiP,EAAUG,aAAa3N,GAEtD,IAAI4N,EAAqCrE,QAAQE,UAGjD,GAAIlL,KAAKsP,EAAqB7N,GAAU,CACpCzB,KAAK2O,IACL,IAAIY,EAAqD,OACrDC,EAAsD,OAEtDpL,EAAc+K,GACdnP,KAAK+O,GAAsBI,EAAkB9K,WAE7CrE,KAAK+O,GAAsBI,EAAkBtK,OAG7C7E,KAAK+O,GAAsB/O,KAAKyO,IAChCY,EAAsB,IAAIrE,SAAQ,CAACE,EAASD,KACxCsE,EAA8BrE,EAC9BsE,EAA8BvE,CAAM,KAI5CjL,KAAK0O,EAAUN,KAAK,IAAIqB,EAAaN,EAAmBnP,KAAK2O,EACzDY,EAA6BC,G,CAGrC,IAKSxP,KAAKgP,SACAhP,KAAKkP,EAAY3M,KAAK4M,E,CAElC,MACEnP,KAAK0P,G,OAEHL,CACV,CAEOM,EAAKC,GACR,IAAIC,GAAsB,EAG1B,IAAK,IAAI1J,EAAQ,EAAGA,EAAQnG,KAAK0O,EAAU7J,OAAQsB,IAAS,CACxD,MAAM2J,EAAU9P,KAAK0O,EAAUvI,GAC/B,GAAI2J,EAAQC,GAAOH,EAAWI,WAC1BH,EAAqB1J,EACjB/B,EAAc0L,EAAQ/M,GACtB/C,KAAK+O,GAAsBe,EAAQ/M,EAASsB,WAE5CrE,KAAK+O,GAAsBe,EAAQ/M,EAAS8B,OAGhDiL,EAAQG,QACL,MAAIjQ,KAAK+O,EAAqB/O,KAAKyO,GAItC,MAFAqB,EAAQG,G,GAMY,IAAxBJ,IAEA7P,KAAK0O,EAAY1O,KAAK0O,EAAUpB,MAAMuC,EAAqB,GAEnE,CAEOK,EAAsBzO,GACzB,GAAIzB,KAAK4O,EACL,OAAInN,EAAQkM,OAASC,EAAYuC,WAG7BnQ,KAAK4O,GAA0B,GACxB,GAKf,IAAK5O,KAAKsP,EAAqB7N,GAC3B,OAAO,EAGX,MAAM2O,EAAYpQ,KAAK6O,EAEvB,OADA7O,KAAK6O,IACDuB,GAAapQ,KAAK8O,GACdsB,IAAcpQ,KAAK8O,GAGnB9O,KAAKqQ,KAGF,IAGXrQ,KAAK8O,EAA4BsB,EAIjCpQ,KAAKqQ,KACE,EACX,CAEOC,EAAe7O,GACdA,EAAQuO,WAAahQ,KAAK6O,EAE1B7O,KAAKkP,EAAYqB,KAAK,IAAIxP,MAAM,gEAIpCf,KAAK6O,EAA2BpN,EAAQuO,UAC5C,CAEON,IACH1P,KAAKgP,GAAuB,EAC5BhP,KAAK4O,GAA0B,CACnC,CAEO5J,UACH,MAAMgL,EAAuC,IAA1BhQ,KAAK0O,EAAU7J,OAC5B7E,KAAK0O,EAAU,GAAGqB,EACjB/P,KAAK2O,EAAqB,QAC3B3O,KAAKkP,EAAY3M,KAAKvC,KAAKiP,EAAUG,aAAa,CAAEzB,KAAMC,EAAYuC,SAAUH,gBAItF,MAAM7D,EAAWnM,KAAK0O,EACtB,IAAK,MAAMoB,KAAW3D,QACZnM,KAAKkP,EAAY3M,KAAKuN,EAAQ/M,GAGxC/C,KAAKgP,GAAuB,CAChC,CAEOwB,EAASpJ,GACZA,UAAAA,EAAU,IAAIrG,MAAM,mCAGpB,IAAK,MAAM+O,KAAW9P,KAAK0O,EACvBoB,EAAQW,EAAUrJ,EAE1B,CAEQkI,EAAqB7N,GAMzB,OAAQA,EAAQkM,MACZ,KAAKC,EAAY8C,WACjB,KAAK9C,EAAY+C,WACjB,KAAK/C,EAAYgD,WACjB,KAAKhD,EAAYiD,iBACjB,KAAKjD,EAAYkD,iBACb,OAAO,EACX,KAAKlD,EAAYmD,MACjB,KAAKnD,EAAYuC,SACjB,KAAKvC,EAAYoD,KACjB,KAAKpD,EAAYqD,IACb,OAAO,EAEnB,CAEQZ,SACyBxG,IAAzB7J,KAAKkR,IACLlR,KAAKkR,EAAkBtH,YAAW5E,UAC9B,IACShF,KAAKgP,SACAhP,KAAKkP,EAAY3M,KAAKvC,KAAKiP,EAAUG,aAAa,CAAEzB,KAAMC,EAAYqD,IAAKjB,WAAYhQ,KAAK8O,IAGhG,CAAR,MAAQ,CAEV1E,aAAapK,KAAKkR,GAClBlR,KAAKkR,OAAkBrH,CAAS,GAEjC,KAEX,EAGJ,MAAM4F,EACFzO,YAAYS,EAA+B0P,EAAYC,EAAiCC,GACpFrR,KAAK+C,EAAWtB,EAChBzB,KAAK+P,EAAMoB,EACXnR,KAAKiQ,EAAYmB,EACjBpR,KAAKyQ,EAAYY,CACrB,GF5MJ,SAAYxD,GAER,8BAEA,0BAEA,wBAEA,gCAEA,6BACH,CAXD,CAAYA,IAAAA,EAAkB,KAcvB,MAAMyD,EAiEFnO,cACHoL,EACArJ,EACAoJ,EACAiD,EACAC,EACAC,EACAC,GACA,OAAO,IAAIJ,EAAc/C,EAAYrJ,EAAQoJ,EAAUiD,EACnDC,EAA6BC,EAAiCC,EACtE,CAEA,YACInD,EACArJ,EACAoJ,EACAiD,EACAC,EACAC,EACAC,GAtDI,KAAAC,EAAyB,EASzB,KAAAC,EAAuB,KAE3B5R,KAAK0I,EAAQ7F,IAAIhC,EAASwG,QAAS,wNAAwN,EA4C3PnE,EAAI2O,WAAWtD,EAAY,cAC3BrL,EAAI2O,WAAW3M,EAAQ,UACvBhC,EAAI2O,WAAWvD,EAAU,YAEzBtO,KAAKwR,4BAA8BA,QAAAA,EA5GL,IA6G9BxR,KAAKyR,gCAAkCA,QAAAA,EA5GH,KA8GpCzR,KAAK8R,EAA+BJ,QAAAA,EA7GG,IA+GvC1R,KAAK0I,EAAUxD,EACflF,KAAKiP,EAAYX,EACjBtO,KAAKuO,WAAaA,EAClBvO,KAAK+R,EAAmBR,EACxBvR,KAAKgS,GAAqB,IAAIxF,EAE9BxM,KAAKuO,WAAW0D,UAAahO,GAAcjE,KAAKkS,GAAqBjO,GACrEjE,KAAKuO,WAAW4D,QAAW/K,GAAkBpH,KAAKoS,GAAkBhL,GAEpEpH,KAAKqS,GAAa,CAAC,EACnBrS,KAAKsS,GAAW,CAAC,EACjBtS,KAAKuS,GAAmB,GACxBvS,KAAKwS,GAAyB,GAC9BxS,KAAKyS,GAAwB,GAC7BzS,KAAK0S,GAAgB,EACrB1S,KAAK2S,IAA6B,EAClC3S,KAAK4S,GAAmB/E,EAAmBgF,aAC3C7S,KAAK8S,IAAqB,EAE1B9S,KAAK+S,GAAqB/S,KAAKiP,EAAUG,aAAa,CAAEzB,KAAMC,EAAYoD,MAC9E,CAGIgC,YACA,OAAOhT,KAAK4S,EAChB,CAKIK,mBACA,OAAOjT,KAAKuO,YAAcvO,KAAKuO,WAAW0E,cAAwB,IACtE,CAGIC,cACA,OAAOlT,KAAKuO,WAAW2E,SAAW,EACtC,CAOIA,YAAQ7Q,GACR,GAAIrC,KAAK4S,KAAqB/E,EAAmBgF,cAAgB7S,KAAK4S,KAAqB/E,EAAmBsF,aAC1G,MAAM,IAAIpS,MAAM,0FAGpB,IAAKsB,EACD,MAAM,IAAItB,MAAM,8CAGpBf,KAAKuO,WAAW2E,QAAU7Q,CAC9B,CAMO+Q,QAEH,OADApT,KAAKqT,GAAgBrT,KAAKsT,KACnBtT,KAAKqT,EAChB,CAEQrO,WACJ,GAAIhF,KAAK4S,KAAqB/E,EAAmBgF,aAC7C,OAAO7H,QAAQC,OAAO,IAAIlK,MAAM,0EAGpCf,KAAK4S,GAAmB/E,EAAmB0F,WAC3CvT,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,2BAEjC,UACUxT,KAAKyT,KAEPjQ,EAASC,WAETtD,OAAOwD,SAAS+P,iBAAiB,SAAU1T,KAAK4R,GAGpD5R,KAAK4S,GAAmB/E,EAAmB8F,UAC3C3T,KAAK8S,IAAqB,EAC1B9S,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,wC,CACnC,MAAOtT,GAGL,OAFAF,KAAK4S,GAAmB/E,EAAmBgF,aAC3C7S,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,gEAAgEtT,OAC1F8K,QAAQC,OAAO/K,E,CAE9B,CAEQ8E,WACJhF,KAAK4T,QAAwB/J,EAC7B7J,KAAK2S,IAA6B,EAElC,MAAMkB,EAAmB,IAAI7I,SAAQ,CAACE,EAASD,KAC3CjL,KAAK8T,GAAqB5I,EAC1BlL,KAAK+T,GAAqB9I,CAAM,UAG9BjL,KAAKuO,WAAW6E,MAAMpT,KAAKiP,EAAU+E,gBAE3C,IACI,IAAInM,EAAU7H,KAAKiP,EAAUpH,QACxB7H,KAAKuO,WAAW0F,SAASC,YAG1BrM,EAAU,GAGd,MAAM6E,EAA4C,CAC9C4B,SAAUtO,KAAKiP,EAAU5L,KACzBwE,WAmBJ,GAhBA7H,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,oCAE3BxT,KAAKmU,GAAanU,KAAKgS,GAAmBvF,sBAAsBC,IAEtE1M,KAAK0I,EAAQ7F,IAAIhC,EAAS0G,YAAa,sBAAsBvH,KAAKiP,EAAU5L,UAG5ErD,KAAKoU,KACLpU,KAAKqU,KACLrU,KAAKsU,WAECT,EAKF7T,KAAK4T,GAKL,MAAM5T,KAAK4T,KAGc5T,KAAKuO,WAAW0F,SAASC,YAElDlU,KAAKuU,GAAiB,IAAIlG,EAAcrO,KAAKiP,EAAWjP,KAAKuO,WAAYvO,KAAK8R,GAC9E9R,KAAKuO,WAAW0F,SAASO,aAAexU,KAAKuU,GAAe7E,EAAc1G,KAAKhJ,KAAKuU,IACpFvU,KAAKuO,WAAW0F,SAASQ,OAAS,KAC9B,GAAIzU,KAAKuU,GACL,OAAOvU,KAAKuU,GAAeG,G,GAKlC1U,KAAKuO,WAAW0F,SAASU,yBACpB3U,KAAKmU,GAAanU,KAAK+S,G,CAEnC,MAAO7S,GASL,MARAF,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,oCAAoCtT,8CAErEF,KAAKoU,KACLpU,KAAK4U,WAIC5U,KAAKuO,WAAWgC,KAAKrQ,GACrBA,C,CAEd,CAMO8E,aAEH,MAAM6P,EAAe7U,KAAKqT,GAC1BrT,KAAKuO,WAAW0F,SAASC,WAAY,EAErClU,KAAK8U,GAAe9U,KAAK+U,WACnB/U,KAAK8U,GAEX,UAEUD,C,CACR,MAAO3U,G,CAGb,CAEQ6U,GAAc3N,GAClB,GAAIpH,KAAK4S,KAAqB/E,EAAmBgF,aAE7C,OADA7S,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,8BAA8BpM,+DACxD4D,QAAQE,UAGnB,GAAIlL,KAAK4S,KAAqB/E,EAAmBmH,cAE7C,OADAhV,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,+BAA+BpM,4EACzDpH,KAAK8U,GAGhB,MAAM9B,EAAQhT,KAAK4S,GAKnB,OAJA5S,KAAK4S,GAAmB/E,EAAmBmH,cAE3ChV,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,2BAE7BxT,KAAKiV,IAILjV,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,iEAEjCpJ,aAAapK,KAAKiV,IAClBjV,KAAKiV,QAAwBpL,EAE7B7J,KAAKkV,KACElK,QAAQE,YAGf8H,IAAUnF,EAAmB8F,WAE7B3T,KAAKmV,KAGTnV,KAAKoU,KACLpU,KAAK4U,KACL5U,KAAK4T,GAAwBxM,GAAS,IAAI7F,EAAW,uEAK9CvB,KAAKuO,WAAWgC,KAAKnJ,GAChC,CAEQpC,WACJ,UACUhF,KAAKoV,GAAkBpV,KAAKqV,K,CACpC,M,CAGN,CASOC,OAAgBC,KAAuBC,GAC1C,MAAOC,EAASC,GAAa1V,KAAK2V,GAAwBH,GACpDI,EAAuB5V,KAAK6V,GAAwBN,EAAYC,EAAME,GAG5E,IAAII,EAEJ,MAAMhQ,EAAU,IAAIgI,EAqCpB,OApCAhI,EAAQS,eAAiB,KACrB,MAAMwP,EAA4C/V,KAAKgW,GAAwBJ,EAAqBK,cAIpG,cAFOjW,KAAKqS,GAAWuD,EAAqBK,cAErCH,EAAaI,MAAK,IACdlW,KAAKoV,GAAkBW,IAChC,EAGN/V,KAAKqS,GAAWuD,EAAqBK,cAAgB,CAACE,EAA+D/O,KAC7GA,EACAtB,EAAQsB,MAAMA,GAEP+O,IAEHA,EAAgBxI,OAASC,EAAYgD,WACjCuF,EAAgB/O,MAChBtB,EAAQsB,MAAM,IAAIrG,MAAMoV,EAAgB/O,QAExCtB,EAAQoI,WAGZpI,EAAQiI,KAAMoI,EAAoB,M,EAK9CL,EAAe9V,KAAKoV,GAAkBQ,GACjCpP,OAAOtG,IACJ4F,EAAQsB,MAAMlH,UACPF,KAAKqS,GAAWuD,EAAqBK,aAAa,IAGjEjW,KAAKoW,GAAeX,EAASK,GAEtBhQ,CACX,CAEQqO,GAAa1S,GAEjB,OADAzB,KAAKsU,KACEtU,KAAKuO,WAAWhM,KAAKd,EAChC,CAMQ2T,GAAkB3T,GACtB,OAAIzB,KAAKuU,GACEvU,KAAKuU,GAAe8B,EAAM5U,GAE1BzB,KAAKmU,GAAanU,KAAKiP,EAAUG,aAAa3N,GAE7D,CAWOc,KAAKgT,KAAuBC,GAC/B,MAAOC,EAASC,GAAa1V,KAAK2V,GAAwBH,GACpDc,EAActW,KAAKoV,GAAkBpV,KAAKuW,GAAkBhB,EAAYC,GAAM,EAAME,IAI1F,OAFA1V,KAAKoW,GAAeX,EAASa,GAEtBA,CACX,CAaOE,OAAgBjB,KAAuBC,GAC1C,MAAOC,EAASC,GAAa1V,KAAK2V,GAAwBH,GACpDI,EAAuB5V,KAAKuW,GAAkBhB,EAAYC,GAAM,EAAOE,GAgC7E,OA9BU,IAAI1K,SAAa,CAACE,EAASD,KAEjCjL,KAAKqS,GAAWuD,EAAqBK,cAAiB,CAACE,EAA+D/O,KAC9GA,EACA6D,EAAO7D,GAEA+O,IAEHA,EAAgBxI,OAASC,EAAYgD,WACjCuF,EAAgB/O,MAChB6D,EAAO,IAAIlK,MAAMoV,EAAgB/O,QAEjC8D,EAAQiL,EAAgBM,QAG5BxL,EAAO,IAAIlK,MAAM,4BAA4BoV,EAAgBxI,S,EAKzE,MAAMmI,EAAe9V,KAAKoV,GAAkBQ,GACvCpP,OAAOtG,IACJ+K,EAAO/K,UAEAF,KAAKqS,GAAWuD,EAAqBK,aAAc,IAGlEjW,KAAKoW,GAAeX,EAASK,EAAa,GAIlD,CAQOY,GAAGnB,EAAoBoB,GACrBpB,GAAeoB,IAIpBpB,EAAaA,EAAWqB,cACnB5W,KAAKsS,GAASiD,KACfvV,KAAKsS,GAASiD,GAAc,KAIsB,IAAlDvV,KAAKsS,GAASiD,GAAYlP,QAAQsQ,IAItC3W,KAAKsS,GAASiD,GAAYnH,KAAKuI,GACnC,CAiBOE,IAAItB,EAAoB/S,GAC3B,IAAK+S,EACD,OAGJA,EAAaA,EAAWqB,cACxB,MAAME,EAAW9W,KAAKsS,GAASiD,GAC/B,GAAKuB,EAGL,GAAItU,EAAQ,CACR,MAAMuU,EAAYD,EAASzQ,QAAQ7D,IAChB,IAAfuU,IACAD,EAASxQ,OAAOyQ,EAAW,GACH,IAApBD,EAASjS,eACF7E,KAAKsS,GAASiD,G,aAItBvV,KAAKsS,GAASiD,EAG7B,CAMOpD,QAAQ6E,GACPA,GACAhX,KAAKuS,GAAiBnE,KAAK4I,EAEnC,CAMOC,eAAeD,GACdA,GACAhX,KAAKwS,GAAuBpE,KAAK4I,EAEzC,CAMOE,cAAcF,GACbA,GACAhX,KAAKyS,GAAsBrE,KAAK4I,EAExC,CAEQ9E,GAAqBjO,GASzB,GARAjE,KAAKoU,KAEApU,KAAK2S,KACN1O,EAAOjE,KAAKmX,GAA0BlT,GACtCjE,KAAK2S,IAA6B,GAIlC1O,EAAM,CAEN,MAAMkI,EAAWnM,KAAKiP,EAAUmI,cAAcnT,EAAMjE,KAAK0I,GAEzD,IAAK,MAAMjH,KAAW0K,EAClB,IAAInM,KAAKuU,IAAmBvU,KAAKuU,GAAerE,EAAsBzO,GAKtE,OAAQA,EAAQkM,MACZ,KAAKC,EAAY8C,WACb1Q,KAAKqX,GAAoB5V,GACpB+E,OAAOtG,IACJF,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,qCAAqCwH,EAAerI,KAAK,IAElG,MACJ,KAAK0N,EAAY+C,WACjB,KAAK/C,EAAYgD,WAAY,CACzB,MAAMoG,EAAWhX,KAAKqS,GAAW5Q,EAAQwU,cACzC,GAAIe,EAAU,CACNvV,EAAQkM,OAASC,EAAYgD,mBACtB5Q,KAAKqS,GAAW5Q,EAAQwU,cAEnC,IACIe,EAASvV,E,CACX,MAAOvB,GACLF,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,gCAAgCwH,EAAerI,K,EAGxF,K,CAEJ,KAAK0N,EAAYoD,KAEb,MACJ,KAAKpD,EAAYmD,MAAO,CACpB/Q,KAAK0I,EAAQ7F,IAAIhC,EAAS0G,YAAa,uCAEvC,MAAMH,EAAQ3F,EAAQ2F,MAAQ,IAAIrG,MAAM,sCAAwCU,EAAQ2F,YAASyC,GAElE,IAA3BpI,EAAQ6V,eAKRtX,KAAKuO,WAAWgC,KAAKnJ,GAGrBpH,KAAK8U,GAAe9U,KAAK+U,GAAc3N,GAG3C,K,CAEJ,KAAKwG,EAAYqD,IACTjR,KAAKuU,IACLvU,KAAKuU,GAAe5E,EAAKlO,GAE7B,MACJ,KAAKmM,EAAYuC,SACTnQ,KAAKuU,IACLvU,KAAKuU,GAAejE,EAAe7O,GAEvC,MACJ,QACIzB,KAAK0I,EAAQ7F,IAAIhC,EAASwG,QAAS,yBAAyB5F,EAAQkM,S,CAMpF3N,KAAKqU,IACT,CAEQ8C,GAA0BlT,GAC9B,IAAIsT,EACAvK,EAEJ,KACKA,EAAeuK,GAAmBvX,KAAKgS,GAAmBlF,uBAAuB7I,E,CACpF,MAAO/D,GACL,MAAMuB,EAAU,qCAAuCvB,EACvDF,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAOU,GAEjC,MAAM2F,EAAQ,IAAIrG,MAAMU,GAExB,MADAzB,KAAK+T,GAAmB3M,GAClBA,C,CAEV,GAAImQ,EAAgBnQ,MAAO,CACvB,MAAM3F,EAAU,oCAAsC8V,EAAgBnQ,MACtEpH,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAOU,GAEjC,MAAM2F,EAAQ,IAAIrG,MAAMU,GAExB,MADAzB,KAAK+T,GAAmB3M,GAClBA,C,CAMV,OAJIpH,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,8BAGrCxT,KAAK8T,KACE9G,CACX,CAEQsH,KACAtU,KAAKuO,WAAW0F,SAASU,oBAM7B3U,KAAK2R,GAAiB,IAAI1K,MAAOuQ,UAAYxX,KAAKyR,gCAElDzR,KAAK4U,KACT,CAEQP,KACJ,KAAKrU,KAAKuO,WAAW0F,UAAajU,KAAKuO,WAAW0F,SAASU,oBAEvD3U,KAAKyX,GAAiB7N,YAAW,IAAM5J,KAAK0X,iBAAiB1X,KAAKwR,kCAGnC3H,IAA3B7J,KAAK2X,KACT,CACI,IAAIC,EAAW5X,KAAK2R,GAAiB,IAAI1K,MAAOuQ,UAC5CI,EAAW,IACXA,EAAW,GAIf5X,KAAK2X,GAAoB/N,YAAW5E,UAChC,GAAIhF,KAAK4S,KAAqB/E,EAAmB8F,UAC7C,UACU3T,KAAKmU,GAAanU,KAAK+S,G,CAC/B,MAGE/S,KAAK4U,I,IAGdgD,E,CAGf,CAGQF,gBAIJ1X,KAAKuO,WAAWgC,KAAK,IAAIxP,MAAM,uEACnC,CAEQiE,SAA0B6S,GAC9B,MAAMtC,EAAasC,EAAkBC,OAAOlB,cACtCmB,EAAU/X,KAAKsS,GAASiD,GAC9B,IAAKwC,EAQD,OAPA/X,KAAK0I,EAAQ7F,IAAIhC,EAASwG,QAAS,mCAAmCkO,kBAGlEsC,EAAkB5B,eAClBjW,KAAK0I,EAAQ7F,IAAIhC,EAASwG,QAAS,wBAAwBkO,gCAAyCsC,EAAkB5B,wBAChHjW,KAAKoV,GAAkBpV,KAAKgY,GAAyBH,EAAkB5B,aAAc,kCAAmC,SAMtI,MAAMgC,EAAcF,EAAQzK,QAGtB4K,IAAkBL,EAAkB5B,aAE1C,IAAIkC,EACAC,EACAC,EACJ,IAAK,MAAMC,KAAKL,EACZ,IACI,MAAMM,EAAUJ,EAChBA,QAAYG,EAAElL,MAAMpN,KAAM6X,EAAkBW,WACxCN,GAAmBC,GAAOI,IAC1BvY,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,kCAAkCwU,gCACnE8C,EAAoBrY,KAAKgY,GAAyBH,EAAkB5B,aAAe,oCAAqC,OAG5HmC,OAAYvO,C,CACd,MAAO3J,GACLkY,EAAYlY,EACZF,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,8BAA8BwU,mBAA4BrV,M,CAG/FmY,QACMrY,KAAKoV,GAAkBiD,GACtBH,GAEHE,EACAC,EAAoBrY,KAAKgY,GAAyBH,EAAkB5B,aAAe,GAAGmC,IAAa,WACpFvO,IAARsO,EACPE,EAAoBrY,KAAKgY,GAAyBH,EAAkB5B,aAAe,KAAMkC,IAEzFnY,KAAK0I,EAAQ7F,IAAIhC,EAASwG,QAAS,wBAAwBkO,gCAAyCsC,EAAkB5B,kBAEtHoC,EAAoBrY,KAAKgY,GAAyBH,EAAkB5B,aAAe,kCAAmC,aAEpHjW,KAAKoV,GAAkBiD,IAEzBF,GACAnY,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,qBAAqBwU,kDAGlE,CAEQnD,GAAkBhL,GACtBpH,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,kCAAkCpM,4BAAgCpH,KAAK4S,OAGxG5S,KAAK4T,GAAwB5T,KAAK4T,IAAyBxM,GAAS,IAAI7F,EAAW,iFAI/EvB,KAAK8T,IACL9T,KAAK8T,KAGT9T,KAAKyY,GAA0BrR,GAAS,IAAIrG,MAAM,uEAElDf,KAAKoU,KACLpU,KAAK4U,KAED5U,KAAK4S,KAAqB/E,EAAmBmH,cAC7ChV,KAAKkV,GAAe9N,GACbpH,KAAK4S,KAAqB/E,EAAmB8F,WAAa3T,KAAK+R,EAEtE/R,KAAK0Y,GAAWtR,GACTpH,KAAK4S,KAAqB/E,EAAmB8F,WACpD3T,KAAKkV,GAAe9N,EAQ5B,CAEQ8N,GAAe9N,GACnB,GAAIpH,KAAK8S,GAAoB,CACzB9S,KAAK4S,GAAmB/E,EAAmBgF,aAC3C7S,KAAK8S,IAAqB,EACtB9S,KAAKuU,KACLvU,KAAKuU,GAAe/D,EAASpJ,QAAAA,EAAS,IAAIrG,MAAM,uBAChDf,KAAKuU,QAAiB1K,GAGtBrG,EAASC,WACTtD,OAAOwD,SAASgV,oBAAoB,SAAU3Y,KAAK4R,GAGvD,IACI5R,KAAKuS,GAAiB9N,SAASkG,GAAMA,EAAEyC,MAAMpN,KAAM,CAACoH,K,CACtD,MAAOlH,GACLF,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,0CAA0CqG,mBAAuBlH,M,EAG9G,CAEQ8E,SAAiBoC,GACrB,MAAMwR,EAAqB3R,KAAK4R,MAChC,IAAIC,EAA4B,EAC5BC,OAAuBlP,IAAVzC,EAAsBA,EAAQ,IAAIrG,MAAM,mDAErDiY,EAAiBhZ,KAAKiZ,GAAmBH,IAA6B,EAAGC,GAE7E,GAAuB,OAAnBC,EAGA,OAFAhZ,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,2GACjCxT,KAAKkV,GAAe9N,GAYxB,GARApH,KAAK4S,GAAmB/E,EAAmBsF,aAEvC/L,EACApH,KAAK0I,EAAQ7F,IAAIhC,EAAS0G,YAAa,6CAA6CH,OAEpFpH,KAAK0I,EAAQ7F,IAAIhC,EAAS0G,YAAa,4BAGA,IAAvCvH,KAAKwS,GAAuB3N,OAAc,CAC1C,IACI7E,KAAKwS,GAAuB/N,SAASkG,GAAMA,EAAEyC,MAAMpN,KAAM,CAACoH,K,CAC5D,MAAOlH,GACLF,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,iDAAiDqG,mBAAuBlH,M,CAI7G,GAAIF,KAAK4S,KAAqB/E,EAAmBsF,aAE7C,YADAnT,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,wF,CAKzC,KAA0B,OAAnBwF,GAAyB,CAQ5B,GAPAhZ,KAAK0I,EAAQ7F,IAAIhC,EAAS0G,YAAa,4BAA4BuR,mBAA2CE,eAExG,IAAIhO,SAASE,IACflL,KAAKiV,GAAwBrL,WAAWsB,EAAS8N,EAAgB,IAErEhZ,KAAKiV,QAAwBpL,EAEzB7J,KAAK4S,KAAqB/E,EAAmBsF,aAE7C,YADAnT,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,qFAIrC,IAMI,SALMxT,KAAKyT,KAEXzT,KAAK4S,GAAmB/E,EAAmB8F,UAC3C3T,KAAK0I,EAAQ7F,IAAIhC,EAAS0G,YAAa,2CAEG,IAAtCvH,KAAKyS,GAAsB5N,OAC3B,IACI7E,KAAKyS,GAAsBhO,SAASkG,GAAMA,EAAEyC,MAAMpN,KAAM,CAACA,KAAKuO,WAAW0E,gB,CAC3E,MAAO/S,GACLF,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,uDAAuDf,KAAKuO,WAAW0E,8BAA8B/S,M,CAI9I,M,CACF,MAAOA,GAGL,GAFAF,KAAK0I,EAAQ7F,IAAIhC,EAAS0G,YAAa,8CAA8CrH,OAEjFF,KAAK4S,KAAqB/E,EAAmBsF,aAM7C,OALAnT,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,4BAA4BxT,KAAK4S,qFAE9D5S,KAAK4S,KAA4B/E,EAAmBmH,eACpDhV,KAAKkV,MAKb6D,EAAa7Y,aAAaa,MAAQb,EAAI,IAAIa,MAAOb,EAAUyE,YAC3DqU,EAAiBhZ,KAAKiZ,GAAmBH,IAA6B7R,KAAK4R,MAAQD,EAAoBG,E,EAI/G/Y,KAAK0I,EAAQ7F,IAAIhC,EAAS0G,YAAa,+CAA+CN,KAAK4R,MAAQD,YAA6BE,gDAEhI9Y,KAAKkV,IACT,CAEQ+D,GAAmBC,EAA4BC,EAA6BC,GAChF,IACI,OAAOpZ,KAAK+R,EAAkBsH,6BAA6B,CACvDF,sBACAD,qBACAE,e,CAEN,MAAOlZ,GAEL,OADAF,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,6CAA6CmY,MAAuBC,mBAAqCjZ,OACnI,I,CAEf,CAEQuY,GAA0BrR,GAC9B,MAAMkS,EAAYtZ,KAAKqS,GACvBrS,KAAKqS,GAAa,CAAC,EAEnB3S,OAAO6L,KAAK+N,GACP7U,SAASjF,IACN,MAAMwX,EAAWsC,EAAU9Z,GAC3B,IACIwX,EAAS,KAAM5P,E,CACjB,MAAOlH,GACLF,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,wCAAwCqG,mBAAuBmB,EAAerI,K,IAG/H,CAEQ0U,KACA5U,KAAK2X,KACLvN,aAAapK,KAAK2X,IAClB3X,KAAK2X,QAAoB9N,EAEjC,CAEQuK,KACApU,KAAKyX,IACLrN,aAAapK,KAAKyX,GAE1B,CAEQlB,GAAkBhB,EAAoBC,EAAa+D,EAAsB7D,GAC7E,GAAI6D,EACA,OAAyB,IAArB7D,EAAU7Q,OACH,CACH2T,UAAWhD,EACXE,YACAoC,OAAQvC,EACR5H,KAAMC,EAAY8C,YAGf,CACH8H,UAAWhD,EACXsC,OAAQvC,EACR5H,KAAMC,EAAY8C,YAGvB,CACH,MAAMuF,EAAejW,KAAK0S,GAG1B,OAFA1S,KAAK0S,KAEoB,IAArBgD,EAAU7Q,OACH,CACH2T,UAAWhD,EACXS,aAAcA,EAAatR,WAC3B+Q,YACAoC,OAAQvC,EACR5H,KAAMC,EAAY8C,YAGf,CACH8H,UAAWhD,EACXS,aAAcA,EAAatR,WAC3BmT,OAAQvC,EACR5H,KAAMC,EAAY8C,W,CAIlC,CAEQ0F,GAAeX,EAA+BK,GAClD,GAAuB,IAAnBL,EAAQ5Q,OAAZ,CAKKiR,IACDA,EAAe9K,QAAQE,WAK3B,IAAK,MAAMsO,KAAY/D,EACnBA,EAAQ+D,GAAUrL,UAAU,CACxBD,SAAU,KACN4H,EAAeA,EAAaI,MAAK,IAAMlW,KAAKoV,GAAkBpV,KAAKgY,GAAyBwB,KAAW,EAE3GpS,MAAQ6G,IACJ,IAAIxM,EAEAA,EADAwM,aAAelN,MACLkN,EAAIxM,QACPwM,GAAOA,EAAItJ,SACRsJ,EAAItJ,WAEJ,gBAGdmR,EAAeA,EAAaI,MAAK,IAAMlW,KAAKoV,GAAkBpV,KAAKgY,GAAyBwB,EAAU/X,KAAU,EAEpHsM,KAAOC,IACH8H,EAAeA,EAAaI,MAAK,IAAMlW,KAAKoV,GAAkBpV,KAAKyZ,GAAyBD,EAAUxL,KAAO,G,CAI7H,CAEQ2H,GAAwBH,GAC5B,MAAMC,EAAgC,GAChCC,EAAsB,GAC5B,IAAK,IAAIgE,EAAI,EAAGA,EAAIlE,EAAK3Q,OAAQ6U,IAAK,CAClC,MAAMC,EAAWnE,EAAKkE,GACtB,GAAI1Z,KAAK4Z,GAAcD,GAAW,CAC9B,MAAMH,EAAWxZ,KAAK0S,GACtB1S,KAAK0S,KAEL+C,EAAQ+D,GAAYG,EACpBjE,EAAUtH,KAAKoL,EAAS7U,YAGxB6Q,EAAKlP,OAAOoT,EAAG,E,EAIvB,MAAO,CAACjE,EAASC,EACrB,CAEQkE,GAAcC,GAElB,OAAOA,GAAOA,EAAI1L,WAAsC,mBAAlB0L,EAAI1L,SAC9C,CAEQ0H,GAAwBN,EAAoBC,EAAaE,GAC7D,MAAMO,EAAejW,KAAK0S,GAG1B,OAFA1S,KAAK0S,KAEoB,IAArBgD,EAAU7Q,OACH,CACH2T,UAAWhD,EACXS,aAAcA,EAAatR,WAC3B+Q,YACAoC,OAAQvC,EACR5H,KAAMC,EAAYiD,kBAGf,CACH2H,UAAWhD,EACXS,aAAcA,EAAatR,WAC3BmT,OAAQvC,EACR5H,KAAMC,EAAYiD,iBAG9B,CAEQmF,GAAwB7E,GAC5B,MAAO,CACH8E,aAAc9E,EACdxD,KAAMC,EAAYkD,iBAE1B,CAEQ2I,GAAyBtI,EAAYnD,GACzC,MAAO,CACHiI,aAAc9E,EACdnD,OACAL,KAAMC,EAAY+C,WAE1B,CAEQqH,GAAyB7G,EAAY/J,EAAaqP,GACtD,OAAIrP,EACO,CACHA,QACA6O,aAAc9E,EACdxD,KAAMC,EAAYgD,YAInB,CACHqF,aAAc9E,EACdsF,SACA9I,KAAMC,EAAYgD,WAE1B,CAEQyE,KACJ,MAAO,CAAE1H,KAAMC,EAAYmD,MAC/B,EGvnCJ,MAAM+I,EAAuC,CAAC,EAAG,IAAM,IAAO,IAAO,MAG9D,MAAMC,EAGT/Y,YAAYgZ,GACRha,KAAKia,QAA+BpQ,IAAhBmQ,EAA4B,IAAIA,EAAa,MAAQF,CAC7E,CAEOT,6BAA6Ba,GAChC,OAAOla,KAAKia,GAAaC,EAAahB,mBAC1C,ECfG,MAAeiB,GACF,EAAAC,cAAgB,gBAChB,EAAAC,OAAS,SCEtB,MAAMC,UAA8BlY,EAKvCpB,YAAYuZ,EAAyBC,GACjCpZ,QAEApB,KAAKya,GAAeF,EACpBva,KAAK0a,GAAsBF,CAC/B,CAEOxV,WAAWoE,GACd,IAAIuR,GAAa,EACb3a,KAAK0a,MAAyB1a,KAAK4a,IAAiBxR,EAAQ/G,KAAO+G,EAAQ/G,IAAIgE,QAAQ,eAAiB,KAExGsU,GAAa,EACb3a,KAAK4a,SAAqB5a,KAAK0a,MAEnC1a,KAAK6a,GAAwBzR,GAC7B,MAAM1D,QAAiB1F,KAAKya,GAAalY,KAAK6G,GAE9C,OAAIuR,GAAsC,MAAxBjV,EAASxE,YAAsBlB,KAAK0a,IAClD1a,KAAK4a,SAAqB5a,KAAK0a,KAC/B1a,KAAK6a,GAAwBzR,SAChBpJ,KAAKya,GAAalY,KAAK6G,IAEjC1D,CACX,CAEQmV,GAAwBzR,GACvBA,EAAQ/D,UACT+D,EAAQ/D,QAAU,CAAC,GAEnBrF,KAAK4a,GACLxR,EAAQ/D,QAAQ8U,EAAYC,eAAiB,UAAUpa,KAAK4a,KAGvD5a,KAAK0a,IACNtR,EAAQ/D,QAAQ8U,EAAYC,uBACrBhR,EAAQ/D,QAAQ8U,EAAYC,cAG/C,CAEOzX,gBAAgBN,GACnB,OAAOrC,KAAKya,GAAa9X,gBAAgBN,EAC7C,ECjDJ,IAAYyY,EAYAC,GAZZ,SAAYD,GAER,mBAEA,+BAEA,2CAEA,gCACH,CATD,CAAYA,IAAAA,EAAiB,KAY7B,SAAYC,GAER,mBAEA,sBACH,CALD,CAAYA,IAAAA,EAAc,KCRnB,MAAM,EAAb,cACY,KAAAC,IAAsB,EACvB,KAAAxR,QAA+B,IAkB1C,CAhBWC,QACEzJ,KAAKgb,KACNhb,KAAKgb,IAAa,EACdhb,KAAKwJ,SACLxJ,KAAKwJ,UAGjB,CAEIW,aACA,OAAOnK,IACX,CAEIsJ,cACA,OAAOtJ,KAAKgb,EAChB,ECfG,MAAMC,EAeEC,kBACP,OAAOlb,KAAKmb,GAAW7R,OAC3B,CAEAtI,YAAYoE,EAAwBF,EAAiB5C,GACjDtC,KAAK8L,EAAc1G,EACnBpF,KAAK0I,EAAUxD,EACflF,KAAKmb,GAAa,IAAI,EACtBnb,KAAKob,GAAW9Y,EAEhBtC,KAAKqb,IAAW,EAEhBrb,KAAKiS,UAAY,KACjBjS,KAAKmS,QAAU,IACnB,CAEOnN,cAAc3C,EAAa2R,GAU9B,GATA9Q,EAAI2O,WAAWxP,EAAK,OACpBa,EAAI2O,WAAWmC,EAAgB,kBAC/B9Q,EAAIoY,KAAKtH,EAAgB+G,EAAgB,kBAEzC/a,KAAKub,GAAOlZ,EAEZrC,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,uCAG7ByO,IAAmB+G,EAAeS,QACP,oBAAnBpQ,gBAA+E,iBAAtC,IAAIA,gBAAiB3F,aACtE,MAAM,IAAI1E,MAAM,8FAGpB,MAAOsC,EAAMzC,GAAS0E,IAChBD,EAAU,CAAE,CAAChC,GAAOzC,KAAUZ,KAAKob,GAAS/V,SAE5CoW,EAA2B,CAC7BpS,YAAarJ,KAAKmb,GAAWhR,OAC7B9E,UACAM,QAAS,IACTC,gBAAiB5F,KAAKob,GAASxV,iBAG/BoO,IAAmB+G,EAAeS,SAClCC,EAAYhW,aAAe,eAK/B,MAAMiW,EAAU,GAAGrZ,OAAS4E,KAAK4R,QACjC7Y,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,oCAAoCmW,MACrE,MAAMhW,QAAiB1F,KAAK8L,EAAYjM,IAAI6b,EAASD,GACzB,MAAxB/V,EAASxE,YACTlB,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,qDAAqD2E,EAASxE,eAG/FlB,KAAK2b,GAAc,IAAI7a,EAAU4E,EAASxD,YAAc,GAAIwD,EAASxE,YACrElB,KAAKqb,IAAW,GAEhBrb,KAAKqb,IAAW,EAGpBrb,KAAK4b,GAAa5b,KAAK6b,GAAM7b,KAAKub,GAAME,EAC5C,CAEQzW,SAAY3C,EAAaoZ,GAC7B,IACI,KAAOzb,KAAKqb,IACR,IACI,MAAMK,EAAU,GAAGrZ,OAAS4E,KAAK4R,QACjC7Y,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,oCAAoCmW,MACrE,MAAMhW,QAAiB1F,KAAK8L,EAAYjM,IAAI6b,EAASD,GAEzB,MAAxB/V,EAASxE,YACTlB,KAAK0I,EAAQ7F,IAAIhC,EAAS0G,YAAa,sDAEvCvH,KAAKqb,IAAW,GACe,MAAxB3V,EAASxE,YAChBlB,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,qDAAqD2E,EAASxE,eAG/FlB,KAAK2b,GAAc,IAAI7a,EAAU4E,EAASxD,YAAc,GAAIwD,EAASxE,YACrElB,KAAKqb,IAAW,GAGZ3V,EAASvD,SACTnC,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,0CAA0CvB,EAAc0B,EAASvD,QAASnC,KAAKob,GAAS5V,uBACrHxF,KAAKiS,WACLjS,KAAKiS,UAAUvM,EAASvD,UAI5BnC,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,qD,CAG3C,MAAOrF,GACAF,KAAKqb,GAIFnb,aAAaoB,EAEbtB,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,uDAGjCvF,KAAK2b,GAAczb,EACnBF,KAAKqb,IAAW,GARpBrb,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,wDAAyDrF,EAAUuB,U,UAchHzB,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,6CAI5BvF,KAAKkb,aACNlb,KAAK8b,I,CAGjB,CAEO9W,WAAWf,GACd,OAAKjE,KAAKqb,GAGHpW,EAAYjF,KAAK0I,EAAS,cAAe1I,KAAK8L,EAAa9L,KAAKub,GAAOtX,EAAMjE,KAAKob,IAF9EpQ,QAAQC,OAAO,IAAIlK,MAAM,gDAGxC,CAEOiE,aACHhF,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,6CAGjCvF,KAAKqb,IAAW,EAChBrb,KAAKmb,GAAW1R,QAEhB,UACUzJ,KAAK4b,GAGX5b,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,qDAAqDvF,KAAKub,OAE3F,MAAMlW,EAAiC,CAAC,GACjChC,EAAMzC,GAAS0E,IACtBD,EAAQhC,GAAQzC,EAEhB,MAAMmb,EAA6B,CAC/B1W,QAAS,IAAKA,KAAYrF,KAAKob,GAAS/V,SACxCM,QAAS3F,KAAKob,GAASzV,QACvBC,gBAAiB5F,KAAKob,GAASxV,iBAGnC,IAAIwB,EACJ,UACUpH,KAAK8L,EAAYpJ,OAAO1C,KAAKub,GAAOQ,E,CAC5C,MAAO9N,GACL7G,EAAQ6G,C,CAGR7G,EACIA,aAAiBtG,IACQ,MAArBsG,EAAMlG,WACNlB,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,sFAEjCvF,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,2DAA2D6B,MAIpGpH,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,mD,SAIrCvF,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,0CAIjCvF,KAAK8b,I,CAEb,CAEQA,KACJ,GAAI9b,KAAKmS,QAAS,CACd,IAAI6J,EAAa,gDACbhc,KAAK2b,KACLK,GAAc,WAAahc,KAAK2b,IAEpC3b,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAOyW,GACjChc,KAAKmS,QAAQnS,KAAK2b,G,CAE1B,EC5MG,MAAMM,EAWTjb,YAAYoE,EAAwB8W,EAAiChX,EACzD5C,GACRtC,KAAK8L,EAAc1G,EACnBpF,KAAK4a,GAAesB,EACpBlc,KAAK0I,EAAUxD,EACflF,KAAKob,GAAW9Y,EAEhBtC,KAAKiS,UAAY,KACjBjS,KAAKmS,QAAU,IACnB,CAEOnN,cAAc3C,EAAa2R,GAc9B,OAbA9Q,EAAI2O,WAAWxP,EAAK,OACpBa,EAAI2O,WAAWmC,EAAgB,kBAC/B9Q,EAAIoY,KAAKtH,EAAgB+G,EAAgB,kBAEzC/a,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,+BAGjCvF,KAAKub,GAAOlZ,EAERrC,KAAK4a,KACLvY,IAAQA,EAAIgE,QAAQ,KAAO,EAAI,IAAM,KAAO,gBAAgB8V,mBAAmBnc,KAAK4a,OAGjF,IAAI5P,SAAc,CAACE,EAASD,KAC/B,IAMImR,EANAC,GAAS,EACb,GAAIrI,IAAmB+G,EAAeuB,KAAtC,CAMA,GAAI9Y,EAASC,WAAaD,EAASI,YAC/BwY,EAAc,IAAIpc,KAAKob,GAASmB,YAAala,EAAK,CAAEuD,gBAAiB5F,KAAKob,GAASxV,sBAChF,CAEH,MAAM6E,EAAUzK,KAAK8L,EAAYnJ,gBAAgBN,GAC3CgD,EAA0B,CAAC,EACjCA,EAAQgV,OAAS5P,EACjB,MAAOpH,EAAMzC,GAAS0E,IACtBD,EAAQhC,GAAQzC,EAEhBwb,EAAc,IAAIpc,KAAKob,GAASmB,YAAala,EAAK,CAAEuD,gBAAiB5F,KAAKob,GAASxV,gBAAiBP,QAAS,IAAKA,KAAYrF,KAAKob,GAAS/V,U,CAGhJ,IACI+W,EAAYI,UAAatc,IACrB,GAAIF,KAAKiS,UACL,IACIjS,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,kCAAkCvB,EAAc9D,EAAE+D,KAAMjE,KAAKob,GAAS5V,uBACvGxF,KAAKiS,UAAU/R,EAAE+D,K,CACnB,MAAOmD,GAEL,YADApH,KAAKyc,GAAOrV,E,GAOxBgV,EAAYzQ,QAAWzL,IAEfmc,EACArc,KAAKyc,KAELxR,EAAO,IAAIlK,MAAM,gQ,EAMzBqb,EAAYM,OAAS,KACjB1c,KAAK0I,EAAQ7F,IAAIhC,EAAS0G,YAAa,oBAAoBvH,KAAKub,MAChEvb,KAAK2c,GAAeP,EACpBC,GAAS,EACTnR,GAAS,C,CAEf,MAAOhL,GAEL,YADA+K,EAAO/K,E,OAlDP+K,EAAO,IAAIlK,MAAM,6E,GAsD7B,CAEOiE,WAAWf,GACd,OAAKjE,KAAK2c,GAGH1X,EAAYjF,KAAK0I,EAAS,MAAO1I,KAAK8L,EAAa9L,KAAKub,GAAOtX,EAAMjE,KAAKob,IAFtEpQ,QAAQC,OAAO,IAAIlK,MAAM,gDAGxC,CAEOwP,OAEH,OADAvQ,KAAKyc,KACEzR,QAAQE,SACnB,CAEQuR,GAAOvc,GACPF,KAAK2c,KACL3c,KAAK2c,GAAaC,QAClB5c,KAAK2c,QAAe9S,EAEhB7J,KAAKmS,SACLnS,KAAKmS,QAAQjS,GAGzB,ECnHG,MAAM2c,EAYT7b,YAAYoE,EAAwBoV,EAAkEtV,EAC1FM,EAA4BsX,EAA4CzX,GAChFrF,KAAK0I,EAAUxD,EACflF,KAAK0a,GAAsBF,EAC3Bxa,KAAK+c,GAAqBvX,EAC1BxF,KAAKgd,GAAwBF,EAC7B9c,KAAK8L,EAAc1G,EAEnBpF,KAAKiS,UAAY,KACjBjS,KAAKmS,QAAU,KACfnS,KAAKid,GAAW5X,CACpB,CAEOL,cAAc3C,EAAa2R,GAM9B,IAAIkJ,EAKJ,OAVAha,EAAI2O,WAAWxP,EAAK,OACpBa,EAAI2O,WAAWmC,EAAgB,kBAC/B9Q,EAAIoY,KAAKtH,EAAgB+G,EAAgB,kBACzC/a,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,sCAG7BvF,KAAK0a,KACLwC,QAAcld,KAAK0a,MAGhB,IAAI1P,SAAc,CAACE,EAASD,KAE/B,IAAIkS,EADJ9a,EAAMA,EAAI+a,QAAQ,QAAS,MAE3B,MAAM3S,EAAUzK,KAAK8L,EAAYnJ,gBAAgBN,GACjD,IAAIga,GAAS,EAEb,GAAI7Y,EAASE,QAAUF,EAASK,cAAe,CAC3C,MAAMwB,EAAiC,CAAC,GACjChC,EAAMzC,GAAS0E,IACtBD,EAAQhC,GAAQzC,EACZsc,IACA7X,EAAQ8U,EAAYC,eAAiB,UAAU8C,KAG/CzS,IACApF,EAAQ8U,EAAYE,QAAU5P,GAIlC0S,EAAY,IAAInd,KAAKgd,GAAsB3a,OAAKwH,EAAW,CACvDxE,QAAS,IAAKA,KAAYrF,KAAKid,K,MAK/BC,IACA7a,IAAQA,EAAIgE,QAAQ,KAAO,EAAI,IAAM,KAAO,gBAAgB8V,mBAAmBe,MAIlFC,IAEDA,EAAY,IAAInd,KAAKgd,GAAsB3a,IAG3C2R,IAAmB+G,EAAeS,SAClC2B,EAAUE,WAAa,eAG3BF,EAAUT,OAAUY,IAChBtd,KAAK0I,EAAQ7F,IAAIhC,EAAS0G,YAAa,0BAA0BlF,MACjErC,KAAKud,GAAaJ,EAClBd,GAAS,EACTnR,GAAS,EAGbiS,EAAUxR,QAAW6R,IACjB,IAAIpW,EAAa,KAGbA,EADsB,oBAAfqW,YAA8BD,aAAiBC,WAC9CD,EAAMpW,MAEN,wCAGZpH,KAAK0I,EAAQ7F,IAAIhC,EAAS0G,YAAa,0BAA0BH,KAAS,EAG9E+V,EAAUX,UAAa/a,IAEnB,GADAzB,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,yCAAyCvB,EAAcvC,EAAQwC,KAAMjE,KAAK+c,QACvG/c,KAAKiS,UACL,IACIjS,KAAKiS,UAAUxQ,EAAQwC,K,CACzB,MAAOmD,GAEL,YADApH,KAAKyc,GAAOrV,E,GAMxB+V,EAAUhL,QAAWqL,IAGjB,GAAInB,EACArc,KAAKyc,GAAOe,OACT,CACH,IAAIpW,EAAa,KAGbA,EADsB,oBAAfqW,YAA8BD,aAAiBC,WAC9CD,EAAMpW,MAEN,iSAMZ6D,EAAO,IAAIlK,MAAMqG,G,EAExB,GAET,CAEO7E,KAAK0B,GACR,OAAIjE,KAAKud,IAAcvd,KAAKud,GAAWG,aAAe1d,KAAKgd,GAAsBW,MAC7E3d,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,wCAAwCvB,EAAcC,EAAMjE,KAAK+c,QAClG/c,KAAKud,GAAWhb,KAAK0B,GACd+G,QAAQE,WAGZF,QAAQC,OAAO,qCAC1B,CAEOsF,OAOH,OANIvQ,KAAKud,IAGLvd,KAAKyc,QAAO5S,GAGTmB,QAAQE,SACnB,CAEQuR,GAAOe,GAEPxd,KAAKud,KAELvd,KAAKud,GAAWpL,QAAU,OAC1BnS,KAAKud,GAAWf,UAAY,OAC5Bxc,KAAKud,GAAW5R,QAAU,OAC1B3L,KAAKud,GAAWX,QAChB5c,KAAKud,QAAa1T,GAGtB7J,KAAK0I,EAAQ7F,IAAIhC,EAAS0E,MAAO,yCAE7BvF,KAAKmS,WACDnS,KAAK4d,GAAcJ,KAA8B,IAAnBA,EAAMK,UAAqC,MAAfL,EAAMM,KAEzDN,aAAiBzc,MACxBf,KAAKmS,QAAQqL,GAEbxd,KAAKmS,UAJLnS,KAAKmS,QAAQ,IAAIpR,MAAM,sCAAsCyc,EAAMM,SAASN,EAAMO,QAAU,wBAOxG,CAEQH,GAAcJ,GAClB,OAAOA,GAAmC,kBAAnBA,EAAMK,UAAgD,iBAAfL,EAAMM,IACxE,EC/IG,MAAME,EA0BThd,YAAYqB,EAAaC,EAAkC,CAAC,GlBqDzD,IAAsB4C,EkB7CrB,GArBI,KAAA+Y,GAA4D,OAKpD,KAAAhK,SAAgB,CAAC,EAMhB,KAAAiK,GAA4B,EAGzChb,EAAI2O,WAAWxP,EAAK,OAEpBrC,KAAK0I,OlBmDMmB,KADU3E,EkBlDO5C,EAAQ4C,QlBoD7B,IAAIwB,EAAc7F,EAAS0G,aAGvB,OAAXrC,EACOtC,EAAWI,cAGU6G,IAA3B3E,EAAmBrC,IACbqC,EAGJ,IAAIwB,EAAcxB,GkB9DrBlF,KAAKkT,QAAUlT,KAAKme,GAAY9b,IAEhCC,EAAUA,GAAW,CAAC,GACdkD,uBAAkDqE,IAA9BvH,EAAQkD,mBAA0ClD,EAAQkD,kBAC/C,kBAA5BlD,EAAQsD,sBAA6DiE,IAA5BvH,EAAQsD,gBAGxD,MAAM,IAAI7E,MAAM,mEAFhBuB,EAAQsD,qBAA8CiE,IAA5BvH,EAAQsD,iBAAuCtD,EAAQsD,gBAIrFtD,EAAQqD,aAA8BkE,IAApBvH,EAAQqD,QAAwB,IAAarD,EAAQqD,QAEvE,IAAIyY,EAAuB,KACvBC,EAAyB,KAE7B,GAAI7a,EAASE,OAA0C,CAGnD,MAAMkF,EAA0D,QAChEwV,EAAkBxV,EAAY,MAC9ByV,EAAoBzV,EAAY,c,CAG/BpF,EAASE,QAA+B,oBAAd4a,WAA8Bhc,EAAQgc,UAE1D9a,EAASE,SAAWpB,EAAQgc,WAC/BF,IACA9b,EAAQgc,UAAYF,GAHxB9b,EAAQgc,UAAYA,UAOnB9a,EAASE,QAAiC,oBAAhB6Y,aAAgCja,EAAQia,YAE5D/Y,EAASE,SAAWpB,EAAQia,kBACF,IAAtB8B,IACP/b,EAAQia,YAAc8B,GAH1B/b,EAAQia,YAAcA,YAO1Bvc,KAAK8L,EAAc,IAAIwO,EAAsBhY,EAAQ8C,YAAc,IAAIyG,EAAkB7L,KAAK0I,GAAUpG,EAAQkY,oBAChHxa,KAAK4S,GAAmB,eACxB5S,KAAK8S,IAAqB,EAC1B9S,KAAKob,GAAW9Y,EAEhBtC,KAAKiS,UAAY,KACjBjS,KAAKmS,QAAU,IACnB,CAIOnN,YAAYgP,GAOf,GANAA,EAAiBA,GAAkB+G,EAAeS,OAElDtY,EAAIoY,KAAKtH,EAAgB+G,EAAgB,kBAEzC/a,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,6CAA6CuH,EAAe/G,QAE/D,iBAA1BhU,KAAK4S,GACL,OAAO5H,QAAQC,OAAO,IAAIlK,MAAM,4EASpC,GANAf,KAAK4S,GAAmB,aAExB5S,KAAKue,GAAwBve,KAAKyT,GAAeO,SAC3ChU,KAAKue,GAG0B,kBAAjCve,KAAK4S,GAA2D,CAEhE,MAAMnR,EAAU,+DAMhB,OALAzB,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAOU,SAG3BzB,KAAK8U,GAEJ9J,QAAQC,OAAO,IAAI1J,EAAWE,G,CAClC,GAAqC,cAAjCzB,KAAK4S,GAAuD,CAEnE,MAAMnR,EAAU,8GAEhB,OADAzB,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAOU,GAC1BuJ,QAAQC,OAAO,IAAI1J,EAAWE,G,CAGzCzB,KAAK8S,IAAqB,CAC9B,CAEOvQ,KAAK0B,GACR,MAA8B,cAA1BjE,KAAK4S,GACE5H,QAAQC,OAAO,IAAIlK,MAAM,yEAG/Bf,KAAKwe,KACNxe,KAAKwe,GAAa,IAAIC,EAAmBze,KAAK0B,YAI3C1B,KAAKwe,GAAWjc,KAAK0B,GAChC,CAEOe,WAAWoC,GACd,MAA8B,iBAA1BpH,KAAK4S,IACL5S,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,+BAA+BpM,2EACzD4D,QAAQE,WAGW,kBAA1BlL,KAAK4S,IACL5S,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,+BAA+BpM,4EACzDpH,KAAK8U,KAGhB9U,KAAK4S,GAAmB,gBAExB5S,KAAK8U,GAAe,IAAI9J,SAASE,IAE7BlL,KAAKie,GAAuB/S,CAAO,UAIjClL,KAAK+U,GAAc3N,cACnBpH,KAAK8U,GACf,CAEQ9P,SAAoBoC,GAIxBpH,KAAK0e,GAAatX,EAElB,UACUpH,KAAKue,E,CACb,MAAOre,G,CAOT,GAAIF,KAAK0B,UAAW,CAChB,UACU1B,KAAK0B,UAAU6O,M,CACvB,MAAOrQ,GACLF,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,gDAAgDb,OACjFF,KAAK2e,I,CAGT3e,KAAK0B,eAAYmI,C,MAEjB7J,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,yFAEzC,CAEQxO,SAAqBgP,GAGzB,IAAI3R,EAAMrC,KAAKkT,QACflT,KAAK0a,GAAsB1a,KAAKob,GAASZ,mBACzCxa,KAAK8L,EAAY4O,GAAsB1a,KAAK0a,GAE5C,IACI,GAAI1a,KAAKob,GAASwD,gBAAiB,CAC/B,GAAI5e,KAAKob,GAAS1Z,YAAcoZ,EAAkB+D,WAO9C,MAAM,IAAI9d,MAAM,gFALhBf,KAAK0B,UAAY1B,KAAK8e,GAAoBhE,EAAkB+D,kBAGtD7e,KAAK+e,GAAgB1c,EAAK2R,E,KAIjC,CACH,IAAIgL,EAA+C,KAC/CC,EAAY,EAEhB,EAAG,CAGC,GAFAD,QAA0Bhf,KAAKkf,GAAwB7c,GAEzB,kBAA1BrC,KAAK4S,IAAgF,iBAA1B5S,KAAK4S,GAChE,MAAM,IAAIrR,EAAW,kDAGzB,GAAIyd,EAAkB5X,MAClB,MAAM,IAAIrG,MAAMie,EAAkB5X,OAGtC,GAAK4X,EAA0BG,gBAC3B,MAAM,IAAIpe,MAAM,gMAOpB,GAJIie,EAAkB3c,MAClBA,EAAM2c,EAAkB3c,KAGxB2c,EAAkB9C,YAAa,CAG/B,MAAMA,EAAc8C,EAAkB9C,YACtClc,KAAK0a,GAAsB,IAAMwB,EAEjClc,KAAK8L,EAAY8O,GAAesB,EAChClc,KAAK8L,EAAY4O,QAAsB7Q,C,CAG3CoV,G,OAEGD,EAAkB3c,KAAO4c,EA5O1B,KA8ON,GA9OM,MA8OFA,GAA+BD,EAAkB3c,IACjD,MAAM,IAAItB,MAAM,+CAGdf,KAAKof,GAAiB/c,EAAKrC,KAAKob,GAAS1Z,UAAWsd,EAAmBhL,E,CAG7EhU,KAAK0B,qBAAqBuZ,IAC1Bjb,KAAKiU,SAASU,mBAAoB,GAGR,eAA1B3U,KAAK4S,KAGL5S,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,8CACjCxT,KAAK4S,GAAmB,Y,CAM9B,MAAO1S,GAOL,OANAF,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,mCAAqCb,GACtEF,KAAK4S,GAAmB,eACxB5S,KAAK0B,eAAYmI,EAGjB7J,KAAKie,KACEjT,QAAQC,OAAO/K,E,CAE9B,CAEQ8E,SAA8B3C,GAClC,MAAMgD,EAAiC,CAAC,GACjChC,EAAMzC,GAAS0E,IACtBD,EAAQhC,GAAQzC,EAEhB,MAAMye,EAAerf,KAAKsf,GAAqBjd,GAC/CrC,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,gCAAgC6L,MACjE,IACI,MAAM3Z,QAAiB1F,KAAK8L,EAAYrJ,KAAK4c,EAAc,CACvDld,QAAS,GACTkD,QAAS,IAAKA,KAAYrF,KAAKob,GAAS/V,SACxCM,QAAS3F,KAAKob,GAASzV,QACvBC,gBAAiB5F,KAAKob,GAASxV,kBAGnC,GAA4B,MAAxBF,EAASxE,WACT,OAAO8J,QAAQC,OAAO,IAAIlK,MAAM,mDAAmD2E,EAASxE,gBAGhG,MAAM8d,EAAoBpS,KAAKc,MAAMhI,EAASvD,SAO9C,QANK6c,EAAkBO,kBAAoBP,EAAkBO,iBAAmB,KAG5EP,EAAkBQ,gBAAkBR,EAAkB/L,cAGtD+L,EAAkBS,uBAAgE,IAAxCzf,KAAKob,GAASsE,GACjD1U,QAAQC,OAAO,IAAInJ,EAAiC,mEAGxDkd,C,CACT,MAAO9e,GACL,IAAIe,EAAe,mDAAqDf,EAQxE,OAPIA,aAAaY,GACQ,MAAjBZ,EAAEgB,aACFD,GAA8B,uFAGtCjB,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAOE,GAE1B+J,QAAQC,OAAO,IAAInJ,EAAiCb,G,CAEnE,CAEQ0e,GAAkBtd,EAAamd,GACnC,OAAKA,EAIEnd,IAA6B,IAAtBA,EAAIgE,QAAQ,KAAc,IAAM,KAAO,MAAMmZ,IAHhDnd,CAIf,CAEQ2C,SAAuB3C,EAAaud,EAAgEZ,EAAuCa,GAC/I,IAAIC,EAAa9f,KAAK2f,GAAkBtd,EAAK2c,EAAkBQ,iBAC/D,GAAIxf,KAAK+f,GAAcH,GAMnB,OALA5f,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,2EACjCxT,KAAK0B,UAAYke,QACX5f,KAAK+e,GAAgBe,EAAYD,QAEvC7f,KAAKiT,aAAe+L,EAAkB/L,cAI1C,MAAM+M,EAA6B,GAC7BC,EAAajB,EAAkBkB,qBAAuB,GAC5D,IAAIC,EAA4CnB,EAChD,IAAK,MAAMoB,KAAYH,EAAY,CAC/B,MAAMI,EAAmBrgB,KAAKsgB,GAAyBF,EAAUR,EAAoBC,GAC7C,KAApCM,aAAS,EAATA,EAAWV,uBACf,GAAIY,aAA4Btf,MAE5Bif,EAAoB5R,KAAK,GAAGgS,EAAS1e,qBACrCse,EAAoB5R,KAAKiS,QACtB,GAAIrgB,KAAK+f,GAAcM,GAAmB,CAE7C,GADArgB,KAAK0B,UAAY2e,GACZF,EAAW,CACZ,IACIA,QAAkBngB,KAAKkf,GAAwB7c,E,CACjD,MAAOke,GACL,OAAOvV,QAAQC,OAAOsV,E,CAE1BT,EAAa9f,KAAK2f,GAAkBtd,EAAK8d,EAAUX,gB,CAEvD,IAGI,aAFMxf,KAAK+e,GAAgBe,EAAYD,QACvC7f,KAAKiT,aAAekN,EAAUlN,a,CAEhC,MAAOsN,GAKL,GAJAvgB,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,kCAAkCqf,EAAS1e,eAAe6e,KAC3FJ,OAAYtW,EACZmW,EAAoB5R,KAAK,IAAIvM,EAA4B,GAAGue,EAAS1e,qBAAqB6e,IAAMzF,EAAkBsF,EAAS1e,aAE7F,eAA1B1B,KAAK4S,GAAiD,CACtD,MAAMnR,EAAU,uDAEhB,OADAzB,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO/R,GAC1BuJ,QAAQC,OAAO,IAAI1J,EAAWE,G,IAMrD,OAAIue,EAAoBnb,OAAS,EACtBmG,QAAQC,OAAO,IAAIlJ,EAAgB,yEAAyEie,EAAoBpV,KAAK,OAAQoV,IAEjJhV,QAAQC,OAAO,IAAIlK,MAAM,+EACpC,CAEQ+d,GAAoBpd,GACxB,OAAQA,GACJ,KAAKoZ,EAAkB+D,WACnB,IAAK7e,KAAKob,GAASkD,UACf,MAAM,IAAIvd,MAAM,qDAEpB,OAAO,IAAI8b,EAAmB7c,KAAK8L,EAAa9L,KAAK0a,GAAqB1a,KAAK0I,EAAS1I,KAAKob,GAAS5V,kBAClGxF,KAAKob,GAASkD,UAAWte,KAAKob,GAAS/V,SAAW,CAAC,GAC3D,KAAKyV,EAAkB0F,iBACnB,IAAKxgB,KAAKob,GAASmB,YACf,MAAM,IAAIxb,MAAM,uDAEpB,OAAO,IAAIkb,EAA0Bjc,KAAK8L,EAAa9L,KAAK8L,EAAY8O,GAAc5a,KAAK0I,EAAS1I,KAAKob,IAC7G,KAAKN,EAAkB2F,YACnB,OAAO,IAAIxF,EAAqBjb,KAAK8L,EAAa9L,KAAK0I,EAAS1I,KAAKob,IACzE,QACI,MAAM,IAAIra,MAAM,sBAAsBW,MAElD,CAEQqd,GAAgB1c,EAAa2R,GAyBjC,OAxBAhU,KAAK0B,UAAWuQ,UAAYjS,KAAKiS,UAC7BjS,KAAKiU,SAASC,UACdlU,KAAK0B,UAAWyQ,QAAUnN,MAAO9E,IAC7B,IAAIwgB,GAAW,EACf,GAAI1gB,KAAKiU,SAASC,UAAlB,CACI,IACIlU,KAAKiU,SAASO,qBACRxU,KAAK0B,UAAWif,QAAQte,EAAK2R,SAC7BhU,KAAKiU,SAASQ,Q,CACtB,MACEiM,GAAW,C,CAOfA,GACA1gB,KAAK2e,GAAgBze,E,MALrBF,KAAK2e,GAAgBze,E,EAS7BF,KAAK0B,UAAWyQ,QAAWjS,GAAMF,KAAK2e,GAAgBze,GAEnDF,KAAK0B,UAAWif,QAAQte,EAAK2R,EACxC,CAEQsM,GAAyBF,EAA+BR,EAC5DC,EAAyCJ,GACzC,MAAM/d,EAAYoZ,EAAkBsF,EAAS1e,WAC7C,GAAIA,QAEA,OADA1B,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,uBAAuB4M,EAAS1e,0DAC1D,IAAIX,MAAM,uBAAuBqf,EAAS1e,0DAEjD,IAsIZ,SAA0Bke,EAAmDgB,GACzE,OAAQhB,GAAkE,IAA1CgB,EAAkBhB,EACtD,CAxIgBiB,CAAiBjB,EAAoBle,GAsBrC,OADA1B,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,uBAAuBsH,EAAkBpZ,8CACnE,IAAIE,EAAuB,IAAIkZ,EAAkBpZ,iCAA0CA,GApBlG,KADwB0e,EAASU,gBAAgBC,KAAKC,GAAMjG,EAAeiG,KACvD3a,QAAQwZ,IAA4B,GAgBpD,OADA7f,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,uBAAuBsH,EAAkBpZ,kEAA0EqZ,EAAe8E,QAC5J,IAAI9e,MAAM,IAAI+Z,EAAkBpZ,wBAAgCqZ,EAAe8E,OAftF,GAAKne,IAAcoZ,EAAkB+D,aAAe7e,KAAKob,GAASkD,WAC7D5c,IAAcoZ,EAAkB0F,mBAAqBxgB,KAAKob,GAASmB,YAEpE,OADAvc,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,uBAAuBsH,EAAkBpZ,yDACnE,IAAIF,EAA0B,IAAIsZ,EAAkBpZ,4CAAqDA,GAEhH1B,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,wBAAwBsH,EAAkBpZ,QAC3E,IAEI,OADA1B,KAAKiU,SAASC,UAAYxS,IAAcoZ,EAAkB+D,WAAaY,OAAuB5V,EACvF7J,KAAK8e,GAAoBpd,E,CAClC,MAAO6e,GACL,OAAOA,C,CAY/B,CAEQR,GAAcre,GAClB,OAAOA,GAAoC,iBAAhB,GAA4B,YAAaA,CACxE,CAEQid,GAAgBvX,GASpB,GARApH,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,iCAAiCpM,4BAAgCpH,KAAK4S,OAEvG5S,KAAK0B,eAAYmI,EAGjBzC,EAAQpH,KAAK0e,IAActX,EAC3BpH,KAAK0e,QAAa7U,EAEY,iBAA1B7J,KAAK4S,GAAT,CAKA,GAA8B,eAA1B5S,KAAK4S,GAEL,MADA5S,KAAK0I,EAAQ7F,IAAIhC,EAASwG,QAAS,yCAAyCD,2EACtE,IAAIrG,MAAM,iCAAiCqG,wEAyBrD,GAtB8B,kBAA1BpH,KAAK4S,IAGL5S,KAAKie,KAGL7W,EACApH,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,uCAAuCqG,OAExEpH,KAAK0I,EAAQ7F,IAAIhC,EAAS0G,YAAa,4BAGvCvH,KAAKwe,KACLxe,KAAKwe,GAAWjO,OAAO/J,OAAOtG,IAC1BF,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,0CAA0Cb,MAAM,IAErFF,KAAKwe,QAAa3U,GAGtB7J,KAAKiT,kBAAepJ,EACpB7J,KAAK4S,GAAmB,eAEpB5S,KAAK8S,GAAoB,CACzB9S,KAAK8S,IAAqB,EAC1B,IACQ9S,KAAKmS,SACLnS,KAAKmS,QAAQ/K,E,CAEnB,MAAOlH,GACLF,KAAK0I,EAAQ7F,IAAIhC,EAASE,MAAO,0BAA0BqG,mBAAuBlH,M,QAtCtFF,KAAK0I,EAAQ7F,IAAIhC,EAAS2S,MAAO,yCAAyCpM,8EAyClF,CAEQ+W,GAAY9b,GAEhB,GAAuC,IAAnCA,EAAI4e,YAAY,WAAY,IAA8C,IAAlC5e,EAAI4e,YAAY,UAAW,GACnE,OAAO5e,EAGX,IAAKmB,EAASC,UACV,MAAM,IAAI1C,MAAM,mBAAmBsB,OAQvC,MAAM6e,EAAO/gB,OAAOwD,SAASwd,cAAc,KAI3C,OAHAD,EAAKE,KAAO/e,EAEZrC,KAAK0I,EAAQ7F,IAAIhC,EAAS0G,YAAa,gBAAgBlF,UAAY6e,EAAKE,UACjEF,EAAKE,IAChB,CAEQ9B,GAAqBjd,GACzB,MAAMgd,EAAe,IAAIgC,IAAIhf,GAEzBgd,EAAaiC,SAASC,SAAS,KAC/BlC,EAAaiC,UAAY,YAEzBjC,EAAaiC,UAAY,aAE7B,MAAME,EAAe,IAAIC,gBAAgBpC,EAAamC,cAgBtD,OAdKA,EAAaE,IAAI,qBAClBF,EAAaG,OAAO,mBAAoB3hB,KAAKke,GAAkBvZ,YAG/D6c,EAAaE,IAAI,wBACgC,SAA7CF,EAAa3hB,IAAI,0BACjBG,KAAKob,GAASsE,IAAwB,IAEK,IAAxC1f,KAAKob,GAASsE,IACrB8B,EAAaG,OAAO,uBAAwB,QAGhDtC,EAAauC,OAASJ,EAAa7c,WAE5B0a,EAAa1a,UACxB,EAQG,MAAM8Z,EAOTzd,YAA6B6gB,GAAA,KAAAA,GAAAA,EANrB,KAAAC,GAAiB,GAEjB,KAAAC,IAAsB,EAK1B/hB,KAAKgiB,GAAoB,IAAIC,EAC7BjiB,KAAKkiB,GAAmB,IAAID,EAE5BjiB,KAAKmiB,GAAmBniB,KAAKoiB,IACjC,CAEO7f,KAAK0B,GAKR,OAJAjE,KAAKqiB,GAAYpe,GACZjE,KAAKkiB,KACNliB,KAAKkiB,GAAmB,IAAID,GAEzBjiB,KAAKkiB,GAAiBI,OACjC,CAEO/R,OAGH,OAFAvQ,KAAK+hB,IAAa,EAClB/hB,KAAKgiB,GAAkB9W,UAChBlL,KAAKmiB,EAChB,CAEQE,GAAYpe,GAChB,GAAIjE,KAAK8hB,GAAQjd,eAAiB7E,KAAK8hB,GAAQ,WAAc,EACzD,MAAM,IAAI/gB,MAAM,sCAAsCf,KAAY,6BAA2B,KAGjGA,KAAK8hB,GAAQ1T,KAAKnK,GAClBjE,KAAKgiB,GAAkB9W,SAC3B,CAEQlG,WACJ,OAAa,CAGT,SAFMhF,KAAKgiB,GAAkBM,SAExBtiB,KAAK+hB,GAAY,CACd/hB,KAAKkiB,IACLliB,KAAKkiB,GAAiBjX,OAAO,uBAGjC,K,CAGJjL,KAAKgiB,GAAoB,IAAIC,EAE7B,MAAMM,EAAkBviB,KAAKkiB,GAC7BliB,KAAKkiB,QAAmBrY,EAExB,MAAM5F,EAAmC,iBAArBjE,KAAK8hB,GAAQ,GAC7B9hB,KAAK8hB,GAAQlX,KAAK,IAClB6T,EAAmB+D,GAAexiB,KAAK8hB,IAE3C9hB,KAAK8hB,GAAQjd,OAAS,EAEtB,UACU7E,KAAK6hB,GAAWtf,KAAK0B,GAC3Bse,EAAgBrX,S,CAClB,MAAO9D,GACLmb,EAAgBtX,OAAO7D,E,EAGnC,CAEQjE,UAAsBsf,GAC1B,MAAMC,EAAcD,EAAa1B,KAAK4B,GAAMA,EAAEte,aAAYue,QAAO,CAACC,EAAGF,IAAME,EAAIF,IACzElM,EAAS,IAAIlS,WAAWme,GAC9B,IAAII,EAAS,EACb,IAAK,MAAM9U,KAAQyU,EACfhM,EAAOsM,IAAI,IAAIxe,WAAWyJ,GAAO8U,GACjCA,GAAU9U,EAAK3J,WAGnB,OAAOoS,EAAOlJ,MAClB,EAGJ,MAAM0U,EAKFjhB,cACIhB,KAAKsiB,QAAU,IAAItX,SAAQ,CAACE,EAASD,KAAYjL,KAAKiQ,EAAWjQ,KAAKgjB,IAAa,CAAC9X,EAASD,IACjG,CAEOC,UACHlL,KAAKiQ,GACT,CAEOhF,OAAO8S,GACV/d,KAAKgjB,GAAWjF,EACpB,ECzrBG,MAAMkF,EAAb,cAGoB,KAAA5f,KANmB,OAQnB,KAAAwE,QAAkB,EAGlB,KAAAmM,eAAiC+G,EAAeuB,IAqHpE,CA9GWlF,cAAclL,EAAehH,GAEhC,GAAqB,iBAAVgH,EACP,MAAM,IAAInL,MAAM,2DAGpB,IAAKmL,EACD,MAAO,GAGI,OAAXhH,IACAA,EAAStC,EAAWI,UAIxB,MAAMmJ,EAAWJ,EAAkB2B,MAAMxB,GAEnCgX,EAAc,GACpB,IAAK,MAAMzhB,KAAW0K,EAAU,CAC5B,MAAMgX,EAAgBvW,KAAKc,MAAMjM,GACjC,GAAkC,iBAAvB0hB,EAAcxV,KACrB,MAAM,IAAI5M,MAAM,oBAEpB,OAAQoiB,EAAcxV,MAClB,KAAKC,EAAY8C,WACb1Q,KAAKsP,EAAqB6T,GAC1B,MACJ,KAAKvV,EAAY+C,WACb3Q,KAAKojB,GAAqBD,GAC1B,MACJ,KAAKvV,EAAYgD,WACb5Q,KAAKqjB,GAAqBF,GAC1B,MACJ,KAAKvV,EAAYoD,KAGjB,KAAKpD,EAAYmD,MAEb,MACJ,KAAKnD,EAAYqD,IACbjR,KAAKsjB,GAAcH,GACnB,MACJ,KAAKvV,EAAYuC,SACbnQ,KAAKujB,GAAmBJ,GACxB,MACJ,QAEIje,EAAOrC,IAAIhC,EAAS0G,YAAa,yBAA2B4b,EAAcxV,KAAO,cACjF,SAERuV,EAAY9U,KAAK+U,E,CAGrB,OAAOD,CACX,CAOO9T,aAAa3N,GAChB,OAAOsK,EAAkBY,MAAMC,KAAKC,UAAUpL,GAClD,CAEQ6N,EAAqB7N,GACzBzB,KAAKwjB,GAAsB/hB,EAAQqW,OAAQ,gDAEdjO,IAAzBpI,EAAQwU,cACRjW,KAAKwjB,GAAsB/hB,EAAQwU,aAAc,0CAEzD,CAEQmN,GAAqB3hB,GAGzB,GAFAzB,KAAKwjB,GAAsB/hB,EAAQwU,aAAc,gDAE5BpM,IAAjBpI,EAAQuM,KACR,MAAM,IAAIjN,MAAM,0CAExB,CAEQsiB,GAAqB5hB,GACzB,GAAIA,EAAQgV,QAAUhV,EAAQ2F,MAC1B,MAAM,IAAIrG,MAAM,4CAGfU,EAAQgV,QAAUhV,EAAQ2F,OAC3BpH,KAAKwjB,GAAsB/hB,EAAQ2F,MAAO,2CAG9CpH,KAAKwjB,GAAsB/hB,EAAQwU,aAAc,0CACrD,CAEQqN,GAAc7hB,GAClB,GAAkC,iBAAvBA,EAAQuO,WACf,MAAM,IAAIjP,MAAM,sCAExB,CAEQwiB,GAAmB9hB,GACvB,GAAkC,iBAAvBA,EAAQuO,WACf,MAAM,IAAIjP,MAAM,2CAExB,CAEQyiB,GAAsB5iB,EAAYK,GACtC,GAAqB,iBAAVL,GAAgC,KAAVA,EAC7B,MAAM,IAAIG,MAAME,EAExB,ECxHJ,MAAMwiB,EAA+C,CACjDC,MAAO7iB,EAAS0E,MAChBoe,MAAO9iB,EAAS2S,MAChBhM,KAAM3G,EAAS0G,YACfqc,YAAa/iB,EAAS0G,YACtBD,KAAMzG,EAASwG,QACfwc,QAAShjB,EAASwG,QAClBD,MAAOvG,EAASE,MAChB+iB,SAAUjjB,EAASsG,SACnB4c,KAAMljB,EAASmjB,MAgBZ,MAAMC,GA+CFC,iBAAiBC,GAGpB,GAFAjhB,EAAI2O,WAAWsS,EAAS,gBA8KNta,IA5KLsa,EA4KHthB,IA3KN7C,KAAKkF,OAASif,OACX,GAAuB,iBAAZA,EAAsB,CACpC,MAAMpd,EAlElB,SAAuB1D,GAInB,MAAM+gB,EAAUX,EAAoBpgB,EAAKuT,eACzC,QAAuB,IAAZwN,EACP,OAAOA,EAEP,MAAM,IAAIrjB,MAAM,sBAAsBsC,IAE9C,CAwD6BghB,CAAcF,GAC/BnkB,KAAKkF,OAAS,IAAIwB,EAAcK,E,MAEhC/G,KAAKkF,OAAS,IAAIwB,EAAcyd,GAGpC,OAAOnkB,IACX,CA0BOskB,QAAQjiB,EAAakiB,GAiBxB,OAhBArhB,EAAI2O,WAAWxP,EAAK,OACpBa,EAAIshB,WAAWniB,EAAK,OAEpBrC,KAAKqC,IAAMA,EAKPrC,KAAKykB,sBAD6B,iBAA3BF,EACsB,IAAKvkB,KAAKykB,yBAA0BF,GAEpC,IACtBvkB,KAAKykB,sBACR/iB,UAAW6iB,GAIZvkB,IACX,CAMO0kB,gBAAgBpW,GAInB,OAHApL,EAAI2O,WAAWvD,EAAU,YAEzBtO,KAAKsO,SAAWA,EACTtO,IACX,CAmBO2kB,uBAAuBC,GAC1B,GAAI5kB,KAAKuR,gBACL,MAAM,IAAIxQ,MAAM,2CAWpB,OARK6jB,EAEMvX,MAAMwX,QAAQD,GACrB5kB,KAAKuR,gBAAkB,IAAIwI,EAAuB6K,GAElD5kB,KAAKuR,gBAAkBqT,EAJvB5kB,KAAKuR,gBAAkB,IAAIwI,EAOxB/Z,IACX,CAMO8kB,kBAAkBC,GAKrB,OAJA7hB,EAAI2O,WAAWkT,EAAc,gBAE7B/kB,KAAKglB,GAA+BD,EAE7B/kB,IACX,CAMOilB,sBAAsBF,GAKzB,OAJA7hB,EAAI2O,WAAWkT,EAAc,gBAE7B/kB,KAAKklB,GAAmCH,EAEjC/kB,IACX,CAMOmlB,sBAAsB7iB,GAQzB,YAPmCuH,IAA/B7J,KAAKykB,wBACLzkB,KAAKykB,sBAAwB,CAAC,GAElCzkB,KAAKykB,sBAAsB/E,IAAwB,EAEnD1f,KAAK8R,EAA+BxP,aAAO,EAAPA,EAASkM,WAEtCxO,IACX,CAMOolB,QAGH,MAAMX,EAAwBzkB,KAAKykB,uBAAyB,CAAC,EAS7D,QANqC5a,IAAjC4a,EAAsBvf,SAEtBuf,EAAsBvf,OAASlF,KAAKkF,SAInClF,KAAKqC,IACN,MAAM,IAAItB,MAAM,4FAEpB,MAAMwN,EAAa,IAAIyP,EAAehe,KAAKqC,IAAKoiB,GAEhD,OAAOnT,EAAc+T,OACjB9W,EACAvO,KAAKkF,QAAUtC,EAAWI,SAC1BhD,KAAKsO,UAAY,IAAI2U,EACrBjjB,KAAKuR,gBACLvR,KAAKglB,GACLhlB,KAAKklB,GACLllB,KAAK8R,EACb,E,OC3PCvN,WAAWjE,UAAU+F,SACtB3G,OAAOC,eAAe4E,WAAWjE,UAAW,UAAW,CACnDM,MAAOyM,MAAM/M,UAAU+F,QACvBif,UAAU,IAGb/gB,WAAWjE,UAAUgN,OACtB5N,OAAOC,eAAe4E,WAAWjE,UAAW,QAAS,CAGjDM,MAAO,SAASwS,EAAgBmS,GAAgB,OAAO,IAAIhhB,WAAW8I,MAAM/M,UAAUgN,MAAM9M,KAAKR,KAAMoT,EAAOmS,GAAO,EACrHD,UAAU,IAGb/gB,WAAWjE,UAAUmE,SACtB/E,OAAOC,eAAe4E,WAAWjE,UAAW,UAAW,CACnDM,MAAOyM,MAAM/M,UAAUmE,QACvB6gB,UAAU,I,M/BxBK,iBAAZhmB,SAA0C,iBAAXkmB,OACxCA,OAAOlmB,QAAUH,IACQ,mBAAXsmB,QAAyBA,OAAOC,IAC9CD,OAAO,GAAItmB,GACe,iBAAZG,QACdA,QAAiB,QAAIH,IAErBD,EAAc,QAAIC", "sources": ["webpack://signalR/webpack/universalModuleDefinition", "webpack://signalR/webpack/bootstrap", "webpack://signalR/webpack/runtime/define property getters", "webpack://signalR/webpack/runtime/global", "webpack://signalR/webpack/runtime/hasOwnProperty shorthand", "webpack://signalR/webpack/runtime/make namespace object", "webpack://signalR/src/ILogger.ts", "webpack://signalR/src/Errors.ts", "webpack://signalR/src/HttpClient.ts", "webpack://signalR/src/Loggers.ts", "webpack://signalR/src/Utils.ts", "webpack://signalR/src/FetchHttpClient.ts", "webpack://signalR/src/XhrHttpClient.ts", "webpack://signalR/src/DefaultHttpClient.ts", "webpack://signalR/src/TextMessageFormat.ts", "webpack://signalR/src/HandshakeProtocol.ts", "webpack://signalR/src/IHubProtocol.ts", "webpack://signalR/src/HubConnection.ts", "webpack://signalR/src/Subject.ts", "webpack://signalR/src/MessageBuffer.ts", "webpack://signalR/src/DefaultReconnectPolicy.ts", "webpack://signalR/src/HeaderNames.ts", "webpack://signalR/src/AccessTokenHttpClient.ts", "webpack://signalR/src/ITransport.ts", "webpack://signalR/src/AbortController.ts", "webpack://signalR/src/LongPollingTransport.ts", "webpack://signalR/src/ServerSentEventsTransport.ts", "webpack://signalR/src/WebSocketTransport.ts", "webpack://signalR/src/HttpConnection.ts", "webpack://signalR/src/JsonHubProtocol.ts", "webpack://signalR/src/HubConnectionBuilder.ts", "webpack://signalR/src/browser-index.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"signalR\"] = factory();\n\telse\n\t\troot[\"signalR\"] = factory();\n})(self, () => {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// These values are designed to match the ASP.NET Log Levels since that's the pattern we're emulating here.\r\n/** Indicates the severity of a log message.\r\n *\r\n * Log Levels are ordered in increasing severity. So `Debug` is more severe than `Trace`, etc.\r\n */\r\nexport enum LogLevel {\r\n    /** Log level for very low severity diagnostic messages. */\r\n    Trace = 0,\r\n    /** Log level for low severity diagnostic messages. */\r\n    Debug = 1,\r\n    /** Log level for informational diagnostic messages. */\r\n    Information = 2,\r\n    /** Log level for diagnostic messages that indicate a non-fatal problem. */\r\n    Warning = 3,\r\n    /** Log level for diagnostic messages that indicate a failure in the current operation. */\r\n    Error = 4,\r\n    /** Log level for diagnostic messages that indicate a failure that will terminate the entire application. */\r\n    Critical = 5,\r\n    /** The highest possible log level. Used when configuring logging to indicate that no log messages should be emitted. */\r\n    None = 6,\r\n}\r\n\r\n/** An abstraction that provides a sink for diagnostic messages. */\r\nexport interface ILogger {\r\n    /** Called by the framework to emit a diagnostic message.\r\n     *\r\n     * @param {LogLevel} logLevel The severity level of the message.\r\n     * @param {string} message The message.\r\n     */\r\n    log(logLevel: LogLevel, message: string): void;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpTransportType } from \"./ITransport\";\r\n\r\n/** Error thrown when an HTTP request fails. */\r\nexport class HttpError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The HTTP status code represented by this error. */\r\n    public statusCode: number;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     * @param {number} statusCode The HTTP status code represented by this error.\r\n     */\r\n    constructor(errorMessage: string, statusCode: number) {\r\n        const trueProto = new.target.prototype;\r\n        super(`${errorMessage}: Status code '${statusCode}'`);\r\n        this.statusCode = statusCode;\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when a timeout elapses. */\r\nexport class TimeoutError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.TimeoutError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage: string = \"A timeout occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when an action is aborted. */\r\nexport class AbortError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** Constructs a new instance of {@link AbortError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage: string = \"An abort occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport is unsupported by the browser. */\r\n/** @private */\r\nexport class UnsupportedTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occurred on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.UnsupportedTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'UnsupportedTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport is disabled by the browser. */\r\n/** @private */\r\nexport class DisabledTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occurred on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.DisabledTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'DisabledTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport cannot be started. */\r\n/** @private */\r\nexport class FailedToStartTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occurred on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToStartTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'FailedToStartTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the negotiation with the server failed to complete. */\r\n/** @private */\r\nexport class FailedToNegotiateWithServerError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToNegotiateWithServerError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     */\r\n    constructor(message: string) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.errorType = 'FailedToNegotiateWithServerError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when multiple errors have occurred. */\r\n/** @private */\r\nexport class AggregateErrors extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The collection of errors this error is aggregating. */\r\n    public innerErrors: Error[];\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.AggregateErrors}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {Error[]} innerErrors The collection of errors this error is aggregating.\r\n     */\r\n    constructor(message: string, innerErrors: Error[]) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n\r\n        this.innerErrors = innerErrors;\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortSignal } from \"./AbortController\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\n\r\n/** Represents an HTTP request. */\r\nexport interface HttpRequest {\r\n    /** The HTTP method to use for the request. */\r\n    method?: string;\r\n\r\n    /** The URL for the request. */\r\n    url?: string;\r\n\r\n    /** The body content for the request. May be a string or an ArrayBuffer (for binary data). */\r\n    content?: string | ArrayBuffer;\r\n\r\n    /** An object describing headers to apply to the request. */\r\n    headers?: MessageHeaders;\r\n\r\n    /** The XMLHttpRequestResponseType to apply to the request. */\r\n    responseType?: XMLHttpRequestResponseType;\r\n\r\n    /** An AbortSignal that can be monitored for cancellation. */\r\n    abortSignal?: AbortSignal;\r\n\r\n    /** The time to wait for the request to complete before throwing a TimeoutError. Measured in milliseconds. */\r\n    timeout?: number;\r\n\r\n    /** This controls whether credentials such as cookies are sent in cross-site requests. */\r\n    withCredentials?: boolean;\r\n}\r\n\r\n/** Represents an HTTP response. */\r\nexport class HttpResponse {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     */\r\n    constructor(statusCode: number);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code and message.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code, message and string content.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     * @param {string} content The content of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string, content: string);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code, message and binary content.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     * @param {ArrayBuffer} content The content of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string, content: ArrayBuffer);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code, message and binary content.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     * @param {string | ArrayBuffer} content The content of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string, content: string | ArrayBuffer);\r\n    constructor(\r\n        public readonly statusCode: number,\r\n        public readonly statusText?: string,\r\n        public readonly content?: string | ArrayBuffer) {\r\n    }\r\n}\r\n\r\n/** Abstraction over an HTTP client.\r\n *\r\n * This class provides an abstraction over an HTTP client so that a different implementation can be provided on different platforms.\r\n */\r\nexport abstract class HttpClient {\r\n    /** Issues an HTTP GET request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public get(url: string): Promise<HttpResponse>;\r\n\r\n    /** Issues an HTTP GET request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @param {HttpRequest} options Additional options to configure the request. The 'url' field in this object will be overridden by the url parameter.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public get(url: string, options: HttpRequest): Promise<HttpResponse>;\r\n    public get(url: string, options?: HttpRequest): Promise<HttpResponse> {\r\n        return this.send({\r\n            ...options,\r\n            method: \"GET\",\r\n            url,\r\n        });\r\n    }\r\n\r\n    /** Issues an HTTP POST request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public post(url: string): Promise<HttpResponse>;\r\n\r\n    /** Issues an HTTP POST request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @param {HttpRequest} options Additional options to configure the request. The 'url' field in this object will be overridden by the url parameter.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public post(url: string, options: HttpRequest): Promise<HttpResponse>;\r\n    public post(url: string, options?: HttpRequest): Promise<HttpResponse> {\r\n        return this.send({\r\n            ...options,\r\n            method: \"POST\",\r\n            url,\r\n        });\r\n    }\r\n\r\n    /** Issues an HTTP DELETE request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public delete(url: string): Promise<HttpResponse>;\r\n\r\n    /** Issues an HTTP DELETE request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @param {HttpRequest} options Additional options to configure the request. The 'url' field in this object will be overridden by the url parameter.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public delete(url: string, options: HttpRequest): Promise<HttpResponse>;\r\n    public delete(url: string, options?: HttpRequest): Promise<HttpResponse> {\r\n        return this.send({\r\n            ...options,\r\n            method: \"DELETE\",\r\n            url,\r\n        });\r\n    }\r\n\r\n    /** Issues an HTTP request to the specified URL, returning a {@link Promise} that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {HttpRequest} request An {@link @microsoft/signalr.HttpRequest} describing the request to send.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an HttpResponse describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public abstract send(request: HttpRequest): Promise<HttpResponse>;\r\n\r\n    /** Gets all cookies that apply to the specified URL.\r\n     *\r\n     * @param url The URL that the cookies are valid for.\r\n     * @returns {string} A string containing all the key-value cookie pairs for the specified URL.\r\n     */\r\n    // @ts-ignore\r\n    public getCookieString(url: string): string {\r\n        return \"\";\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\n\r\n/** A logger that does nothing when log messages are sent to it. */\r\nexport class NullLogger implements ILogger {\r\n    /** The singleton instance of the {@link @microsoft/signalr.NullLogger}. */\r\n    public static instance: ILogger = new NullLogger();\r\n\r\n    private constructor() {}\r\n\r\n    /** @inheritDoc */\r\n    // eslint-disable-next-line\r\n    public log(_logLevel: LogLevel, _message: string): void {\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { IStreamSubscriber, ISubscription } from \"./Stream\";\r\nimport { Subject } from \"./Subject\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n// Version token that will be replaced by the prepack command\r\n/** The version of the SignalR client. */\r\n\r\nexport const VERSION: string = \"0.0.0-DEV_BUILD\";\r\n/** @private */\r\nexport class Arg {\r\n    public static isRequired(val: any, name: string): void {\r\n        if (val === null || val === undefined) {\r\n            throw new Error(`The '${name}' argument is required.`);\r\n        }\r\n    }\r\n    public static isNotEmpty(val: string, name: string): void {\r\n        if (!val || val.match(/^\\s*$/)) {\r\n            throw new Error(`The '${name}' argument should not be empty.`);\r\n        }\r\n    }\r\n\r\n    public static isIn(val: any, values: any, name: string): void {\r\n        // TypeScript enums have keys for **both** the name and the value of each enum member on the type itself.\r\n        if (!(val in values)) {\r\n            throw new Error(`Unknown ${name} value: ${val}.`);\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport class Platform {\r\n    // react-native has a window but no document so we should check both\r\n    public static get isBrowser(): boolean {\r\n        return !Platform.isNode && typeof window === \"object\" && typeof window.document === \"object\";\r\n    }\r\n\r\n    // WebWorkers don't have a window object so the isBrowser check would fail\r\n    public static get isWebWorker(): boolean {\r\n        return !Platform.isNode && typeof self === \"object\" && \"importScripts\" in self;\r\n    }\r\n\r\n    // react-native has a window but no document\r\n    static get isReactNative(): boolean {\r\n        return !Platform.isNode && typeof window === \"object\" && typeof window.document === \"undefined\";\r\n    }\r\n\r\n    // Node apps shouldn't have a window object, but WebWorkers don't either\r\n    // so we need to check for both WebWorker and window\r\n    public static get isNode(): boolean {\r\n        return typeof process !== \"undefined\" && process.release && process.release.name === \"node\";\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getDataDetail(data: any, includeContent: boolean): string {\r\n    let detail = \"\";\r\n    if (isArrayBuffer(data)) {\r\n        detail = `Binary data of length ${data.byteLength}`;\r\n        if (includeContent) {\r\n            detail += `. Content: '${formatArrayBuffer(data)}'`;\r\n        }\r\n    } else if (typeof data === \"string\") {\r\n        detail = `String data of length ${data.length}`;\r\n        if (includeContent) {\r\n            detail += `. Content: '${data}'`;\r\n        }\r\n    }\r\n    return detail;\r\n}\r\n\r\n/** @private */\r\nexport function formatArrayBuffer(data: ArrayBuffer): string {\r\n    const view = new Uint8Array(data);\r\n\r\n    // Uint8Array.map only supports returning another Uint8Array?\r\n    let str = \"\";\r\n    view.forEach((num) => {\r\n        const pad = num < 16 ? \"0\" : \"\";\r\n        str += `0x${pad}${num.toString(16)} `;\r\n    });\r\n\r\n    // Trim of trailing space.\r\n    return str.substr(0, str.length - 1);\r\n}\r\n\r\n// Also in signalr-protocol-msgpack/Utils.ts\r\n/** @private */\r\nexport function isArrayBuffer(val: any): val is ArrayBuffer {\r\n    return val && typeof ArrayBuffer !== \"undefined\" &&\r\n        (val instanceof ArrayBuffer ||\r\n            // Sometimes we get an ArrayBuffer that doesn't satisfy instanceof\r\n            (val.constructor && val.constructor.name === \"ArrayBuffer\"));\r\n}\r\n\r\n/** @private */\r\nexport async function sendMessage(logger: ILogger, transportName: string, httpClient: HttpClient, url: string,\r\n                                  content: string | ArrayBuffer, options: IHttpConnectionOptions): Promise<void> {\r\n    const headers: {[k: string]: string} = {};\r\n\r\n    const [name, value] = getUserAgentHeader();\r\n    headers[name] = value;\r\n\r\n    logger.log(LogLevel.Trace, `(${transportName} transport) sending data. ${getDataDetail(content, options.logMessageContent!)}.`);\r\n\r\n    const responseType = isArrayBuffer(content) ? \"arraybuffer\" : \"text\";\r\n    const response = await httpClient.post(url, {\r\n        content,\r\n        headers: { ...headers, ...options.headers},\r\n        responseType,\r\n        timeout: options.timeout,\r\n        withCredentials: options.withCredentials,\r\n    });\r\n\r\n    logger.log(LogLevel.Trace, `(${transportName} transport) request complete. Response status: ${response.statusCode}.`);\r\n}\r\n\r\n/** @private */\r\nexport function createLogger(logger?: ILogger | LogLevel): ILogger {\r\n    if (logger === undefined) {\r\n        return new ConsoleLogger(LogLevel.Information);\r\n    }\r\n\r\n    if (logger === null) {\r\n        return NullLogger.instance;\r\n    }\r\n\r\n    if ((logger as ILogger).log !== undefined) {\r\n        return logger as ILogger;\r\n    }\r\n\r\n    return new ConsoleLogger(logger as LogLevel);\r\n}\r\n\r\n/** @private */\r\nexport class SubjectSubscription<T> implements ISubscription<T> {\r\n    private _subject: Subject<T>;\r\n    private _observer: IStreamSubscriber<T>;\r\n\r\n    constructor(subject: Subject<T>, observer: IStreamSubscriber<T>) {\r\n        this._subject = subject;\r\n        this._observer = observer;\r\n    }\r\n\r\n    public dispose(): void {\r\n        const index: number = this._subject.observers.indexOf(this._observer);\r\n        if (index > -1) {\r\n            this._subject.observers.splice(index, 1);\r\n        }\r\n\r\n        if (this._subject.observers.length === 0 && this._subject.cancelCallback) {\r\n            this._subject.cancelCallback().catch((_) => { });\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport class ConsoleLogger implements ILogger {\r\n    private readonly _minLevel: LogLevel;\r\n\r\n    // Public for testing purposes.\r\n    public out: {\r\n        error(message: any): void,\r\n        warn(message: any): void,\r\n        info(message: any): void,\r\n        log(message: any): void,\r\n    };\r\n\r\n    constructor(minimumLogLevel: LogLevel) {\r\n        this._minLevel = minimumLogLevel;\r\n        this.out = console;\r\n    }\r\n\r\n    public log(logLevel: LogLevel, message: string): void {\r\n        if (logLevel >= this._minLevel) {\r\n            const msg = `[${new Date().toISOString()}] ${LogLevel[logLevel]}: ${message}`;\r\n            switch (logLevel) {\r\n                case LogLevel.Critical:\r\n                case LogLevel.Error:\r\n                    this.out.error(msg);\r\n                    break;\r\n                case LogLevel.Warning:\r\n                    this.out.warn(msg);\r\n                    break;\r\n                case LogLevel.Information:\r\n                    this.out.info(msg);\r\n                    break;\r\n                default:\r\n                    // console.debug only goes to attached debuggers in Node, so we use console.log for Trace and Debug\r\n                    this.out.log(msg);\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getUserAgentHeader(): [string, string] {\r\n    let userAgentHeaderName = \"X-SignalR-User-Agent\";\r\n    if (Platform.isNode) {\r\n        userAgentHeaderName = \"User-Agent\";\r\n    }\r\n    return [ userAgentHeaderName, constructUserAgent(VERSION, getOsName(), getRuntime(), getRuntimeVersion()) ];\r\n}\r\n\r\n/** @private */\r\nexport function constructUserAgent(version: string, os: string, runtime: string, runtimeVersion: string | undefined): string {\r\n    // Microsoft SignalR/[Version] ([Detailed Version]; [Operating System]; [Runtime]; [Runtime Version])\r\n    let userAgent: string = \"Microsoft SignalR/\";\r\n\r\n    const majorAndMinor = version.split(\".\");\r\n    userAgent += `${majorAndMinor[0]}.${majorAndMinor[1]}`;\r\n    userAgent += ` (${version}; `;\r\n\r\n    if (os && os !== \"\") {\r\n        userAgent += `${os}; `;\r\n    } else {\r\n        userAgent += \"Unknown OS; \";\r\n    }\r\n\r\n    userAgent += `${runtime}`;\r\n\r\n    if (runtimeVersion) {\r\n        userAgent += `; ${runtimeVersion}`;\r\n    } else {\r\n        userAgent += \"; Unknown Runtime Version\";\r\n    }\r\n\r\n    userAgent += \")\";\r\n    return userAgent;\r\n}\r\n\r\n// eslint-disable-next-line spaced-comment\r\n/*#__PURE__*/ function getOsName(): string {\r\n    if (Platform.isNode) {\r\n        switch (process.platform) {\r\n            case \"win32\":\r\n                return \"Windows NT\";\r\n            case \"darwin\":\r\n                return \"macOS\";\r\n            case \"linux\":\r\n                return \"Linux\";\r\n            default:\r\n                return process.platform;\r\n        }\r\n    } else {\r\n        return \"\";\r\n    }\r\n}\r\n\r\n// eslint-disable-next-line spaced-comment\r\n/*#__PURE__*/ function getRuntimeVersion(): string | undefined {\r\n    if (Platform.isNode) {\r\n        return process.versions.node;\r\n    }\r\n    return undefined;\r\n}\r\n\r\nfunction getRuntime(): string {\r\n    if (Platform.isNode) {\r\n        return \"NodeJS\";\r\n    } else {\r\n        return \"Browser\";\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getErrorString(e: any): string {\r\n    if (e.stack) {\r\n        return e.stack;\r\n    } else if (e.message) {\r\n        return e.message;\r\n    }\r\n    return `${e}`;\r\n}\r\n\r\n/** @private */\r\nexport function getGlobalThis(): unknown {\r\n    // globalThis is semi-new and not available in Node until v12\r\n    if (typeof globalThis !== \"undefined\") {\r\n        return globalThis;\r\n    }\r\n    if (typeof self !== \"undefined\") {\r\n        return self;\r\n    }\r\n    if (typeof window !== \"undefined\") {\r\n        return window;\r\n    }\r\n    if (typeof global !== \"undefined\") {\r\n        return global;\r\n    }\r\n    throw new Error(\"could not find global\");\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// @ts-ignore: This will be removed from built files and is here to make the types available during dev work\r\nimport { CookieJar } from \"@types/tough-cookie\";\r\n\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { Platform, getGlobalThis, isArrayBuffer } from \"./Utils\";\r\n\r\nexport class FetchHttpClient extends HttpClient {\r\n    private readonly _abortControllerType: { prototype: AbortController, new(): AbortController };\r\n    private readonly _fetchType: (input: RequestInfo, init?: RequestInit) => Promise<Response>;\r\n    private readonly _jar?: CookieJar;\r\n\r\n    private readonly _logger: ILogger;\r\n\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n        this._logger = logger;\r\n\r\n        // Node added a fetch implementation to the global scope starting in v18.\r\n        // We need to add a cookie jar in node to be able to share cookies with WebSocket\r\n        if (typeof fetch === \"undefined\" || Platform.isNode) {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n\r\n            // Cookies aren't automatically handled in Node so we need to add a CookieJar to preserve cookies across requests\r\n            this._jar = new (requireFunc(\"tough-cookie\")).CookieJar();\r\n\r\n            if (typeof fetch === \"undefined\") {\r\n                this._fetchType = requireFunc(\"node-fetch\");\r\n            } else {\r\n                // Use fetch from Node if available\r\n                this._fetchType = fetch;\r\n            }\r\n\r\n            // node-fetch doesn't have a nice API for getting and setting cookies\r\n            // fetch-cookie will wrap a fetch implementation with a default CookieJar or a provided one\r\n            this._fetchType = requireFunc(\"fetch-cookie\")(this._fetchType, this._jar);\r\n        } else {\r\n            this._fetchType = fetch.bind(getGlobalThis());\r\n        }\r\n        if (typeof AbortController === \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n\r\n            // Node needs EventListener methods on AbortController which our custom polyfill doesn't provide\r\n            this._abortControllerType = requireFunc(\"abort-controller\");\r\n        } else {\r\n            this._abortControllerType = AbortController;\r\n        }\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public async send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            throw new AbortError();\r\n        }\r\n\r\n        if (!request.method) {\r\n            throw new Error(\"No method defined.\");\r\n        }\r\n        if (!request.url) {\r\n            throw new Error(\"No url defined.\");\r\n        }\r\n\r\n        const abortController = new this._abortControllerType();\r\n\r\n        let error: any;\r\n        // Hook our abortSignal into the abort controller\r\n        if (request.abortSignal) {\r\n            request.abortSignal.onabort = () => {\r\n                abortController.abort();\r\n                error = new AbortError();\r\n            };\r\n        }\r\n\r\n        // If a timeout has been passed in, setup a timeout to call abort\r\n        // Type needs to be any to fit window.setTimeout and NodeJS.setTimeout\r\n        let timeoutId: any = null;\r\n        if (request.timeout) {\r\n            const msTimeout = request.timeout!;\r\n            timeoutId = setTimeout(() => {\r\n                abortController.abort();\r\n                this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\r\n                error = new TimeoutError();\r\n            }, msTimeout);\r\n        }\r\n\r\n        if (request.content === \"\") {\r\n            request.content = undefined;\r\n        }\r\n        if (request.content) {\r\n            // Explicitly setting the Content-Type header for React Native on Android platform.\r\n            request.headers = request.headers || {};\r\n            if (isArrayBuffer(request.content)) {\r\n                request.headers[\"Content-Type\"] = \"application/octet-stream\";\r\n            } else {\r\n                request.headers[\"Content-Type\"] = \"text/plain;charset=UTF-8\";\r\n            }\r\n        }\r\n\r\n        let response: Response;\r\n        try {\r\n            response = await this._fetchType(request.url!, {\r\n                body: request.content,\r\n                cache: \"no-cache\",\r\n                credentials: request.withCredentials === true ? \"include\" : \"same-origin\",\r\n                headers: {\r\n                    \"X-Requested-With\": \"XMLHttpRequest\",\r\n                    ...request.headers,\r\n                },\r\n                method: request.method!,\r\n                mode: \"cors\",\r\n                redirect: \"follow\",\r\n                signal: abortController.signal,\r\n            });\r\n        } catch (e) {\r\n            if (error) {\r\n                throw error;\r\n            }\r\n            this._logger.log(\r\n                LogLevel.Warning,\r\n                `Error from HTTP request. ${e}.`,\r\n            );\r\n            throw e;\r\n        } finally {\r\n            if (timeoutId) {\r\n                clearTimeout(timeoutId);\r\n            }\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = null;\r\n            }\r\n        }\r\n\r\n        if (!response.ok) {\r\n            const errorMessage = await deserializeContent(response, \"text\") as string;\r\n            throw new HttpError(errorMessage || response.statusText, response.status);\r\n        }\r\n\r\n        const content = deserializeContent(response, request.responseType);\r\n        const payload = await content;\r\n\r\n        return new HttpResponse(\r\n            response.status,\r\n            response.statusText,\r\n            payload,\r\n        );\r\n    }\r\n\r\n    public getCookieString(url: string): string {\r\n        let cookies: string = \"\";\r\n        if (Platform.isNode && this._jar) {\r\n            // @ts-ignore: unused variable\r\n            this._jar.getCookies(url, (e, c) => cookies = c.join(\"; \"));\r\n        }\r\n        return cookies;\r\n    }\r\n}\r\n\r\nfunction deserializeContent(response: Response, responseType?: XMLHttpRequestResponseType): Promise<string | ArrayBuffer> {\r\n    let content;\r\n    switch (responseType) {\r\n        case \"arraybuffer\":\r\n            content = response.arrayBuffer();\r\n            break;\r\n        case \"text\":\r\n            content = response.text();\r\n            break;\r\n        case \"blob\":\r\n        case \"document\":\r\n        case \"json\":\r\n            throw new Error(`${responseType} is not supported.`);\r\n        default:\r\n            content = response.text();\r\n            break;\r\n    }\r\n\r\n    return content;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n\r\nexport class XhrHttpClient extends HttpClient {\r\n    private readonly _logger: ILogger;\r\n\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n        this._logger = logger;\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new AbortError());\r\n        }\r\n\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n\r\n        return new Promise<HttpResponse>((resolve, reject) => {\r\n            const xhr = new XMLHttpRequest();\r\n\r\n            xhr.open(request.method!, request.url!, true);\r\n            xhr.withCredentials = request.withCredentials === undefined ? true : request.withCredentials;\r\n            xhr.setRequestHeader(\"X-Requested-With\", \"XMLHttpRequest\");\r\n            if (request.content === \"\") {\r\n                request.content = undefined;\r\n            }\r\n            if (request.content) {\r\n                // Explicitly setting the Content-Type header for React Native on Android platform.\r\n                if (isArrayBuffer(request.content)) {\r\n                    xhr.setRequestHeader(\"Content-Type\", \"application/octet-stream\");\r\n                } else {\r\n                    xhr.setRequestHeader(\"Content-Type\", \"text/plain;charset=UTF-8\");\r\n                }\r\n            }\r\n\r\n            const headers = request.headers;\r\n            if (headers) {\r\n                Object.keys(headers)\r\n                    .forEach((header) => {\r\n                        xhr.setRequestHeader(header, headers[header]);\r\n                    });\r\n            }\r\n\r\n            if (request.responseType) {\r\n                xhr.responseType = request.responseType;\r\n            }\r\n\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = () => {\r\n                    xhr.abort();\r\n                    reject(new AbortError());\r\n                };\r\n            }\r\n\r\n            if (request.timeout) {\r\n                xhr.timeout = request.timeout;\r\n            }\r\n\r\n            xhr.onload = () => {\r\n                if (request.abortSignal) {\r\n                    request.abortSignal.onabort = null;\r\n                }\r\n\r\n                if (xhr.status >= 200 && xhr.status < 300) {\r\n                    resolve(new HttpResponse(xhr.status, xhr.statusText, xhr.response || xhr.responseText));\r\n                } else {\r\n                    reject(new HttpError(xhr.response || xhr.responseText || xhr.statusText, xhr.status));\r\n                }\r\n            };\r\n\r\n            xhr.onerror = () => {\r\n                this._logger.log(LogLevel.Warning, `Error from HTTP request. ${xhr.status}: ${xhr.statusText}.`);\r\n                reject(new HttpError(xhr.statusText, xhr.status));\r\n            };\r\n\r\n            xhr.ontimeout = () => {\r\n                this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\r\n                reject(new TimeoutError());\r\n            };\r\n\r\n            xhr.send(request.content);\r\n        });\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortError } from \"./Errors\";\r\nimport { FetchHttpClient } from \"./FetchHttpClient\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger } from \"./ILogger\";\r\nimport { Platform } from \"./Utils\";\r\nimport { XhrHttpClient } from \"./XhrHttpClient\";\r\n\r\n/** Default implementation of {@link @microsoft/signalr.HttpClient}. */\r\nexport class DefaultHttpClient extends HttpClient {\r\n    private readonly _httpClient: HttpClient;\r\n\r\n    /** Creates a new instance of the {@link @microsoft/signalr.DefaultHttpClient}, using the provided {@link @microsoft/signalr.ILogger} to log messages. */\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n\r\n        if (typeof fetch !== \"undefined\" || Platform.isNode) {\r\n            this._httpClient = new FetchHttpClient(logger);\r\n        } else if (typeof XMLHttpRequest !== \"undefined\") {\r\n            this._httpClient = new XhrHttpClient(logger);\r\n        } else {\r\n            throw new Error(\"No usable HttpClient found.\");\r\n        }\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new AbortError());\r\n        }\r\n\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n\r\n        return this._httpClient.send(request);\r\n    }\r\n\r\n    public getCookieString(url: string): string {\r\n        return this._httpClient.getCookieString(url);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Not exported from index\r\n/** @private */\r\nexport class TextMessageFormat {\r\n    public static RecordSeparatorCode = 0x1e;\r\n    public static RecordSeparator = String.fromCharCode(TextMessageFormat.RecordSeparatorCode);\r\n\r\n    public static write(output: string): string {\r\n        return `${output}${TextMessageFormat.RecordSeparator}`;\r\n    }\r\n\r\n    public static parse(input: string): string[] {\r\n        if (input[input.length - 1] !== TextMessageFormat.RecordSeparator) {\r\n            throw new Error(\"Message is incomplete.\");\r\n        }\r\n\r\n        const messages = input.split(TextMessageFormat.RecordSeparator);\r\n        messages.pop();\r\n        return messages;\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n\r\n/** @private */\r\nexport interface HandshakeRequestMessage {\r\n    readonly protocol: string;\r\n    readonly version: number;\r\n}\r\n\r\n/** @private */\r\nexport interface HandshakeResponseMessage {\r\n    readonly error: string;\r\n    readonly minorVersion: number;\r\n}\r\n\r\n/** @private */\r\nexport class HandshakeProtocol {\r\n    // Handshake request is always JSON\r\n    public writeHandshakeRequest(handshakeRequest: HandshakeRequestMessage): string {\r\n        return TextMessageFormat.write(JSON.stringify(handshakeRequest));\r\n    }\r\n\r\n    public parseHandshakeResponse(data: any): [any, HandshakeResponseMessage] {\r\n        let messageData: string;\r\n        let remainingData: any;\r\n\r\n        if (isArrayBuffer(data)) {\r\n            // Format is binary but still need to read JSON text from handshake response\r\n            const binaryData = new Uint8Array(data);\r\n            const separatorIndex = binaryData.indexOf(TextMessageFormat.RecordSeparatorCode);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = String.fromCharCode.apply(null, Array.prototype.slice.call(binaryData.slice(0, responseLength)));\r\n            remainingData = (binaryData.byteLength > responseLength) ? binaryData.slice(responseLength).buffer : null;\r\n        } else {\r\n            const textData: string = data;\r\n            const separatorIndex = textData.indexOf(TextMessageFormat.RecordSeparator);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = textData.substring(0, responseLength);\r\n            remainingData = (textData.length > responseLength) ? textData.substring(responseLength) : null;\r\n        }\r\n\r\n        // At this point we should have just the single handshake message\r\n        const messages = TextMessageFormat.parse(messageData);\r\n        const response = JSON.parse(messages[0]);\r\n        if (response.type) {\r\n            throw new Error(\"Expected a handshake response from the server.\");\r\n        }\r\n        const responseMessage: HandshakeResponseMessage = response;\r\n\r\n        // multiple messages could have arrived with handshake\r\n        // return additional data to be parsed as usual, or null if all parsed\r\n        return [remainingData, responseMessage];\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { ILogger } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\n\r\n/** Defines the type of a Hub Message. */\r\nexport enum MessageType {\r\n    /** Indicates the message is an Invocation message and implements the {@link @microsoft/signalr.InvocationMessage} interface. */\r\n    Invocation = 1,\r\n    /** Indicates the message is a StreamItem message and implements the {@link @microsoft/signalr.StreamItemMessage} interface. */\r\n    StreamItem = 2,\r\n    /** Indicates the message is a Completion message and implements the {@link @microsoft/signalr.CompletionMessage} interface. */\r\n    Completion = 3,\r\n    /** Indicates the message is a Stream Invocation message and implements the {@link @microsoft/signalr.StreamInvocationMessage} interface. */\r\n    StreamInvocation = 4,\r\n    /** Indicates the message is a Cancel Invocation message and implements the {@link @microsoft/signalr.CancelInvocationMessage} interface. */\r\n    CancelInvocation = 5,\r\n    /** Indicates the message is a Ping message and implements the {@link @microsoft/signalr.PingMessage} interface. */\r\n    Ping = 6,\r\n    /** Indicates the message is a Close message and implements the {@link @microsoft/signalr.CloseMessage} interface. */\r\n    Close = 7,\r\n    Ack = 8,\r\n    Sequence = 9\r\n}\r\n\r\n/** Defines a dictionary of string keys and string values representing headers attached to a Hub message. */\r\nexport interface MessageHeaders {\r\n    /** Gets or sets the header with the specified key. */\r\n    [key: string]: string;\r\n}\r\n\r\n/** Union type of all known Hub messages. */\r\nexport type HubMessage =\r\n    InvocationMessage |\r\n    StreamInvocationMessage |\r\n    StreamItemMessage |\r\n    CompletionMessage |\r\n    CancelInvocationMessage |\r\n    PingMessage |\r\n    CloseMessage |\r\n    AckMessage |\r\n    SequenceMessage;\r\n\r\n/** Defines properties common to all Hub messages. */\r\nexport interface HubMessageBase {\r\n    /** A {@link @microsoft/signalr.MessageType} value indicating the type of this message. */\r\n    readonly type: MessageType;\r\n}\r\n\r\n/** Defines properties common to all Hub messages relating to a specific invocation. */\r\nexport interface HubInvocationMessage extends HubMessageBase {\r\n    /** A {@link @microsoft/signalr.MessageHeaders} dictionary containing headers attached to the message. */\r\n    readonly headers?: MessageHeaders;\r\n    /** The ID of the invocation relating to this message.\r\n     *\r\n     * This is expected to be present for {@link @microsoft/signalr.StreamInvocationMessage} and {@link @microsoft/signalr.CompletionMessage}. It may\r\n     * be 'undefined' for an {@link @microsoft/signalr.InvocationMessage} if the sender does not expect a response.\r\n     */\r\n    readonly invocationId?: string;\r\n}\r\n\r\n/** A hub message representing a non-streaming invocation. */\r\nexport interface InvocationMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Invocation;\r\n    /** The target method name. */\r\n    readonly target: string;\r\n    /** The target method arguments. */\r\n    readonly arguments: any[];\r\n    /** The target methods stream IDs. */\r\n    readonly streamIds?: string[];\r\n}\r\n\r\n/** A hub message representing a streaming invocation. */\r\nexport interface StreamInvocationMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.StreamInvocation;\r\n\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n    /** The target method name. */\r\n    readonly target: string;\r\n    /** The target method arguments. */\r\n    readonly arguments: any[];\r\n    /** The target methods stream IDs. */\r\n    readonly streamIds?: string[];\r\n}\r\n\r\n/** A hub message representing a single item produced as part of a result stream. */\r\nexport interface StreamItemMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.StreamItem;\r\n\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n\r\n    /** The item produced by the server. */\r\n    readonly item?: any;\r\n}\r\n\r\n/** A hub message representing the result of an invocation. */\r\nexport interface CompletionMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Completion;\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n    /** The error produced by the invocation, if any.\r\n     *\r\n     * Either {@link @microsoft/signalr.CompletionMessage.error} or {@link @microsoft/signalr.CompletionMessage.result} must be defined, but not both.\r\n     */\r\n    readonly error?: string;\r\n    /** The result produced by the invocation, if any.\r\n     *\r\n     * Either {@link @microsoft/signalr.CompletionMessage.error} or {@link @microsoft/signalr.CompletionMessage.result} must be defined, but not both.\r\n     */\r\n    readonly result?: any;\r\n}\r\n\r\n/** A hub message indicating that the sender is still active. */\r\nexport interface PingMessage extends HubMessageBase {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Ping;\r\n}\r\n\r\n/** A hub message indicating that the sender is closing the connection.\r\n *\r\n * If {@link @microsoft/signalr.CloseMessage.error} is defined, the sender is closing the connection due to an error.\r\n */\r\nexport interface CloseMessage extends HubMessageBase {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Close;\r\n    /** The error that triggered the close, if any.\r\n     *\r\n     * If this property is undefined, the connection was closed normally and without error.\r\n     */\r\n    readonly error?: string;\r\n\r\n    /** If true, clients with automatic reconnects enabled should attempt to reconnect after receiving the CloseMessage. Otherwise, they should not. */\r\n    readonly allowReconnect?: boolean;\r\n}\r\n\r\n/** A hub message sent to request that a streaming invocation be canceled. */\r\nexport interface CancelInvocationMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.CancelInvocation;\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n}\r\n\r\nexport interface AckMessage extends HubMessageBase\r\n{\r\n    readonly type: MessageType.Ack;\r\n\r\n    readonly sequenceId: number;\r\n}\r\n\r\nexport interface SequenceMessage extends HubMessageBase\r\n{\r\n    readonly type: MessageType.Sequence;\r\n\r\n    readonly sequenceId: number;\r\n}\r\n\r\n/** A protocol abstraction for communicating with SignalR Hubs.  */\r\nexport interface IHubProtocol {\r\n    /** The name of the protocol. This is used by SignalR to resolve the protocol between the client and server. */\r\n    readonly name: string;\r\n    /** The version of the protocol. */\r\n    readonly version: number;\r\n    /** The {@link @microsoft/signalr.TransferFormat} of the protocol. */\r\n    readonly transferFormat: TransferFormat;\r\n\r\n    /** Creates an array of {@link @microsoft/signalr.HubMessage} objects from the specified serialized representation.\r\n     *\r\n     * If {@link @microsoft/signalr.IHubProtocol.transferFormat} is 'Text', the `input` parameter must be a string, otherwise it must be an ArrayBuffer.\r\n     *\r\n     * @param {string | ArrayBuffer} input A string or ArrayBuffer containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    parseMessages(input: string | ArrayBuffer, logger: ILogger): HubMessage[];\r\n\r\n    /** Writes the specified {@link @microsoft/signalr.HubMessage} to a string or ArrayBuffer and returns it.\r\n     *\r\n     * If {@link @microsoft/signalr.IHubProtocol.transferFormat} is 'Text', the result of this method will be a string, otherwise it will be an ArrayBuffer.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {string | ArrayBuffer} A string or ArrayBuffer containing the serialized representation of the message.\r\n     */\r\n    writeMessage(message: HubMessage): string | ArrayBuffer;\r\n}", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HandshakeProtocol, HandshakeRequestMessage, HandshakeResponseMessage } from \"./HandshakeProtocol\";\r\nimport { IConnection } from \"./IConnection\";\r\nimport { AbortError } from \"./Errors\";\r\nimport { CancelInvocationMessage, CloseMessage, CompletionMessage, IHubProtocol, InvocationMessage, MessageType, StreamInvocationMessage, StreamItemMessage } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { IRetryPolicy } from \"./IRetryPolicy\";\r\nimport { IStreamResult } from \"./Stream\";\r\nimport { Subject } from \"./Subject\";\r\nimport { Arg, getErrorString, Platform } from \"./Utils\";\r\nimport { MessageBuffer } from \"./MessageBuffer\";\r\n\r\nconst DEFAULT_TIMEOUT_IN_MS: number = 30 * 1000;\r\nconst DEFAULT_PING_INTERVAL_IN_MS: number = 15 * 1000;\r\nconst DEFAULT_STATEFUL_RECONNECT_BUFFER_SIZE = 100_000;\r\n\r\n/** Describes the current state of the {@link HubConnection} to the server. */\r\nexport enum HubConnectionState {\r\n    /** The hub connection is disconnected. */\r\n    Disconnected = \"Disconnected\",\r\n    /** The hub connection is connecting. */\r\n    Connecting = \"Connecting\",\r\n    /** The hub connection is connected. */\r\n    Connected = \"Connected\",\r\n    /** The hub connection is disconnecting. */\r\n    Disconnecting = \"Disconnecting\",\r\n    /** The hub connection is reconnecting. */\r\n    Reconnecting = \"Reconnecting\",\r\n}\r\n\r\n/** Represents a connection to a SignalR Hub. */\r\nexport class HubConnection {\r\n    private readonly _cachedPingMessage: string | ArrayBuffer;\r\n    // Needs to not start with _ for tests\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private readonly connection: IConnection;\r\n    private readonly _logger: ILogger;\r\n    private readonly _reconnectPolicy?: IRetryPolicy;\r\n    private readonly _statefulReconnectBufferSize: number;\r\n    private _protocol: IHubProtocol;\r\n    private _handshakeProtocol: HandshakeProtocol;\r\n    private _callbacks: { [invocationId: string]: (invocationEvent: StreamItemMessage | CompletionMessage | null, error?: Error) => void };\r\n    private _methods: { [name: string]: (((...args: any[]) => void) | ((...args: any[]) => any))[] };\r\n    private _invocationId: number;\r\n    private _messageBuffer?: MessageBuffer;\r\n\r\n    private _closedCallbacks: ((error?: Error) => void)[];\r\n    private _reconnectingCallbacks: ((error?: Error) => void)[];\r\n    private _reconnectedCallbacks: ((connectionId?: string) => void)[];\r\n\r\n    private _receivedHandshakeResponse: boolean;\r\n    private _handshakeResolver!: (value?: PromiseLike<{}>) => void;\r\n    private _handshakeRejecter!: (reason?: any) => void;\r\n    private _stopDuringStartError?: Error;\r\n\r\n    private _connectionState: HubConnectionState;\r\n    // connectionStarted is tracked independently from connectionState, so we can check if the\r\n    // connection ever did successfully transition from connecting to connected before disconnecting.\r\n    private _connectionStarted: boolean;\r\n    private _startPromise?: Promise<void>;\r\n    private _stopPromise?: Promise<void>;\r\n    private _nextKeepAlive: number = 0;\r\n\r\n    // The type of these a) doesn't matter and b) varies when building in browser and node contexts\r\n    // Since we're building the WebPack bundle directly from the TypeScript, this matters (previously\r\n    // we built the bundle from the compiled JavaScript).\r\n    private _reconnectDelayHandle?: any;\r\n    private _timeoutHandle?: any;\r\n    private _pingServerHandle?: any;\r\n\r\n    private _freezeEventListener = () =>\r\n    {\r\n        this._logger.log(LogLevel.Warning, \"The page is being frozen, this will likely lead to the connection being closed and messages being lost. For more information see the docs at https://learn.microsoft.com/aspnet/core/signalr/javascript-client#bsleep\");\r\n    };\r\n\r\n    /** The server timeout in milliseconds.\r\n     *\r\n     * If this timeout elapses without receiving any messages from the server, the connection will be terminated with an error.\r\n     * The default timeout value is 30,000 milliseconds (30 seconds).\r\n     */\r\n    public serverTimeoutInMilliseconds: number;\r\n\r\n    /** Default interval at which to ping the server.\r\n     *\r\n     * The default value is 15,000 milliseconds (15 seconds).\r\n     * Allows the server to detect hard disconnects (like when a client unplugs their computer).\r\n     * The ping will happen at most as often as the server pings.\r\n     * If the server pings every 5 seconds, a value lower than 5 will ping every 5 seconds.\r\n     */\r\n    public keepAliveIntervalInMilliseconds: number;\r\n\r\n    /** @internal */\r\n    // Using a public static factory method means we can have a private constructor and an _internal_\r\n    // create method that can be used by HubConnectionBuilder. An \"internal\" constructor would just\r\n    // be stripped away and the '.d.ts' file would have no constructor, which is interpreted as a\r\n    // public parameter-less constructor.\r\n    public static create(\r\n        connection: IConnection,\r\n        logger: ILogger,\r\n        protocol: IHubProtocol,\r\n        reconnectPolicy?: IRetryPolicy,\r\n        serverTimeoutInMilliseconds?: number,\r\n        keepAliveIntervalInMilliseconds?: number,\r\n        statefulReconnectBufferSize?: number): HubConnection {\r\n        return new HubConnection(connection, logger, protocol, reconnectPolicy,\r\n            serverTimeoutInMilliseconds, keepAliveIntervalInMilliseconds, statefulReconnectBufferSize);\r\n    }\r\n\r\n    private constructor(\r\n        connection: IConnection,\r\n        logger: ILogger,\r\n        protocol: IHubProtocol,\r\n        reconnectPolicy?: IRetryPolicy,\r\n        serverTimeoutInMilliseconds?: number,\r\n        keepAliveIntervalInMilliseconds?: number,\r\n        statefulReconnectBufferSize?: number) {\r\n        Arg.isRequired(connection, \"connection\");\r\n        Arg.isRequired(logger, \"logger\");\r\n        Arg.isRequired(protocol, \"protocol\");\r\n\r\n        this.serverTimeoutInMilliseconds = serverTimeoutInMilliseconds ?? DEFAULT_TIMEOUT_IN_MS;\r\n        this.keepAliveIntervalInMilliseconds = keepAliveIntervalInMilliseconds ?? DEFAULT_PING_INTERVAL_IN_MS;\r\n\r\n        this._statefulReconnectBufferSize = statefulReconnectBufferSize ?? DEFAULT_STATEFUL_RECONNECT_BUFFER_SIZE;\r\n\r\n        this._logger = logger;\r\n        this._protocol = protocol;\r\n        this.connection = connection;\r\n        this._reconnectPolicy = reconnectPolicy;\r\n        this._handshakeProtocol = new HandshakeProtocol();\r\n\r\n        this.connection.onreceive = (data: any) => this._processIncomingData(data);\r\n        this.connection.onclose = (error?: Error) => this._connectionClosed(error);\r\n\r\n        this._callbacks = {};\r\n        this._methods = {};\r\n        this._closedCallbacks = [];\r\n        this._reconnectingCallbacks = [];\r\n        this._reconnectedCallbacks = [];\r\n        this._invocationId = 0;\r\n        this._receivedHandshakeResponse = false;\r\n        this._connectionState = HubConnectionState.Disconnected;\r\n        this._connectionStarted = false;\r\n\r\n        this._cachedPingMessage = this._protocol.writeMessage({ type: MessageType.Ping });\r\n    }\r\n\r\n    /** Indicates the state of the {@link HubConnection} to the server. */\r\n    get state(): HubConnectionState {\r\n        return this._connectionState;\r\n    }\r\n\r\n    /** Represents the connection id of the {@link HubConnection} on the server. The connection id will be null when the connection is either\r\n     *  in the disconnected state or if the negotiation step was skipped.\r\n     */\r\n    get connectionId(): string | null {\r\n        return this.connection ? (this.connection.connectionId || null) : null;\r\n    }\r\n\r\n    /** Indicates the url of the {@link HubConnection} to the server. */\r\n    get baseUrl(): string {\r\n        return this.connection.baseUrl || \"\";\r\n    }\r\n\r\n    /**\r\n     * Sets a new url for the HubConnection. Note that the url can only be changed when the connection is in either the Disconnected or\r\n     * Reconnecting states.\r\n     * @param {string} url The url to connect to.\r\n     */\r\n    set baseUrl(url: string) {\r\n        if (this._connectionState !== HubConnectionState.Disconnected && this._connectionState !== HubConnectionState.Reconnecting) {\r\n            throw new Error(\"The HubConnection must be in the Disconnected or Reconnecting state to change the url.\");\r\n        }\r\n\r\n        if (!url) {\r\n            throw new Error(\"The HubConnection url must be a valid url.\");\r\n        }\r\n\r\n        this.connection.baseUrl = url;\r\n    }\r\n\r\n    /** Starts the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully established, or rejects with an error.\r\n     */\r\n    public start(): Promise<void> {\r\n        this._startPromise = this._startWithStateTransitions();\r\n        return this._startPromise;\r\n    }\r\n\r\n    private async _startWithStateTransitions(): Promise<void> {\r\n        if (this._connectionState !== HubConnectionState.Disconnected) {\r\n            return Promise.reject(new Error(\"Cannot start a HubConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n\r\n        this._connectionState = HubConnectionState.Connecting;\r\n        this._logger.log(LogLevel.Debug, \"Starting HubConnection.\");\r\n\r\n        try {\r\n            await this._startInternal();\r\n\r\n            if (Platform.isBrowser) {\r\n                // Log when the browser freezes the tab so users know why their connection unexpectedly stopped working\r\n                window.document.addEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n\r\n            this._connectionState = HubConnectionState.Connected;\r\n            this._connectionStarted = true;\r\n            this._logger.log(LogLevel.Debug, \"HubConnection connected successfully.\");\r\n        } catch (e) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._logger.log(LogLevel.Debug, `HubConnection failed to start successfully because of error '${e}'.`);\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n\r\n    private async _startInternal() {\r\n        this._stopDuringStartError = undefined;\r\n        this._receivedHandshakeResponse = false;\r\n        // Set up the promise before any connection is (re)started otherwise it could race with received messages\r\n        const handshakePromise = new Promise((resolve, reject) => {\r\n            this._handshakeResolver = resolve;\r\n            this._handshakeRejecter = reject;\r\n        });\r\n\r\n        await this.connection.start(this._protocol.transferFormat);\r\n\r\n        try {\r\n            let version = this._protocol.version;\r\n            if (!this.connection.features.reconnect) {\r\n                // Stateful Reconnect starts with HubProtocol version 2, newer clients connecting to older servers will fail to connect due to\r\n                // the handshake only supporting version 1, so we will try to send version 1 during the handshake to keep old servers working.\r\n                version = 1;\r\n            }\r\n\r\n            const handshakeRequest: HandshakeRequestMessage = {\r\n                protocol: this._protocol.name,\r\n                version,\r\n            };\r\n\r\n            this._logger.log(LogLevel.Debug, \"Sending handshake request.\");\r\n\r\n            await this._sendMessage(this._handshakeProtocol.writeHandshakeRequest(handshakeRequest));\r\n\r\n            this._logger.log(LogLevel.Information, `Using HubProtocol '${this._protocol.name}'.`);\r\n\r\n            // defensively cleanup timeout in case we receive a message from the server before we finish start\r\n            this._cleanupTimeout();\r\n            this._resetTimeoutPeriod();\r\n            this._resetKeepAliveInterval();\r\n\r\n            await handshakePromise;\r\n\r\n            // It's important to check the stopDuringStartError instead of just relying on the handshakePromise\r\n            // being rejected on close, because this continuation can run after both the handshake completed successfully\r\n            // and the connection was closed.\r\n            if (this._stopDuringStartError) {\r\n                // It's important to throw instead of returning a rejected promise, because we don't want to allow any state\r\n                // transitions to occur between now and the calling code observing the exceptions. Returning a rejected promise\r\n                // will cause the calling continuation to get scheduled to run later.\r\n                // eslint-disable-next-line @typescript-eslint/no-throw-literal\r\n                throw this._stopDuringStartError;\r\n            }\r\n\r\n            const useStatefulReconnect = this.connection.features.reconnect || false;\r\n            if (useStatefulReconnect) {\r\n                this._messageBuffer = new MessageBuffer(this._protocol, this.connection, this._statefulReconnectBufferSize);\r\n                this.connection.features.disconnected = this._messageBuffer._disconnected.bind(this._messageBuffer);\r\n                this.connection.features.resend = () => {\r\n                    if (this._messageBuffer) {\r\n                        return this._messageBuffer._resend();\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (!this.connection.features.inherentKeepAlive) {\r\n                await this._sendMessage(this._cachedPingMessage);\r\n            }\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Debug, `Hub handshake failed with error '${e}' during start(). Stopping HubConnection.`);\r\n\r\n            this._cleanupTimeout();\r\n            this._cleanupPingTimer();\r\n\r\n            // HttpConnection.stop() should not complete until after the onclose callback is invoked.\r\n            // This will transition the HubConnection to the disconnected state before HttpConnection.stop() completes.\r\n            await this.connection.stop(e);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /** Stops the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully terminated, or rejects with an error.\r\n     */\r\n    public async stop(): Promise<void> {\r\n        // Capture the start promise before the connection might be restarted in an onclose callback.\r\n        const startPromise = this._startPromise;\r\n        this.connection.features.reconnect = false;\r\n\r\n        this._stopPromise = this._stopInternal();\r\n        await this._stopPromise;\r\n\r\n        try {\r\n            // Awaiting undefined continues immediately\r\n            await startPromise;\r\n        } catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n    }\r\n\r\n    private _stopInternal(error?: Error): Promise<void> {\r\n        if (this._connectionState === HubConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HubConnection.stop(${error}) ignored because it is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise!;\r\n        }\r\n\r\n        const state = this._connectionState;\r\n        this._connectionState = HubConnectionState.Disconnecting;\r\n\r\n        this._logger.log(LogLevel.Debug, \"Stopping HubConnection.\");\r\n\r\n        if (this._reconnectDelayHandle) {\r\n            // We're in a reconnect delay which means the underlying connection is currently already stopped.\r\n            // Just clear the handle to stop the reconnect loop (which no one is waiting on thankfully) and\r\n            // fire the onclose callbacks.\r\n            this._logger.log(LogLevel.Debug, \"Connection stopped during reconnect delay. Done reconnecting.\");\r\n\r\n            clearTimeout(this._reconnectDelayHandle);\r\n            this._reconnectDelayHandle = undefined;\r\n\r\n            this._completeClose();\r\n            return Promise.resolve();\r\n        }\r\n\r\n        if (state === HubConnectionState.Connected) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._sendCloseMessage();\r\n        }\r\n\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n        this._stopDuringStartError = error || new AbortError(\"The connection was stopped before the hub handshake could complete.\");\r\n\r\n        // HttpConnection.stop() should not complete until after either HttpConnection.start() fails\r\n        // or the onclose callback is invoked. The onclose callback will transition the HubConnection\r\n        // to the disconnected state if need be before HttpConnection.stop() completes.\r\n        return this.connection.stop(error);\r\n    }\r\n\r\n    private async _sendCloseMessage() {\r\n        try {\r\n            await this._sendWithProtocol(this._createCloseMessage());\r\n        } catch {\r\n            // Ignore, this is a best effort attempt to let the server know the client closed gracefully.\r\n        }\r\n    }\r\n\r\n    /** Invokes a streaming hub method on the server using the specified name and arguments.\r\n     *\r\n     * @typeparam T The type of the items returned by the server.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {IStreamResult<T>} An object that yields results from the server as they are received.\r\n     */\r\n    public stream<T = any>(methodName: string, ...args: any[]): IStreamResult<T> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createStreamInvocation(methodName, args, streamIds);\r\n\r\n        // eslint-disable-next-line prefer-const\r\n        let promiseQueue: Promise<void>;\r\n\r\n        const subject = new Subject<T>();\r\n        subject.cancelCallback = () => {\r\n            const cancelInvocation: CancelInvocationMessage = this._createCancelInvocation(invocationDescriptor.invocationId);\r\n\r\n            delete this._callbacks[invocationDescriptor.invocationId];\r\n\r\n            return promiseQueue.then(() => {\r\n                return this._sendWithProtocol(cancelInvocation);\r\n            });\r\n        };\r\n\r\n        this._callbacks[invocationDescriptor.invocationId] = (invocationEvent: CompletionMessage | StreamItemMessage | null, error?: Error) => {\r\n            if (error) {\r\n                subject.error(error);\r\n                return;\r\n            } else if (invocationEvent) {\r\n                // invocationEvent will not be null when an error is not passed to the callback\r\n                if (invocationEvent.type === MessageType.Completion) {\r\n                    if (invocationEvent.error) {\r\n                        subject.error(new Error(invocationEvent.error));\r\n                    } else {\r\n                        subject.complete();\r\n                    }\r\n                } else {\r\n                    subject.next((invocationEvent.item) as T);\r\n                }\r\n            }\r\n        };\r\n\r\n        promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n            .catch((e) => {\r\n                subject.error(e);\r\n                delete this._callbacks[invocationDescriptor.invocationId];\r\n            });\r\n\r\n        this._launchStreams(streams, promiseQueue);\r\n\r\n        return subject;\r\n    }\r\n\r\n    private _sendMessage(message: any) {\r\n        this._resetKeepAliveInterval();\r\n        return this.connection.send(message);\r\n    }\r\n\r\n    /**\r\n     * Sends a js object to the server.\r\n     * @param message The js object to serialize and send.\r\n     */\r\n    private _sendWithProtocol(message: any) {\r\n        if (this._messageBuffer) {\r\n            return this._messageBuffer._send(message);\r\n        } else {\r\n            return this._sendMessage(this._protocol.writeMessage(message));\r\n        }\r\n    }\r\n\r\n    /** Invokes a hub method on the server using the specified name and arguments. Does not wait for a response from the receiver.\r\n     *\r\n     * The Promise returned by this method resolves when the client has sent the invocation to the server. The server may still\r\n     * be processing the invocation.\r\n     *\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<void>} A Promise that resolves when the invocation has been successfully sent, or rejects with an error.\r\n     */\r\n    public send(methodName: string, ...args: any[]): Promise<void> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const sendPromise = this._sendWithProtocol(this._createInvocation(methodName, args, true, streamIds));\r\n\r\n        this._launchStreams(streams, sendPromise);\r\n\r\n        return sendPromise;\r\n    }\r\n\r\n    /** Invokes a hub method on the server using the specified name and arguments.\r\n     *\r\n     * The Promise returned by this method resolves when the server indicates it has finished invoking the method. When the promise\r\n     * resolves, the server has finished invoking the method. If the server method returns a result, it is produced as the result of\r\n     * resolving the Promise.\r\n     *\r\n     * @typeparam T The expected return type.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<T>} A Promise that resolves with the result of the server method (if any), or rejects with an error.\r\n     */\r\n    public invoke<T = any>(methodName: string, ...args: any[]): Promise<T> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createInvocation(methodName, args, false, streamIds);\r\n\r\n        const p = new Promise<any>((resolve, reject) => {\r\n            // invocationId will always have a value for a non-blocking invocation\r\n            this._callbacks[invocationDescriptor.invocationId!] = (invocationEvent: StreamItemMessage | CompletionMessage | null, error?: Error) => {\r\n                if (error) {\r\n                    reject(error);\r\n                    return;\r\n                } else if (invocationEvent) {\r\n                    // invocationEvent will not be null when an error is not passed to the callback\r\n                    if (invocationEvent.type === MessageType.Completion) {\r\n                        if (invocationEvent.error) {\r\n                            reject(new Error(invocationEvent.error));\r\n                        } else {\r\n                            resolve(invocationEvent.result);\r\n                        }\r\n                    } else {\r\n                        reject(new Error(`Unexpected message type: ${invocationEvent.type}`));\r\n                    }\r\n                }\r\n            };\r\n\r\n            const promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n                .catch((e) => {\r\n                    reject(e);\r\n                    // invocationId will always have a value for a non-blocking invocation\r\n                    delete this._callbacks[invocationDescriptor.invocationId!];\r\n                });\r\n\r\n            this._launchStreams(streams, promiseQueue);\r\n        });\r\n\r\n        return p;\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the hub method with the specified method name is invoked.\r\n     *\r\n     * @param {string} methodName The name of the hub method to define.\r\n     * @param {Function} newMethod The handler that will be raised when the hub method is invoked.\r\n     */\r\n    public on(methodName: string, newMethod: (...args: any[]) => any): void\r\n    public on(methodName: string, newMethod: (...args: any[]) => void): void {\r\n        if (!methodName || !newMethod) {\r\n            return;\r\n        }\r\n\r\n        methodName = methodName.toLowerCase();\r\n        if (!this._methods[methodName]) {\r\n            this._methods[methodName] = [];\r\n        }\r\n\r\n        // Preventing adding the same handler multiple times.\r\n        if (this._methods[methodName].indexOf(newMethod) !== -1) {\r\n            return;\r\n        }\r\n\r\n        this._methods[methodName].push(newMethod);\r\n    }\r\n\r\n    /** Removes all handlers for the specified hub method.\r\n     *\r\n     * @param {string} methodName The name of the method to remove handlers for.\r\n     */\r\n    public off(methodName: string): void;\r\n\r\n    /** Removes the specified handler for the specified hub method.\r\n     *\r\n     * You must pass the exact same Function instance as was previously passed to {@link @microsoft/signalr.HubConnection.on}. Passing a different instance (even if the function\r\n     * body is the same) will not remove the handler.\r\n     *\r\n     * @param {string} methodName The name of the method to remove handlers for.\r\n     * @param {Function} method The handler to remove. This must be the same Function instance as the one passed to {@link @microsoft/signalr.HubConnection.on}.\r\n     */\r\n    public off(methodName: string, method: (...args: any[]) => void): void;\r\n    public off(methodName: string, method?: (...args: any[]) => void): void {\r\n        if (!methodName) {\r\n            return;\r\n        }\r\n\r\n        methodName = methodName.toLowerCase();\r\n        const handlers = this._methods[methodName];\r\n        if (!handlers) {\r\n            return;\r\n        }\r\n        if (method) {\r\n            const removeIdx = handlers.indexOf(method);\r\n            if (removeIdx !== -1) {\r\n                handlers.splice(removeIdx, 1);\r\n                if (handlers.length === 0) {\r\n                    delete this._methods[methodName];\r\n                }\r\n            }\r\n        } else {\r\n            delete this._methods[methodName];\r\n        }\r\n\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection is closed.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection is closed. Optionally receives a single argument containing the error that caused the connection to close (if any).\r\n     */\r\n    public onclose(callback: (error?: Error) => void): void {\r\n        if (callback) {\r\n            this._closedCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection starts reconnecting.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection starts reconnecting. Optionally receives a single argument containing the error that caused the connection to start reconnecting (if any).\r\n     */\r\n    public onreconnecting(callback: (error?: Error) => void): void {\r\n        if (callback) {\r\n            this._reconnectingCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection successfully reconnects.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection successfully reconnects.\r\n     */\r\n    public onreconnected(callback: (connectionId?: string) => void): void {\r\n        if (callback) {\r\n            this._reconnectedCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    private _processIncomingData(data: any) {\r\n        this._cleanupTimeout();\r\n\r\n        if (!this._receivedHandshakeResponse) {\r\n            data = this._processHandshakeResponse(data);\r\n            this._receivedHandshakeResponse = true;\r\n        }\r\n\r\n        // Data may have all been read when processing handshake response\r\n        if (data) {\r\n            // Parse the messages\r\n            const messages = this._protocol.parseMessages(data, this._logger);\r\n\r\n            for (const message of messages) {\r\n                if (this._messageBuffer && !this._messageBuffer._shouldProcessMessage(message)) {\r\n                    // Don't process the message, we are either waiting for a SequenceMessage or received a duplicate message\r\n                    continue;\r\n                }\r\n\r\n                switch (message.type) {\r\n                    case MessageType.Invocation:\r\n                        this._invokeClientMethod(message)\r\n                            .catch((e) => {\r\n                                this._logger.log(LogLevel.Error, `Invoke client method threw error: ${getErrorString(e)}`)\r\n                            });\r\n                        break;\r\n                    case MessageType.StreamItem:\r\n                    case MessageType.Completion: {\r\n                        const callback = this._callbacks[message.invocationId];\r\n                        if (callback) {\r\n                            if (message.type === MessageType.Completion) {\r\n                                delete this._callbacks[message.invocationId];\r\n                            }\r\n                            try {\r\n                                callback(message);\r\n                            } catch (e) {\r\n                                this._logger.log(LogLevel.Error, `Stream callback threw error: ${getErrorString(e)}`);\r\n                            }\r\n                        }\r\n                        break;\r\n                    }\r\n                    case MessageType.Ping:\r\n                        // Don't care about pings\r\n                        break;\r\n                    case MessageType.Close: {\r\n                        this._logger.log(LogLevel.Information, \"Close message received from server.\");\r\n\r\n                        const error = message.error ? new Error(\"Server returned an error on close: \" + message.error) : undefined;\r\n\r\n                        if (message.allowReconnect === true) {\r\n                            // It feels wrong not to await connection.stop() here, but processIncomingData is called as part of an onreceive callback which is not async,\r\n                            // this is already the behavior for serverTimeout(), and HttpConnection.Stop() should catch and log all possible exceptions.\r\n\r\n                            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n                            this.connection.stop(error);\r\n                        } else {\r\n                            // We cannot await stopInternal() here, but subsequent calls to stop() will await this if stopInternal() is still ongoing.\r\n                            this._stopPromise = this._stopInternal(error);\r\n                        }\r\n\r\n                        break;\r\n                    }\r\n                    case MessageType.Ack:\r\n                        if (this._messageBuffer) {\r\n                            this._messageBuffer._ack(message);\r\n                        }\r\n                        break;\r\n                    case MessageType.Sequence:\r\n                        if (this._messageBuffer) {\r\n                            this._messageBuffer._resetSequence(message);\r\n                        }\r\n                        break;\r\n                    default:\r\n                        this._logger.log(LogLevel.Warning, `Invalid message type: ${message.type}.`);\r\n                        break;\r\n                }\r\n            }\r\n        }\r\n\r\n        this._resetTimeoutPeriod();\r\n    }\r\n\r\n    private _processHandshakeResponse(data: any): any {\r\n        let responseMessage: HandshakeResponseMessage;\r\n        let remainingData: any;\r\n\r\n        try {\r\n            [remainingData, responseMessage] = this._handshakeProtocol.parseHandshakeResponse(data);\r\n        } catch (e) {\r\n            const message = \"Error parsing handshake response: \" + e;\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        }\r\n        if (responseMessage.error) {\r\n            const message = \"Server returned handshake error: \" + responseMessage.error;\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        } else {\r\n            this._logger.log(LogLevel.Debug, \"Server handshake complete.\");\r\n        }\r\n\r\n        this._handshakeResolver();\r\n        return remainingData;\r\n    }\r\n\r\n    private _resetKeepAliveInterval() {\r\n        if (this.connection.features.inherentKeepAlive) {\r\n            return;\r\n        }\r\n\r\n        // Set the time we want the next keep alive to be sent\r\n        // Timer will be setup on next message receive\r\n        this._nextKeepAlive = new Date().getTime() + this.keepAliveIntervalInMilliseconds;\r\n\r\n        this._cleanupPingTimer();\r\n    }\r\n\r\n    private _resetTimeoutPeriod() {\r\n        if (!this.connection.features || !this.connection.features.inherentKeepAlive) {\r\n            // Set the timeout timer\r\n            this._timeoutHandle = setTimeout(() => this.serverTimeout(), this.serverTimeoutInMilliseconds);\r\n\r\n            // Set keepAlive timer if there isn't one\r\n            if (this._pingServerHandle === undefined)\r\n            {\r\n                let nextPing = this._nextKeepAlive - new Date().getTime();\r\n                if (nextPing < 0) {\r\n                    nextPing = 0;\r\n                }\r\n\r\n                // The timer needs to be set from a networking callback to avoid Chrome timer throttling from causing timers to run once a minute\r\n                this._pingServerHandle = setTimeout(async () => {\r\n                    if (this._connectionState === HubConnectionState.Connected) {\r\n                        try {\r\n                            await this._sendMessage(this._cachedPingMessage);\r\n                        } catch {\r\n                            // We don't care about the error. It should be seen elsewhere in the client.\r\n                            // The connection is probably in a bad or closed state now, cleanup the timer so it stops triggering\r\n                            this._cleanupPingTimer();\r\n                        }\r\n                    }\r\n                }, nextPing);\r\n            }\r\n        }\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private serverTimeout() {\r\n        // The server hasn't talked to us in a while. It doesn't like us anymore ... :(\r\n        // Terminate the connection, but we don't need to wait on the promise. This could trigger reconnecting.\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this.connection.stop(new Error(\"Server timeout elapsed without receiving a message from the server.\"));\r\n    }\r\n\r\n    private async _invokeClientMethod(invocationMessage: InvocationMessage) {\r\n        const methodName = invocationMessage.target.toLowerCase();\r\n        const methods = this._methods[methodName];\r\n        if (!methods) {\r\n            this._logger.log(LogLevel.Warning, `No client method with the name '${methodName}' found.`);\r\n\r\n            // No handlers provided by client but the server is expecting a response still, so we send an error\r\n            if (invocationMessage.invocationId) {\r\n                this._logger.log(LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\r\n                await this._sendWithProtocol(this._createCompletionMessage(invocationMessage.invocationId, \"Client didn't provide a result.\", null));\r\n            }\r\n            return;\r\n        }\r\n\r\n        // Avoid issues with handlers removing themselves thus modifying the list while iterating through it\r\n        const methodsCopy = methods.slice();\r\n\r\n        // Server expects a response\r\n        const expectsResponse = invocationMessage.invocationId ? true : false;\r\n        // We preserve the last result or exception but still call all handlers\r\n        let res;\r\n        let exception;\r\n        let completionMessage;\r\n        for (const m of methodsCopy) {\r\n            try {\r\n                const prevRes = res;\r\n                res = await m.apply(this, invocationMessage.arguments);\r\n                if (expectsResponse && res && prevRes) {\r\n                    this._logger.log(LogLevel.Error, `Multiple results provided for '${methodName}'. Sending error to server.`);\r\n                    completionMessage = this._createCompletionMessage(invocationMessage.invocationId!, `Client provided multiple results.`, null);\r\n                }\r\n                // Ignore exception if we got a result after, the exception will be logged\r\n                exception = undefined;\r\n            } catch (e) {\r\n                exception = e;\r\n                this._logger.log(LogLevel.Error, `A callback for the method '${methodName}' threw error '${e}'.`);\r\n            }\r\n        }\r\n        if (completionMessage) {\r\n            await this._sendWithProtocol(completionMessage);\r\n        } else if (expectsResponse) {\r\n            // If there is an exception that means either no result was given or a handler after a result threw\r\n            if (exception) {\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId!, `${exception}`, null);\r\n            } else if (res !== undefined) {\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId!, null, res);\r\n            } else {\r\n                this._logger.log(LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\r\n                // Client didn't provide a result or throw from a handler, server expects a response so we send an error\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId!, \"Client didn't provide a result.\", null);\r\n            }\r\n            await this._sendWithProtocol(completionMessage);\r\n        } else {\r\n            if (res) {\r\n                this._logger.log(LogLevel.Error, `Result given for '${methodName}' method but server is not expecting a result.`);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _connectionClosed(error?: Error) {\r\n        this._logger.log(LogLevel.Debug, `HubConnection.connectionClosed(${error}) called while in state ${this._connectionState}.`);\r\n\r\n        // Triggering this.handshakeRejecter is insufficient because it could already be resolved without the continuation having run yet.\r\n        this._stopDuringStartError = this._stopDuringStartError || error || new AbortError(\"The underlying connection was closed before the hub handshake could complete.\");\r\n\r\n        // If the handshake is in progress, start will be waiting for the handshake promise, so we complete it.\r\n        // If it has already completed, this should just noop.\r\n        if (this._handshakeResolver) {\r\n            this._handshakeResolver();\r\n        }\r\n\r\n        this._cancelCallbacksWithError(error || new Error(\"Invocation canceled due to the underlying connection being closed.\"));\r\n\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._completeClose(error);\r\n        } else if (this._connectionState === HubConnectionState.Connected && this._reconnectPolicy) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._reconnect(error);\r\n        } else if (this._connectionState === HubConnectionState.Connected) {\r\n            this._completeClose(error);\r\n        }\r\n\r\n        // If none of the above if conditions were true were called the HubConnection must be in either:\r\n        // 1. The Connecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail it.\r\n        // 2. The Reconnecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail the current reconnect attempt\r\n        //    and potentially continue the reconnect() loop.\r\n        // 3. The Disconnected state in which case we're already done.\r\n    }\r\n\r\n    private _completeClose(error?: Error) {\r\n        if (this._connectionStarted) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._connectionStarted = false;\r\n            if (this._messageBuffer) {\r\n                this._messageBuffer._dispose(error ?? new Error(\"Connection closed.\"));\r\n                this._messageBuffer = undefined;\r\n            }\r\n\r\n            if (Platform.isBrowser) {\r\n                window.document.removeEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n\r\n            try {\r\n                this._closedCallbacks.forEach((c) => c.apply(this, [error]));\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `An onclose callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n\r\n    private async _reconnect(error?: Error) {\r\n        const reconnectStartTime = Date.now();\r\n        let previousReconnectAttempts = 0;\r\n        let retryError = error !== undefined ? error : new Error(\"Attempting to reconnect due to a unknown error.\");\r\n\r\n        let nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, 0, retryError);\r\n\r\n        if (nextRetryDelay === null) {\r\n            this._logger.log(LogLevel.Debug, \"Connection not reconnecting because the IRetryPolicy returned null on the first reconnect attempt.\");\r\n            this._completeClose(error);\r\n            return;\r\n        }\r\n\r\n        this._connectionState = HubConnectionState.Reconnecting;\r\n\r\n        if (error) {\r\n            this._logger.log(LogLevel.Information, `Connection reconnecting because of error '${error}'.`);\r\n        } else {\r\n            this._logger.log(LogLevel.Information, \"Connection reconnecting.\");\r\n        }\r\n\r\n        if (this._reconnectingCallbacks.length !== 0) {\r\n            try {\r\n                this._reconnectingCallbacks.forEach((c) => c.apply(this, [error]));\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `An onreconnecting callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n\r\n            // Exit early if an onreconnecting callback called connection.stop().\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state in onreconnecting callback. Done reconnecting.\");\r\n                return;\r\n            }\r\n        }\r\n\r\n        while (nextRetryDelay !== null) {\r\n            this._logger.log(LogLevel.Information, `Reconnect attempt number ${previousReconnectAttempts} will start in ${nextRetryDelay} ms.`);\r\n\r\n            await new Promise((resolve) => {\r\n                this._reconnectDelayHandle = setTimeout(resolve, nextRetryDelay!);\r\n            });\r\n            this._reconnectDelayHandle = undefined;\r\n\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state during reconnect delay. Done reconnecting.\");\r\n                return;\r\n            }\r\n\r\n            try {\r\n                await this._startInternal();\r\n\r\n                this._connectionState = HubConnectionState.Connected;\r\n                this._logger.log(LogLevel.Information, \"HubConnection reconnected successfully.\");\r\n\r\n                if (this._reconnectedCallbacks.length !== 0) {\r\n                    try {\r\n                        this._reconnectedCallbacks.forEach((c) => c.apply(this, [this.connection.connectionId]));\r\n                    } catch (e) {\r\n                        this._logger.log(LogLevel.Error, `An onreconnected callback called with connectionId '${this.connection.connectionId}; threw error '${e}'.`);\r\n                    }\r\n                }\r\n\r\n                return;\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Information, `Reconnect attempt failed because of error '${e}'.`);\r\n\r\n                if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                    this._logger.log(LogLevel.Debug, `Connection moved to the '${this._connectionState}' from the reconnecting state during reconnect attempt. Done reconnecting.`);\r\n                    // The TypeScript compiler thinks that connectionState must be Connected here. The TypeScript compiler is wrong.\r\n                    if (this._connectionState as any === HubConnectionState.Disconnecting) {\r\n                        this._completeClose();\r\n                    }\r\n                    return;\r\n                }\r\n\r\n                retryError = e instanceof Error ? e : new Error((e as any).toString());\r\n                nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, Date.now() - reconnectStartTime, retryError);\r\n            }\r\n        }\r\n\r\n        this._logger.log(LogLevel.Information, `Reconnect retries have been exhausted after ${Date.now() - reconnectStartTime} ms and ${previousReconnectAttempts} failed attempts. Connection disconnecting.`);\r\n\r\n        this._completeClose();\r\n    }\r\n\r\n    private _getNextRetryDelay(previousRetryCount: number, elapsedMilliseconds: number, retryReason: Error) {\r\n        try {\r\n            return this._reconnectPolicy!.nextRetryDelayInMilliseconds({\r\n                elapsedMilliseconds,\r\n                previousRetryCount,\r\n                retryReason,\r\n            });\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Error, `IRetryPolicy.nextRetryDelayInMilliseconds(${previousRetryCount}, ${elapsedMilliseconds}) threw error '${e}'.`);\r\n            return null;\r\n        }\r\n    }\r\n\r\n    private _cancelCallbacksWithError(error: Error) {\r\n        const callbacks = this._callbacks;\r\n        this._callbacks = {};\r\n\r\n        Object.keys(callbacks)\r\n            .forEach((key) => {\r\n                const callback = callbacks[key];\r\n                try {\r\n                    callback(null, error);\r\n                } catch (e) {\r\n                    this._logger.log(LogLevel.Error, `Stream 'error' callback called with '${error}' threw error: ${getErrorString(e)}`);\r\n                }\r\n            });\r\n    }\r\n\r\n    private _cleanupPingTimer(): void {\r\n        if (this._pingServerHandle) {\r\n            clearTimeout(this._pingServerHandle);\r\n            this._pingServerHandle = undefined;\r\n        }\r\n    }\r\n\r\n    private _cleanupTimeout(): void {\r\n        if (this._timeoutHandle) {\r\n            clearTimeout(this._timeoutHandle);\r\n        }\r\n    }\r\n\r\n    private _createInvocation(methodName: string, args: any[], nonblocking: boolean, streamIds: string[]): InvocationMessage {\r\n        if (nonblocking) {\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            } else {\r\n                return {\r\n                    arguments: args,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n        } else {\r\n            const invocationId = this._invocationId;\r\n            this._invocationId++;\r\n\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            } else {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n        }\r\n    }\r\n\r\n    private _launchStreams(streams: IStreamResult<any>[], promiseQueue: Promise<void>): void {\r\n        if (streams.length === 0) {\r\n            return;\r\n        }\r\n\r\n        // Synchronize stream data so they arrive in-order on the server\r\n        if (!promiseQueue) {\r\n            promiseQueue = Promise.resolve();\r\n        }\r\n\r\n        // We want to iterate over the keys, since the keys are the stream ids\r\n        // eslint-disable-next-line guard-for-in\r\n        for (const streamId in streams) {\r\n            streams[streamId].subscribe({\r\n                complete: () => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId)));\r\n                },\r\n                error: (err) => {\r\n                    let message: string;\r\n                    if (err instanceof Error) {\r\n                        message = err.message;\r\n                    } else if (err && err.toString) {\r\n                        message = err.toString();\r\n                    } else {\r\n                        message = \"Unknown error\";\r\n                    }\r\n\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId, message)));\r\n                },\r\n                next: (item) => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createStreamItemMessage(streamId, item)));\r\n                },\r\n            });\r\n        }\r\n    }\r\n\r\n    private _replaceStreamingParams(args: any[]): [IStreamResult<any>[], string[]] {\r\n        const streams: IStreamResult<any>[] = [];\r\n        const streamIds: string[] = [];\r\n        for (let i = 0; i < args.length; i++) {\r\n            const argument = args[i];\r\n            if (this._isObservable(argument)) {\r\n                const streamId = this._invocationId;\r\n                this._invocationId++;\r\n                // Store the stream for later use\r\n                streams[streamId] = argument;\r\n                streamIds.push(streamId.toString());\r\n\r\n                // remove stream from args\r\n                args.splice(i, 1);\r\n            }\r\n        }\r\n\r\n        return [streams, streamIds];\r\n    }\r\n\r\n    private _isObservable(arg: any): arg is IStreamResult<any> {\r\n        // This allows other stream implementations to just work (like rxjs)\r\n        return arg && arg.subscribe && typeof arg.subscribe === \"function\";\r\n    }\r\n\r\n    private _createStreamInvocation(methodName: string, args: any[], streamIds: string[]): StreamInvocationMessage {\r\n        const invocationId = this._invocationId;\r\n        this._invocationId++;\r\n\r\n        if (streamIds.length !== 0) {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                streamIds,\r\n                target: methodName,\r\n                type: MessageType.StreamInvocation,\r\n            };\r\n        } else {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                target: methodName,\r\n                type: MessageType.StreamInvocation,\r\n            };\r\n        }\r\n    }\r\n\r\n    private _createCancelInvocation(id: string): CancelInvocationMessage {\r\n        return {\r\n            invocationId: id,\r\n            type: MessageType.CancelInvocation,\r\n        };\r\n    }\r\n\r\n    private _createStreamItemMessage(id: string, item: any): StreamItemMessage {\r\n        return {\r\n            invocationId: id,\r\n            item,\r\n            type: MessageType.StreamItem,\r\n        };\r\n    }\r\n\r\n    private _createCompletionMessage(id: string, error?: any, result?: any): CompletionMessage {\r\n        if (error) {\r\n            return {\r\n                error,\r\n                invocationId: id,\r\n                type: MessageType.Completion,\r\n            };\r\n        }\r\n\r\n        return {\r\n            invocationId: id,\r\n            result,\r\n            type: MessageType.Completion,\r\n        };\r\n    }\r\n\r\n    private _createCloseMessage(): CloseMessage {\r\n        return { type: MessageType.Close };\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { IStreamResult, IStreamSubscriber, ISubscription } from \"./Stream\";\r\nimport { SubjectSubscription } from \"./Utils\";\r\n\r\n/** Stream implementation to stream items to the server. */\r\nexport class Subject<T> implements IStreamResult<T> {\r\n    /** @internal */\r\n    public observers: IStreamSubscriber<T>[];\r\n\r\n    /** @internal */\r\n    public cancelCallback?: () => Promise<void>;\r\n\r\n    constructor() {\r\n        this.observers = [];\r\n    }\r\n\r\n    public next(item: T): void {\r\n        for (const observer of this.observers) {\r\n            observer.next(item);\r\n        }\r\n    }\r\n\r\n    public error(err: any): void {\r\n        for (const observer of this.observers) {\r\n            if (observer.error) {\r\n                observer.error(err);\r\n            }\r\n        }\r\n    }\r\n\r\n    public complete(): void {\r\n        for (const observer of this.observers) {\r\n            if (observer.complete) {\r\n                observer.complete();\r\n            }\r\n        }\r\n    }\r\n\r\n    public subscribe(observer: IStreamSubscriber<T>): ISubscription<T> {\r\n        this.observers.push(observer);\r\n        return new SubjectSubscription(this, observer);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { IConnection } from \"./IConnection\";\r\nimport { AckMessage, HubMessage, IHubProtocol, MessageType, SequenceMessage } from \"./IHubProtocol\";\r\nimport { is<PERSON><PERSON><PERSON><PERSON>uffer } from \"./Utils\";\r\n\r\n/** @private */\r\nexport class MessageBuffer {\r\n    private readonly _protocol: IHubProtocol;\r\n    private readonly _connection: IConnection;\r\n\r\n    private readonly _bufferSize: number = 100_000;\r\n\r\n    private _messages: BufferedItem[] = [];\r\n    private _totalMessageCount: number = 0;\r\n    private _waitForSequenceMessage: boolean = false;\r\n\r\n    // Message IDs start at 1 and always increment by 1\r\n    private _nextReceivingSequenceId = 1;\r\n    private _latestReceivedSequenceId = 0;\r\n    private _bufferedByteCount: number = 0;\r\n    private _reconnectInProgress: boolean = false;\r\n\r\n    private _ackTimerHandle?: any;\r\n\r\n    constructor(protocol: IHubProtocol, connection: IConnection, bufferSize: number) {\r\n        this._protocol = protocol;\r\n        this._connection = connection;\r\n        this._bufferSize = bufferSize;\r\n    }\r\n\r\n    public async _send(message: HubMessage): Promise<void> {\r\n        const serializedMessage = this._protocol.writeMessage(message);\r\n\r\n        let backpressurePromise: Promise<void> = Promise.resolve();\r\n\r\n        // Only count invocation messages. Acks, pings, etc. don't need to be resent on reconnect\r\n        if (this._isInvocationMessage(message)) {\r\n            this._totalMessageCount++;\r\n            let backpressurePromiseResolver: (value: void) => void = () => {};\r\n            let backpressurePromiseRejector: (value?: void) => void = () => {};\r\n\r\n            if (isArrayBuffer(serializedMessage)) {\r\n                this._bufferedByteCount += serializedMessage.byteLength;\r\n            } else {\r\n                this._bufferedByteCount += serializedMessage.length;\r\n            }\r\n\r\n            if (this._bufferedByteCount >= this._bufferSize) {\r\n                backpressurePromise = new Promise((resolve, reject) => {\r\n                    backpressurePromiseResolver = resolve;\r\n                    backpressurePromiseRejector = reject;\r\n                });\r\n            }\r\n\r\n            this._messages.push(new BufferedItem(serializedMessage, this._totalMessageCount,\r\n                backpressurePromiseResolver, backpressurePromiseRejector));\r\n        }\r\n\r\n        try {\r\n            // If this is set it means we are reconnecting or resending\r\n            // We don't want to send on a disconnected connection\r\n            // And we don't want to send if resend is running since that would mean sending\r\n            // this message twice\r\n            if (!this._reconnectInProgress) {\r\n                await this._connection.send(serializedMessage);\r\n            }\r\n        } catch {\r\n            this._disconnected();\r\n        }\r\n        await backpressurePromise;\r\n    }\r\n\r\n    public _ack(ackMessage: AckMessage): void {\r\n        let newestAckedMessage = -1;\r\n\r\n        // Find index of newest message being acked\r\n        for (let index = 0; index < this._messages.length; index++) {\r\n            const element = this._messages[index];\r\n            if (element._id <= ackMessage.sequenceId) {\r\n                newestAckedMessage = index;\r\n                if (isArrayBuffer(element._message)) {\r\n                    this._bufferedByteCount -= element._message.byteLength;\r\n                } else {\r\n                    this._bufferedByteCount -= element._message.length;\r\n                }\r\n                // resolve items that have already been sent and acked\r\n                element._resolver();\r\n            } else if (this._bufferedByteCount < this._bufferSize) {\r\n                // resolve items that now fall under the buffer limit but haven't been acked\r\n                element._resolver();\r\n            } else {\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (newestAckedMessage !== -1) {\r\n            // We're removing everything including the message pointed to, so add 1\r\n            this._messages = this._messages.slice(newestAckedMessage + 1);\r\n        }\r\n    }\r\n\r\n    public _shouldProcessMessage(message: HubMessage): boolean {\r\n        if (this._waitForSequenceMessage) {\r\n            if (message.type !== MessageType.Sequence) {\r\n                return false;\r\n            } else {\r\n                this._waitForSequenceMessage = false;\r\n                return true;\r\n            }\r\n        }\r\n\r\n        // No special processing for acks, pings, etc.\r\n        if (!this._isInvocationMessage(message)) {\r\n            return true;\r\n        }\r\n\r\n        const currentId = this._nextReceivingSequenceId;\r\n        this._nextReceivingSequenceId++;\r\n        if (currentId <= this._latestReceivedSequenceId) {\r\n            if (currentId === this._latestReceivedSequenceId) {\r\n                // Should only hit this if we just reconnected and the server is sending\r\n                // Messages it has buffered, which would mean it hasn't seen an Ack for these messages\r\n                this._ackTimer();\r\n            }\r\n            // Ignore, this is a duplicate message\r\n            return false;\r\n        }\r\n\r\n        this._latestReceivedSequenceId = currentId;\r\n\r\n        // Only start the timer for sending an Ack message when we have a message to ack. This also conveniently solves\r\n        // timer throttling by not having a recursive timer, and by starting the timer via a network call (recv)\r\n        this._ackTimer();\r\n        return true;\r\n    }\r\n\r\n    public _resetSequence(message: SequenceMessage): void {\r\n        if (message.sequenceId > this._nextReceivingSequenceId) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._connection.stop(new Error(\"Sequence ID greater than amount of messages we've received.\"));\r\n            return;\r\n        }\r\n\r\n        this._nextReceivingSequenceId = message.sequenceId;\r\n    }\r\n\r\n    public _disconnected(): void {\r\n        this._reconnectInProgress = true;\r\n        this._waitForSequenceMessage = true;\r\n    }\r\n\r\n    public async _resend(): Promise<void> {\r\n        const sequenceId = this._messages.length !== 0\r\n            ? this._messages[0]._id\r\n            :  this._totalMessageCount + 1;\r\n        await this._connection.send(this._protocol.writeMessage({ type: MessageType.Sequence, sequenceId }));\r\n\r\n        // Get a local variable to the _messages, just in case messages are acked while resending\r\n        // Which would slice the _messages array (which creates a new copy)\r\n        const messages = this._messages;\r\n        for (const element of messages) {\r\n            await this._connection.send(element._message);\r\n        }\r\n\r\n        this._reconnectInProgress = false;\r\n    }\r\n\r\n    public _dispose(error?: Error): void {\r\n        error ??= new Error(\"Unable to reconnect to server.\")\r\n\r\n        // Unblock backpressure if any\r\n        for (const element of this._messages) {\r\n            element._rejector(error);\r\n        }\r\n    }\r\n\r\n    private _isInvocationMessage(message: HubMessage): boolean {\r\n        // There is no way to check if something implements an interface.\r\n        // So we individually check the messages in a switch statement.\r\n        // To make sure we don't miss any message types we rely on the compiler\r\n        // seeing the function returns a value and it will do the\r\n        // exhaustive check for us on the switch statement, since we don't use 'case default'\r\n        switch (message.type) {\r\n            case MessageType.Invocation:\r\n            case MessageType.StreamItem:\r\n            case MessageType.Completion:\r\n            case MessageType.StreamInvocation:\r\n            case MessageType.CancelInvocation:\r\n                return true;\r\n            case MessageType.Close:\r\n            case MessageType.Sequence:\r\n            case MessageType.Ping:\r\n            case MessageType.Ack:\r\n                return false;\r\n        }\r\n    }\r\n\r\n    private _ackTimer(): void {\r\n        if (this._ackTimerHandle === undefined) {\r\n            this._ackTimerHandle = setTimeout(async () => {\r\n                try {\r\n                    if (!this._reconnectInProgress) {\r\n                        await this._connection.send(this._protocol.writeMessage({ type: MessageType.Ack, sequenceId: this._latestReceivedSequenceId }))\r\n                    }\r\n                // Ignore errors, that means the connection is closed and we don't care about the Ack message anymore.\r\n                } catch { }\r\n\r\n                clearTimeout(this._ackTimerHandle);\r\n                this._ackTimerHandle = undefined;\r\n            // 1 second delay so we don't spam Ack messages if there are many messages being received at once.\r\n            }, 1000);\r\n        }\r\n    }\r\n}\r\n\r\nclass BufferedItem {\r\n    constructor(message: string | ArrayBuffer, id: number, resolver: (value: void) => void, rejector: (value?: any) => void) {\r\n        this._message = message;\r\n        this._id = id;\r\n        this._resolver = resolver;\r\n        this._rejector = rejector;\r\n    }\r\n\r\n    _message: string | ArrayBuffer;\r\n    _id: number;\r\n    _resolver: (value: void) => void;\r\n    _rejector: (value?: any) => void;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { IRetryPolicy, RetryContext } from \"./IRetryPolicy\";\r\n\r\n// 0, 2, 10, 30 second delays before reconnect attempts.\r\nconst DEFAULT_RETRY_DELAYS_IN_MILLISECONDS = [0, 2000, 10000, 30000, null];\r\n\r\n/** @private */\r\nexport class DefaultReconnectPolicy implements IRetryPolicy {\r\n    private readonly _retryDelays: (number | null)[];\r\n\r\n    constructor(retryDelays?: number[]) {\r\n        this._retryDelays = retryDelays !== undefined ? [...retryDelays, null] : DEFAULT_RETRY_DELAYS_IN_MILLISECONDS;\r\n    }\r\n\r\n    public nextRetryDelayInMilliseconds(retryContext: RetryContext): number | null {\r\n        return this._retryDelays[retryContext.previousRetryCount];\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nexport abstract class HeaderNames {\r\n    static readonly Authorization = \"Authorization\";\r\n    static readonly Cookie = \"<PERSON>ie\";\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\n\r\n/** @private */\r\nexport class AccessTokenHttpClient extends HttpClient {\r\n    private _innerClient: HttpClient;\r\n    _accessToken: string | undefined;\r\n    _accessTokenFactory: (() => string | Promise<string>) | undefined;\r\n\r\n    constructor(innerClient: HttpClient, accessTokenFactory: (() => string | Promise<string>) | undefined) {\r\n        super();\r\n\r\n        this._innerClient = innerClient;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n    }\r\n\r\n    public async send(request: HttpRequest): Promise<HttpResponse> {\r\n        let allowRetry = true;\r\n        if (this._accessTokenFactory && (!this._accessToken || (request.url && request.url.indexOf(\"/negotiate?\") > 0))) {\r\n            // don't retry if the request is a negotiate or if we just got a potentially new token from the access token factory\r\n            allowRetry = false;\r\n            this._accessToken = await this._accessTokenFactory();\r\n        }\r\n        this._setAuthorizationHeader(request);\r\n        const response = await this._innerClient.send(request);\r\n\r\n        if (allowRetry && response.statusCode === 401 && this._accessTokenFactory) {\r\n            this._accessToken = await this._accessTokenFactory();\r\n            this._setAuthorizationHeader(request);\r\n            return await this._innerClient.send(request);\r\n        }\r\n        return response;\r\n    }\r\n\r\n    private _setAuthorizationHeader(request: HttpRequest) {\r\n        if (!request.headers) {\r\n            request.headers = {};\r\n        }\r\n        if (this._accessToken) {\r\n            request.headers[HeaderNames.Authorization] = `Bearer ${this._accessToken}`\r\n        }\r\n        // don't remove the header if there isn't an access token factory, the user manually added the header in this case\r\n        else if (this._accessTokenFactory) {\r\n            if (request.headers[HeaderNames.Authorization]) {\r\n                delete request.headers[HeaderNames.Authorization];\r\n            }\r\n        }\r\n    }\r\n\r\n    public getCookieString(url: string): string {\r\n        return this._innerClient.getCookieString(url);\r\n    }\r\n}", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// This will be treated as a bit flag in the future, so we keep it using power-of-two values.\r\n/** Specifies a specific HTTP transport type. */\r\nexport enum HttpTransportType {\r\n    /** Specifies no transport preference. */\r\n    None = 0,\r\n    /** Specifies the WebSockets transport. */\r\n    WebSockets = 1,\r\n    /** Specifies the Server-Sent Events transport. */\r\n    ServerSentEvents = 2,\r\n    /** Specifies the Long Polling transport. */\r\n    LongPolling = 4,\r\n}\r\n\r\n/** Specifies the transfer format for a connection. */\r\nexport enum TransferFormat {\r\n    /** Specifies that only text data will be transmitted over the connection. */\r\n    Text = 1,\r\n    /** Specifies that binary data will be transmitted over the connection. */\r\n    Binary = 2,\r\n}\r\n\r\n/** An abstraction over the behavior of transports. This is designed to support the framework and not intended for use by applications. */\r\nexport interface ITransport {\r\n    connect(url: string, transferFormat: TransferFormat): Promise<void>;\r\n    send(data: any): Promise<void>;\r\n    stop(): Promise<void>;\r\n    onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    onclose: ((error?: Error) => void) | null;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Rough polyfill of https://developer.mozilla.org/en-US/docs/Web/API/AbortController\r\n// We don't actually ever use the API being polyfilled, we always use the polyfill because\r\n// it's a very new API right now.\r\n\r\n// Not exported from index.\r\n/** @private */\r\nexport class AbortController implements AbortSignal {\r\n    private _isAborted: boolean = false;\r\n    public onabort: (() => void) | null = null;\r\n\r\n    public abort(): void {\r\n        if (!this._isAborted) {\r\n            this._isAborted = true;\r\n            if (this.onabort) {\r\n                this.onabort();\r\n            }\r\n        }\r\n    }\r\n\r\n    get signal(): AbortSignal {\r\n        return this;\r\n    }\r\n\r\n    get aborted(): boolean {\r\n        return this._isAborted;\r\n    }\r\n}\r\n\r\n/** Represents a signal that can be monitored to determine if a request has been aborted. */\r\nexport interface AbortSignal {\r\n    /** Indicates if the request has been aborted. */\r\n    aborted: boolean;\r\n    /** Set this to a handler that will be invoked when the request is aborted. */\r\n    onabort: (() => void) | null;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortController } from \"./AbortController\";\r\nimport { HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpRequest } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, sendMessage } from \"./Utils\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n// Not exported from 'index', this type is internal.\r\n/** @private */\r\nexport class LongPollingTransport implements ITransport {\r\n    private readonly _httpClient: HttpClient;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    private readonly _pollAbort: AbortController;\r\n\r\n    private _url?: string;\r\n    private _running: boolean;\r\n    private _receiving?: Promise<void>;\r\n    private _closeError?: Error | unknown;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error | unknown) => void) | null;\r\n\r\n    // This is an internal type, not exported from 'index' so this is really just internal.\r\n    public get pollAborted(): boolean {\r\n        return this._pollAbort.aborted;\r\n    }\r\n\r\n    constructor(httpClient: HttpClient, logger: ILogger, options: IHttpConnectionOptions) {\r\n        this._httpClient = httpClient;\r\n        this._logger = logger;\r\n        this._pollAbort = new AbortController();\r\n        this._options = options;\r\n\r\n        this._running = false;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._url = url;\r\n\r\n        this._logger.log(LogLevel.Trace, \"(LongPolling transport) Connecting.\");\r\n\r\n        // Allow binary format on Node and Browsers that support binary content (indicated by the presence of responseType property)\r\n        if (transferFormat === TransferFormat.Binary &&\r\n            (typeof XMLHttpRequest !== \"undefined\" && typeof new XMLHttpRequest().responseType !== \"string\")) {\r\n            throw new Error(\"Binary protocols over XmlHttpRequest not implementing advanced features are not supported.\");\r\n        }\r\n\r\n        const [name, value] = getUserAgentHeader();\r\n        const headers = { [name]: value, ...this._options.headers };\r\n\r\n        const pollOptions: HttpRequest = {\r\n            abortSignal: this._pollAbort.signal,\r\n            headers,\r\n            timeout: 100000,\r\n            withCredentials: this._options.withCredentials,\r\n        };\r\n\r\n        if (transferFormat === TransferFormat.Binary) {\r\n            pollOptions.responseType = \"arraybuffer\";\r\n        }\r\n\r\n        // Make initial long polling request\r\n        // Server uses first long polling request to finish initializing connection and it returns without data\r\n        const pollUrl = `${url}&_=${Date.now()}`;\r\n        this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n        const response = await this._httpClient.get(pollUrl, pollOptions);\r\n        if (response.statusCode !== 200) {\r\n            this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n\r\n            // Mark running as false so that the poll immediately ends and runs the close logic\r\n            this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n            this._running = false;\r\n        } else {\r\n            this._running = true;\r\n        }\r\n\r\n        this._receiving = this._poll(this._url, pollOptions);\r\n    }\r\n\r\n    private async _poll(url: string, pollOptions: HttpRequest): Promise<void> {\r\n        try {\r\n            while (this._running) {\r\n                try {\r\n                    const pollUrl = `${url}&_=${Date.now()}`;\r\n                    this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n                    const response = await this._httpClient.get(pollUrl, pollOptions);\r\n\r\n                    if (response.statusCode === 204) {\r\n                        this._logger.log(LogLevel.Information, \"(LongPolling transport) Poll terminated by server.\");\r\n\r\n                        this._running = false;\r\n                    } else if (response.statusCode !== 200) {\r\n                        this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n\r\n                        // Unexpected status code\r\n                        this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n                        this._running = false;\r\n                    } else {\r\n                        // Process the response\r\n                        if (response.content) {\r\n                            this._logger.log(LogLevel.Trace, `(LongPolling transport) data received. ${getDataDetail(response.content, this._options.logMessageContent!)}.`);\r\n                            if (this.onreceive) {\r\n                                this.onreceive(response.content);\r\n                            }\r\n                        } else {\r\n                            // This is another way timeout manifest.\r\n                            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        }\r\n                    }\r\n                } catch (e) {\r\n                    if (!this._running) {\r\n                        // Log but disregard errors that occur after stopping\r\n                        this._logger.log(LogLevel.Trace, `(LongPolling transport) Poll errored after shutdown: ${(e as any).message}`);\r\n                    } else {\r\n                        if (e instanceof TimeoutError) {\r\n                            // Ignore timeouts and reissue the poll.\r\n                            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        } else {\r\n                            // Close the connection with the error as the result.\r\n                            this._closeError = e;\r\n                            this._running = false;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        } finally {\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Polling complete.\");\r\n\r\n            // We will reach here with pollAborted==false when the server returned a response causing the transport to stop.\r\n            // If pollAborted==true then client initiated the stop and the stop method will raise the close event after DELETE is sent.\r\n            if (!this.pollAborted) {\r\n                this._raiseOnClose();\r\n            }\r\n        }\r\n    }\r\n\r\n    public async send(data: any): Promise<void> {\r\n        if (!this._running) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return sendMessage(this._logger, \"LongPolling\", this._httpClient, this._url!, data, this._options);\r\n    }\r\n\r\n    public async stop(): Promise<void> {\r\n        this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stopping polling.\");\r\n\r\n        // Tell receiving loop to stop, abort any current request, and then wait for it to finish\r\n        this._running = false;\r\n        this._pollAbort.abort();\r\n\r\n        try {\r\n            await this._receiving;\r\n\r\n            // Send DELETE to clean up long polling on the server\r\n            this._logger.log(LogLevel.Trace, `(LongPolling transport) sending DELETE request to ${this._url}.`);\r\n\r\n            const headers: {[k: string]: string} = {};\r\n            const [name, value] = getUserAgentHeader();\r\n            headers[name] = value;\r\n\r\n            const deleteOptions: HttpRequest = {\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            };\r\n\r\n            let error;\r\n            try {\r\n                await this._httpClient.delete(this._url!, deleteOptions);\r\n            } catch (err) {\r\n                error = err;\r\n            }\r\n\r\n            if (error) {\r\n                if (error instanceof HttpError) {\r\n                    if (error.statusCode === 404) {\r\n                        this._logger.log(LogLevel.Trace, \"(LongPolling transport) A 404 response was returned from sending a DELETE request.\");\r\n                    } else {\r\n                        this._logger.log(LogLevel.Trace, `(LongPolling transport) Error sending a DELETE request: ${error}`);\r\n                    }\r\n                }\r\n            } else {\r\n                this._logger.log(LogLevel.Trace, \"(LongPolling transport) DELETE request accepted.\");\r\n            }\r\n\r\n        } finally {\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stop finished.\");\r\n\r\n            // Raise close event here instead of in polling\r\n            // It needs to happen after the DELETE request is sent\r\n            this._raiseOnClose();\r\n        }\r\n    }\r\n\r\n    private _raiseOnClose() {\r\n        if (this.onclose) {\r\n            let logMessage = \"(LongPolling transport) Firing onclose event.\";\r\n            if (this._closeError) {\r\n                logMessage += \" Error: \" + this._closeError;\r\n            }\r\n            this._logger.log(LogLevel.Trace, logMessage);\r\n            this.onclose(this._closeError);\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, Platform, sendMessage } from \"./Utils\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n/** @private */\r\nexport class ServerSentEventsTransport implements ITransport {\r\n    private readonly _httpClient: HttpClient;\r\n    private readonly _accessToken: string | undefined;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    private _eventSource?: EventSource;\r\n    private _url?: string;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error | unknown) => void) | null;\r\n\r\n    constructor(httpClient: HttpClient, accessToken: string | undefined, logger: ILogger,\r\n                options: IHttpConnectionOptions) {\r\n        this._httpClient = httpClient;\r\n        this._accessToken = accessToken;\r\n        this._logger = logger;\r\n        this._options = options;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._logger.log(LogLevel.Trace, \"(SSE transport) Connecting.\");\r\n\r\n        // set url before accessTokenFactory because this._url is only for send and we set the auth header instead of the query string for send\r\n        this._url = url;\r\n\r\n        if (this._accessToken) {\r\n            url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(this._accessToken)}`;\r\n        }\r\n\r\n        return new Promise<void>((resolve, reject) => {\r\n            let opened = false;\r\n            if (transferFormat !== TransferFormat.Text) {\r\n                reject(new Error(\"The Server-Sent Events transport only supports the 'Text' transfer format\"));\r\n                return;\r\n            }\r\n\r\n            let eventSource: EventSource;\r\n            if (Platform.isBrowser || Platform.isWebWorker) {\r\n                eventSource = new this._options.EventSource!(url, { withCredentials: this._options.withCredentials });\r\n            } else {\r\n                // Non-browser passes cookies via the dictionary\r\n                const cookies = this._httpClient.getCookieString(url);\r\n                const headers: MessageHeaders = {};\r\n                headers.Cookie = cookies;\r\n                const [name, value] = getUserAgentHeader();\r\n                headers[name] = value;\r\n\r\n                eventSource = new this._options.EventSource!(url, { withCredentials: this._options.withCredentials, headers: { ...headers, ...this._options.headers} } as EventSourceInit);\r\n            }\r\n\r\n            try {\r\n                eventSource.onmessage = (e: MessageEvent) => {\r\n                    if (this.onreceive) {\r\n                        try {\r\n                            this._logger.log(LogLevel.Trace, `(SSE transport) data received. ${getDataDetail(e.data, this._options.logMessageContent!)}.`);\r\n                            this.onreceive(e.data);\r\n                        } catch (error) {\r\n                            this._close(error);\r\n                            return;\r\n                        }\r\n                    }\r\n                };\r\n\r\n                // @ts-ignore: not using event on purpose\r\n                eventSource.onerror = (e: Event) => {\r\n                    // EventSource doesn't give any useful information about server side closes.\r\n                    if (opened) {\r\n                        this._close();\r\n                    } else {\r\n                        reject(new Error(\"EventSource failed to connect. The connection could not be found on the server,\"\r\n                        + \" either the connection ID is not present on the server, or a proxy is refusing/buffering the connection.\"\r\n                        + \" If you have multiple servers check that sticky sessions are enabled.\"));\r\n                    }\r\n                };\r\n\r\n                eventSource.onopen = () => {\r\n                    this._logger.log(LogLevel.Information, `SSE connected to ${this._url}`);\r\n                    this._eventSource = eventSource;\r\n                    opened = true;\r\n                    resolve();\r\n                };\r\n            } catch (e) {\r\n                reject(e);\r\n                return;\r\n            }\r\n        });\r\n    }\r\n\r\n    public async send(data: any): Promise<void> {\r\n        if (!this._eventSource) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return sendMessage(this._logger, \"SSE\", this._httpClient, this._url!, data, this._options);\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        this._close();\r\n        return Promise.resolve();\r\n    }\r\n\r\n    private _close(e?: Error | unknown) {\r\n        if (this._eventSource) {\r\n            this._eventSource.close();\r\n            this._eventSource = undefined;\r\n\r\n            if (this.onclose) {\r\n                this.onclose(e);\r\n            }\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { WebSocketConstructor } from \"./Polyfills\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, Platform } from \"./Utils\";\r\n\r\n/** @private */\r\nexport class WebSocketTransport implements ITransport {\r\n    private readonly _logger: ILogger;\r\n    private readonly _accessTokenFactory: (() => string | Promise<string>) | undefined;\r\n    private readonly _logMessageContent: boolean;\r\n    private readonly _webSocketConstructor: WebSocketConstructor;\r\n    private readonly _httpClient: HttpClient;\r\n    private _webSocket?: WebSocket;\r\n    private _headers: MessageHeaders;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error) => void) | null;\r\n\r\n    constructor(httpClient: HttpClient, accessTokenFactory: (() => string | Promise<string>) | undefined, logger: ILogger,\r\n                logMessageContent: boolean, webSocketConstructor: WebSocketConstructor, headers: MessageHeaders) {\r\n        this._logger = logger;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n        this._logMessageContent = logMessageContent;\r\n        this._webSocketConstructor = webSocketConstructor;\r\n        this._httpClient = httpClient;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n        this._headers = headers;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n        this._logger.log(LogLevel.Trace, \"(WebSockets transport) Connecting.\");\r\n\r\n        let token: string;\r\n        if (this._accessTokenFactory) {\r\n            token = await this._accessTokenFactory();\r\n        }\r\n\r\n        return new Promise<void>((resolve, reject) => {\r\n            url = url.replace(/^http/, \"ws\");\r\n            let webSocket: WebSocket | undefined;\r\n            const cookies = this._httpClient.getCookieString(url);\r\n            let opened = false;\r\n\r\n            if (Platform.isNode || Platform.isReactNative) {\r\n                const headers: {[k: string]: string} = {};\r\n                const [name, value] = getUserAgentHeader();\r\n                headers[name] = value;\r\n                if (token) {\r\n                    headers[HeaderNames.Authorization] = `Bearer ${token}`;\r\n                }\r\n\r\n                if (cookies) {\r\n                    headers[HeaderNames.Cookie] = cookies;\r\n                }\r\n\r\n                // Only pass headers when in non-browser environments\r\n                webSocket = new this._webSocketConstructor(url, undefined, {\r\n                    headers: { ...headers, ...this._headers },\r\n                });\r\n            }\r\n            else\r\n            {\r\n                if (token) {\r\n                    url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(token)}`;\r\n                }\r\n            }\r\n\r\n            if (!webSocket) {\r\n                // Chrome is not happy with passing 'undefined' as protocol\r\n                webSocket = new this._webSocketConstructor(url);\r\n            }\r\n\r\n            if (transferFormat === TransferFormat.Binary) {\r\n                webSocket.binaryType = \"arraybuffer\";\r\n            }\r\n\r\n            webSocket.onopen = (_event: Event) => {\r\n                this._logger.log(LogLevel.Information, `WebSocket connected to ${url}.`);\r\n                this._webSocket = webSocket;\r\n                opened = true;\r\n                resolve();\r\n            };\r\n\r\n            webSocket.onerror = (event: Event) => {\r\n                let error: any = null;\r\n                // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                    error = event.error;\r\n                } else {\r\n                    error = \"There was an error with the transport\";\r\n                }\r\n\r\n                this._logger.log(LogLevel.Information, `(WebSockets transport) ${error}.`);\r\n            };\r\n\r\n            webSocket.onmessage = (message: MessageEvent) => {\r\n                this._logger.log(LogLevel.Trace, `(WebSockets transport) data received. ${getDataDetail(message.data, this._logMessageContent)}.`);\r\n                if (this.onreceive) {\r\n                    try {\r\n                        this.onreceive(message.data);\r\n                    } catch (error) {\r\n                        this._close(error);\r\n                        return;\r\n                    }\r\n                }\r\n            };\r\n\r\n            webSocket.onclose = (event: CloseEvent) => {\r\n                // Don't call close handler if connection was never established\r\n                // We'll reject the connect call instead\r\n                if (opened) {\r\n                    this._close(event);\r\n                } else {\r\n                    let error: any = null;\r\n                    // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                    if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                        error = event.error;\r\n                    } else {\r\n                        error = \"WebSocket failed to connect. The connection could not be found on the server,\"\r\n                        + \" either the endpoint may not be a SignalR endpoint,\"\r\n                        + \" the connection ID is not present on the server, or there is a proxy blocking WebSockets.\"\r\n                        + \" If you have multiple servers check that sticky sessions are enabled.\";\r\n                    }\r\n\r\n                    reject(new Error(error));\r\n                }\r\n            };\r\n        });\r\n    }\r\n\r\n    public send(data: any): Promise<void> {\r\n        if (this._webSocket && this._webSocket.readyState === this._webSocketConstructor.OPEN) {\r\n            this._logger.log(LogLevel.Trace, `(WebSockets transport) sending data. ${getDataDetail(data, this._logMessageContent)}.`);\r\n            this._webSocket.send(data);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        return Promise.reject(\"WebSocket is not in the OPEN state\");\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        if (this._webSocket) {\r\n            // Manually invoke onclose callback inline so we know the HttpConnection was closed properly before returning\r\n            // This also solves an issue where websocket.onclose could take 18+ seconds to trigger during network disconnects\r\n            this._close(undefined);\r\n        }\r\n\r\n        return Promise.resolve();\r\n    }\r\n\r\n    private _close(event: CloseEvent | Error | unknown): void {\r\n        // webSocket will be null if the transport did not start successfully\r\n        if (this._webSocket) {\r\n            // Clear websocket handlers because we are considering the socket closed now\r\n            this._webSocket.onclose = () => {};\r\n            this._webSocket.onmessage = () => {};\r\n            this._webSocket.onerror = () => {};\r\n            this._webSocket.close();\r\n            this._webSocket = undefined;\r\n        }\r\n\r\n        this._logger.log(LogLevel.Trace, \"(WebSockets transport) socket closed.\");\r\n\r\n        if (this.onclose) {\r\n            if (this._isCloseEvent(event) && (event.wasClean === false || event.code !== 1000)) {\r\n                this.onclose(new Error(`WebSocket closed with status code: ${event.code} (${event.reason || \"no reason given\"}).`));\r\n            } else if (event instanceof Error) {\r\n                this.onclose(event);\r\n            } else {\r\n                this.onclose();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _isCloseEvent(event?: any): event is CloseEvent {\r\n        return event && typeof event.wasClean === \"boolean\" && typeof event.code === \"number\";\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AccessTokenHttpClient } from \"./AccessTokenHttpClient\";\r\nimport { DefaultHttpClient } from \"./DefaultHttpClient\";\r\nimport { AggregateErrors, DisabledTransportError, FailedToNegotiateWithServerError, FailedToStartTransportError, HttpError, UnsupportedTransportError, AbortError } from \"./Errors\";\r\nimport { IConnection } from \"./IConnection\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { HttpTransportType, ITransport, TransferFormat } from \"./ITransport\";\r\nimport { LongPollingTransport } from \"./LongPollingTransport\";\r\nimport { ServerSentEventsTransport } from \"./ServerSentEventsTransport\";\r\nimport { Arg, createLogger, getUserAgentHeader, Platform } from \"./Utils\";\r\nimport { WebSocketTransport } from \"./WebSocketTransport\";\r\n\r\n/** @private */\r\nconst enum ConnectionState {\r\n    Connecting = \"Connecting\",\r\n    Connected = \"Connected\",\r\n    Disconnected = \"Disconnected\",\r\n    Disconnecting = \"Disconnecting\",\r\n}\r\n\r\n/** @private */\r\nexport interface INegotiateResponse {\r\n    connectionId?: string;\r\n    connectionToken?: string;\r\n    negotiateVersion?: number;\r\n    availableTransports?: IAvailableTransport[];\r\n    url?: string;\r\n    accessToken?: string;\r\n    error?: string;\r\n    useStatefulReconnect?: boolean;\r\n}\r\n\r\n/** @private */\r\nexport interface IAvailableTransport {\r\n    transport: keyof typeof HttpTransportType;\r\n    transferFormats: (keyof typeof TransferFormat)[];\r\n}\r\n\r\nconst MAX_REDIRECTS = 100;\r\n\r\n/** @private */\r\nexport class HttpConnection implements IConnection {\r\n    private _connectionState: ConnectionState;\r\n    // connectionStarted is tracked independently from connectionState, so we can check if the\r\n    // connection ever did successfully transition from connecting to connected before disconnecting.\r\n    private _connectionStarted: boolean;\r\n    private readonly _httpClient: AccessTokenHttpClient;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    // Needs to not start with _ to be available for tests\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private transport?: ITransport;\r\n    private _startInternalPromise?: Promise<void>;\r\n    private _stopPromise?: Promise<void>;\r\n    private _stopPromiseResolver: (value?: PromiseLike<void>) => void = () => {};\r\n    private _stopError?: Error;\r\n    private _accessTokenFactory?: () => string | Promise<string>;\r\n    private _sendQueue?: TransportSendQueue;\r\n\r\n    public readonly features: any = {};\r\n    public baseUrl: string;\r\n    public connectionId?: string;\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((e?: Error) => void) | null;\r\n\r\n    private readonly _negotiateVersion: number = 1;\r\n\r\n    constructor(url: string, options: IHttpConnectionOptions = {}) {\r\n        Arg.isRequired(url, \"url\");\r\n\r\n        this._logger = createLogger(options.logger);\r\n        this.baseUrl = this._resolveUrl(url);\r\n\r\n        options = options || {};\r\n        options.logMessageContent = options.logMessageContent === undefined ? false : options.logMessageContent;\r\n        if (typeof options.withCredentials === \"boolean\" || options.withCredentials === undefined) {\r\n            options.withCredentials = options.withCredentials === undefined ? true : options.withCredentials;\r\n        } else {\r\n            throw new Error(\"withCredentials option was not a 'boolean' or 'undefined' value\");\r\n        }\r\n        options.timeout = options.timeout === undefined ? 100 * 1000 : options.timeout;\r\n\r\n        let webSocketModule: any = null;\r\n        let eventSourceModule: any = null;\r\n\r\n        if (Platform.isNode && typeof require !== \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n            webSocketModule = requireFunc(\"ws\");\r\n            eventSourceModule = requireFunc(\"eventsource\");\r\n        }\r\n\r\n        if (!Platform.isNode && typeof WebSocket !== \"undefined\" && !options.WebSocket) {\r\n            options.WebSocket = WebSocket;\r\n        } else if (Platform.isNode && !options.WebSocket) {\r\n            if (webSocketModule) {\r\n                options.WebSocket = webSocketModule;\r\n            }\r\n        }\r\n\r\n        if (!Platform.isNode && typeof EventSource !== \"undefined\" && !options.EventSource) {\r\n            options.EventSource = EventSource;\r\n        } else if (Platform.isNode && !options.EventSource) {\r\n            if (typeof eventSourceModule !== \"undefined\") {\r\n                options.EventSource = eventSourceModule;\r\n            }\r\n        }\r\n\r\n        this._httpClient = new AccessTokenHttpClient(options.httpClient || new DefaultHttpClient(this._logger), options.accessTokenFactory);\r\n        this._connectionState = ConnectionState.Disconnected;\r\n        this._connectionStarted = false;\r\n        this._options = options;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public start(): Promise<void>;\r\n    public start(transferFormat: TransferFormat): Promise<void>;\r\n    public async start(transferFormat?: TransferFormat): Promise<void> {\r\n        transferFormat = transferFormat || TransferFormat.Binary;\r\n\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._logger.log(LogLevel.Debug, `Starting connection with transfer format '${TransferFormat[transferFormat]}'.`);\r\n\r\n        if (this._connectionState !== ConnectionState.Disconnected) {\r\n            return Promise.reject(new Error(\"Cannot start an HttpConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n\r\n        this._connectionState = ConnectionState.Connecting;\r\n\r\n        this._startInternalPromise = this._startInternal(transferFormat);\r\n        await this._startInternalPromise;\r\n\r\n        // The TypeScript compiler thinks that connectionState must be Connecting here. The TypeScript compiler is wrong.\r\n        if (this._connectionState as any === ConnectionState.Disconnecting) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"Failed to start the HttpConnection before stop() was called.\";\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            // We cannot await stopPromise inside startInternal since stopInternal awaits the startInternalPromise.\r\n            await this._stopPromise;\r\n\r\n            return Promise.reject(new AbortError(message));\r\n        } else if (this._connectionState as any !== ConnectionState.Connected) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"HttpConnection.startInternal completed gracefully but didn't enter the connection into the connected state!\";\r\n            this._logger.log(LogLevel.Error, message);\r\n            return Promise.reject(new AbortError(message));\r\n        }\r\n\r\n        this._connectionStarted = true;\r\n    }\r\n\r\n    public send(data: string | ArrayBuffer): Promise<void> {\r\n        if (this._connectionState !== ConnectionState.Connected) {\r\n            return Promise.reject(new Error(\"Cannot send data if the connection is not in the 'Connected' State.\"));\r\n        }\r\n\r\n        if (!this._sendQueue) {\r\n            this._sendQueue = new TransportSendQueue(this.transport!);\r\n        }\r\n\r\n        // Transport will not be null if state is connected\r\n        return this._sendQueue.send(data);\r\n    }\r\n\r\n    public async stop(error?: Error): Promise<void> {\r\n        if (this._connectionState === ConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Disconnecting) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise;\r\n        }\r\n\r\n        this._connectionState = ConnectionState.Disconnecting;\r\n\r\n        this._stopPromise = new Promise((resolve) => {\r\n            // Don't complete stop() until stopConnection() completes.\r\n            this._stopPromiseResolver = resolve;\r\n        });\r\n\r\n        // stopInternal should never throw so just observe it.\r\n        await this._stopInternal(error);\r\n        await this._stopPromise;\r\n    }\r\n\r\n    private async _stopInternal(error?: Error): Promise<void> {\r\n        // Set error as soon as possible otherwise there is a race between\r\n        // the transport closing and providing an error and the error from a close message\r\n        // We would prefer the close message error.\r\n        this._stopError = error;\r\n\r\n        try {\r\n            await this._startInternalPromise;\r\n        } catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n\r\n        // The transport's onclose will trigger stopConnection which will run our onclose event.\r\n        // The transport should always be set if currently connected. If it wasn't set, it's likely because\r\n        // stop was called during start() and start() failed.\r\n        if (this.transport) {\r\n            try {\r\n                await this.transport.stop();\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `HttpConnection.transport.stop() threw error '${e}'.`);\r\n                this._stopConnection();\r\n            }\r\n\r\n            this.transport = undefined;\r\n        } else {\r\n            this._logger.log(LogLevel.Debug, \"HttpConnection.transport is undefined in HttpConnection.stop() because start() failed.\");\r\n        }\r\n    }\r\n\r\n    private async _startInternal(transferFormat: TransferFormat): Promise<void> {\r\n        // Store the original base url and the access token factory since they may change\r\n        // as part of negotiating\r\n        let url = this.baseUrl;\r\n        this._accessTokenFactory = this._options.accessTokenFactory;\r\n        this._httpClient._accessTokenFactory = this._accessTokenFactory;\r\n\r\n        try {\r\n            if (this._options.skipNegotiation) {\r\n                if (this._options.transport === HttpTransportType.WebSockets) {\r\n                    // No need to add a connection ID in this case\r\n                    this.transport = this._constructTransport(HttpTransportType.WebSockets);\r\n                    // We should just call connect directly in this case.\r\n                    // No fallback or negotiate in this case.\r\n                    await this._startTransport(url, transferFormat);\r\n                } else {\r\n                    throw new Error(\"Negotiation can only be skipped when using the WebSocket transport directly.\");\r\n                }\r\n            } else {\r\n                let negotiateResponse: INegotiateResponse | null = null;\r\n                let redirects = 0;\r\n\r\n                do {\r\n                    negotiateResponse = await this._getNegotiationResponse(url);\r\n                    // the user tries to stop the connection when it is being started\r\n                    if (this._connectionState === ConnectionState.Disconnecting || this._connectionState === ConnectionState.Disconnected) {\r\n                        throw new AbortError(\"The connection was stopped during negotiation.\");\r\n                    }\r\n\r\n                    if (negotiateResponse.error) {\r\n                        throw new Error(negotiateResponse.error);\r\n                    }\r\n\r\n                    if ((negotiateResponse as any).ProtocolVersion) {\r\n                        throw new Error(\"Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.\");\r\n                    }\r\n\r\n                    if (negotiateResponse.url) {\r\n                        url = negotiateResponse.url;\r\n                    }\r\n\r\n                    if (negotiateResponse.accessToken) {\r\n                        // Replace the current access token factory with one that uses\r\n                        // the returned access token\r\n                        const accessToken = negotiateResponse.accessToken;\r\n                        this._accessTokenFactory = () => accessToken;\r\n                        // set the factory to undefined so the AccessTokenHttpClient won't retry with the same token, since we know it won't change until a connection restart\r\n                        this._httpClient._accessToken = accessToken;\r\n                        this._httpClient._accessTokenFactory = undefined;\r\n                    }\r\n\r\n                    redirects++;\r\n                }\r\n                while (negotiateResponse.url && redirects < MAX_REDIRECTS);\r\n\r\n                if (redirects === MAX_REDIRECTS && negotiateResponse.url) {\r\n                    throw new Error(\"Negotiate redirection limit exceeded.\");\r\n                }\r\n\r\n                await this._createTransport(url, this._options.transport, negotiateResponse, transferFormat);\r\n            }\r\n\r\n            if (this.transport instanceof LongPollingTransport) {\r\n                this.features.inherentKeepAlive = true;\r\n            }\r\n\r\n            if (this._connectionState === ConnectionState.Connecting) {\r\n                // Ensure the connection transitions to the connected state prior to completing this.startInternalPromise.\r\n                // start() will handle the case when stop was called and startInternal exits still in the disconnecting state.\r\n                this._logger.log(LogLevel.Debug, \"The HttpConnection connected successfully.\");\r\n                this._connectionState = ConnectionState.Connected;\r\n            }\r\n\r\n            // stop() is waiting on us via this.startInternalPromise so keep this.transport around so it can clean up.\r\n            // This is the only case startInternal can exit in neither the connected nor disconnected state because stopConnection()\r\n            // will transition to the disconnected state. start() will wait for the transition using the stopPromise.\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Error, \"Failed to start the connection: \" + e);\r\n            this._connectionState = ConnectionState.Disconnected;\r\n            this.transport = undefined;\r\n\r\n            // if start fails, any active calls to stop assume that start will complete the stop promise\r\n            this._stopPromiseResolver();\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n\r\n    private async _getNegotiationResponse(url: string): Promise<INegotiateResponse> {\r\n        const headers: {[k: string]: string} = {};\r\n        const [name, value] = getUserAgentHeader();\r\n        headers[name] = value;\r\n\r\n        const negotiateUrl = this._resolveNegotiateUrl(url);\r\n        this._logger.log(LogLevel.Debug, `Sending negotiation request: ${negotiateUrl}.`);\r\n        try {\r\n            const response = await this._httpClient.post(negotiateUrl, {\r\n                content: \"\",\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            });\r\n\r\n            if (response.statusCode !== 200) {\r\n                return Promise.reject(new Error(`Unexpected status code returned from negotiate '${response.statusCode}'`));\r\n            }\r\n\r\n            const negotiateResponse = JSON.parse(response.content as string) as INegotiateResponse;\r\n            if (!negotiateResponse.negotiateVersion || negotiateResponse.negotiateVersion < 1) {\r\n                // Negotiate version 0 doesn't use connectionToken\r\n                // So we set it equal to connectionId so all our logic can use connectionToken without being aware of the negotiate version\r\n                negotiateResponse.connectionToken = negotiateResponse.connectionId;\r\n            }\r\n\r\n            if (negotiateResponse.useStatefulReconnect && this._options._useStatefulReconnect !== true) {\r\n                return Promise.reject(new FailedToNegotiateWithServerError(\"Client didn't negotiate Stateful Reconnect but the server did.\"));\r\n            }\r\n\r\n            return negotiateResponse;\r\n        } catch (e) {\r\n            let errorMessage = \"Failed to complete negotiation with the server: \" + e;\r\n            if (e instanceof HttpError) {\r\n                if (e.statusCode === 404) {\r\n                    errorMessage = errorMessage + \" Either this is not a SignalR endpoint or there is a proxy blocking the connection.\";\r\n                }\r\n            }\r\n            this._logger.log(LogLevel.Error, errorMessage);\r\n\r\n            return Promise.reject(new FailedToNegotiateWithServerError(errorMessage));\r\n        }\r\n    }\r\n\r\n    private _createConnectUrl(url: string, connectionToken: string | null | undefined) {\r\n        if (!connectionToken) {\r\n            return url;\r\n        }\r\n\r\n        return url + (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + `id=${connectionToken}`;\r\n    }\r\n\r\n    private async _createTransport(url: string, requestedTransport: HttpTransportType | ITransport | undefined, negotiateResponse: INegotiateResponse, requestedTransferFormat: TransferFormat): Promise<void> {\r\n        let connectUrl = this._createConnectUrl(url, negotiateResponse.connectionToken);\r\n        if (this._isITransport(requestedTransport)) {\r\n            this._logger.log(LogLevel.Debug, \"Connection was provided an instance of ITransport, using that directly.\");\r\n            this.transport = requestedTransport;\r\n            await this._startTransport(connectUrl, requestedTransferFormat);\r\n\r\n            this.connectionId = negotiateResponse.connectionId;\r\n            return;\r\n        }\r\n\r\n        const transportExceptions: any[] = [];\r\n        const transports = negotiateResponse.availableTransports || [];\r\n        let negotiate: INegotiateResponse | undefined = negotiateResponse;\r\n        for (const endpoint of transports) {\r\n            const transportOrError = this._resolveTransportOrError(endpoint, requestedTransport, requestedTransferFormat,\r\n                negotiate?.useStatefulReconnect === true);\r\n            if (transportOrError instanceof Error) {\r\n                // Store the error and continue, we don't want to cause a re-negotiate in these cases\r\n                transportExceptions.push(`${endpoint.transport} failed:`);\r\n                transportExceptions.push(transportOrError);\r\n            } else if (this._isITransport(transportOrError)) {\r\n                this.transport = transportOrError;\r\n                if (!negotiate) {\r\n                    try {\r\n                        negotiate = await this._getNegotiationResponse(url);\r\n                    } catch (ex) {\r\n                        return Promise.reject(ex);\r\n                    }\r\n                    connectUrl = this._createConnectUrl(url, negotiate.connectionToken);\r\n                }\r\n                try {\r\n                    await this._startTransport(connectUrl, requestedTransferFormat);\r\n                    this.connectionId = negotiate.connectionId;\r\n                    return;\r\n                } catch (ex) {\r\n                    this._logger.log(LogLevel.Error, `Failed to start the transport '${endpoint.transport}': ${ex}`);\r\n                    negotiate = undefined;\r\n                    transportExceptions.push(new FailedToStartTransportError(`${endpoint.transport} failed: ${ex}`, HttpTransportType[endpoint.transport]));\r\n\r\n                    if (this._connectionState !== ConnectionState.Connecting) {\r\n                        const message = \"Failed to select transport before stop() was called.\";\r\n                        this._logger.log(LogLevel.Debug, message);\r\n                        return Promise.reject(new AbortError(message));\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        if (transportExceptions.length > 0) {\r\n            return Promise.reject(new AggregateErrors(`Unable to connect to the server with any of the available transports. ${transportExceptions.join(\" \")}`, transportExceptions));\r\n        }\r\n        return Promise.reject(new Error(\"None of the transports supported by the client are supported by the server.\"));\r\n    }\r\n\r\n    private _constructTransport(transport: HttpTransportType): ITransport {\r\n        switch (transport) {\r\n            case HttpTransportType.WebSockets:\r\n                if (!this._options.WebSocket) {\r\n                    throw new Error(\"'WebSocket' is not supported in your environment.\");\r\n                }\r\n                return new WebSocketTransport(this._httpClient, this._accessTokenFactory, this._logger, this._options.logMessageContent!,\r\n                    this._options.WebSocket, this._options.headers || {});\r\n            case HttpTransportType.ServerSentEvents:\r\n                if (!this._options.EventSource) {\r\n                    throw new Error(\"'EventSource' is not supported in your environment.\");\r\n                }\r\n                return new ServerSentEventsTransport(this._httpClient, this._httpClient._accessToken, this._logger, this._options);\r\n            case HttpTransportType.LongPolling:\r\n                return new LongPollingTransport(this._httpClient, this._logger, this._options);\r\n            default:\r\n                throw new Error(`Unknown transport: ${transport}.`);\r\n        }\r\n    }\r\n\r\n    private _startTransport(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        this.transport!.onreceive = this.onreceive;\r\n        if (this.features.reconnect) {\r\n            this.transport!.onclose = async (e) => {\r\n                let callStop = false;\r\n                if (this.features.reconnect) {\r\n                    try {\r\n                        this.features.disconnected();\r\n                        await this.transport!.connect(url, transferFormat);\r\n                        await this.features.resend();\r\n                    } catch {\r\n                        callStop = true;\r\n                    }\r\n                } else {\r\n                    this._stopConnection(e);\r\n                    return;\r\n                }\r\n\r\n                if (callStop) {\r\n                    this._stopConnection(e);\r\n                }\r\n            };\r\n        } else {\r\n            this.transport!.onclose = (e) => this._stopConnection(e);\r\n        }\r\n        return this.transport!.connect(url, transferFormat);\r\n    }\r\n\r\n    private _resolveTransportOrError(endpoint: IAvailableTransport, requestedTransport: HttpTransportType | undefined,\r\n        requestedTransferFormat: TransferFormat, useStatefulReconnect: boolean): ITransport | Error | unknown {\r\n        const transport = HttpTransportType[endpoint.transport];\r\n        if (transport === null || transport === undefined) {\r\n            this._logger.log(LogLevel.Debug, `Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n            return new Error(`Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n        } else {\r\n            if (transportMatches(requestedTransport, transport)) {\r\n                const transferFormats = endpoint.transferFormats.map((s) => TransferFormat[s]);\r\n                if (transferFormats.indexOf(requestedTransferFormat) >= 0) {\r\n                    if ((transport === HttpTransportType.WebSockets && !this._options.WebSocket) ||\r\n                        (transport === HttpTransportType.ServerSentEvents && !this._options.EventSource)) {\r\n                        this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it is not supported in your environment.'`);\r\n                        return new UnsupportedTransportError(`'${HttpTransportType[transport]}' is not supported in your environment.`, transport);\r\n                    } else {\r\n                        this._logger.log(LogLevel.Debug, `Selecting transport '${HttpTransportType[transport]}'.`);\r\n                        try {\r\n                            this.features.reconnect = transport === HttpTransportType.WebSockets ? useStatefulReconnect : undefined;\r\n                            return this._constructTransport(transport);\r\n                        } catch (ex) {\r\n                            return ex;\r\n                        }\r\n                    }\r\n                } else {\r\n                    this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it does not support the requested transfer format '${TransferFormat[requestedTransferFormat]}'.`);\r\n                    return new Error(`'${HttpTransportType[transport]}' does not support ${TransferFormat[requestedTransferFormat]}.`);\r\n                }\r\n            } else {\r\n                this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it was disabled by the client.`);\r\n                return new DisabledTransportError(`'${HttpTransportType[transport]}' is disabled by the client.`, transport);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _isITransport(transport: any): transport is ITransport {\r\n        return transport && typeof (transport) === \"object\" && \"connect\" in transport;\r\n    }\r\n\r\n    private _stopConnection(error?: Error): void {\r\n        this._logger.log(LogLevel.Debug, `HttpConnection.stopConnection(${error}) called while in state ${this._connectionState}.`);\r\n\r\n        this.transport = undefined;\r\n\r\n        // If we have a stopError, it takes precedence over the error from the transport\r\n        error = this._stopError || error;\r\n        this._stopError = undefined;\r\n\r\n        if (this._connectionState === ConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is already in the disconnected state.`);\r\n            return;\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Connecting) {\r\n            this._logger.log(LogLevel.Warning, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is still in the connecting state.`);\r\n            throw new Error(`HttpConnection.stopConnection(${error}) was called while the connection is still in the connecting state.`);\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Disconnecting) {\r\n            // A call to stop() induced this call to stopConnection and needs to be completed.\r\n            // Any stop() awaiters will be scheduled to continue after the onclose callback fires.\r\n            this._stopPromiseResolver();\r\n        }\r\n\r\n        if (error) {\r\n            this._logger.log(LogLevel.Error, `Connection disconnected with error '${error}'.`);\r\n        } else {\r\n            this._logger.log(LogLevel.Information, \"Connection disconnected.\");\r\n        }\r\n\r\n        if (this._sendQueue) {\r\n            this._sendQueue.stop().catch((e) => {\r\n                this._logger.log(LogLevel.Error, `TransportSendQueue.stop() threw error '${e}'.`);\r\n            });\r\n            this._sendQueue = undefined;\r\n        }\r\n\r\n        this.connectionId = undefined;\r\n        this._connectionState = ConnectionState.Disconnected;\r\n\r\n        if (this._connectionStarted) {\r\n            this._connectionStarted = false;\r\n            try {\r\n                if (this.onclose) {\r\n                    this.onclose(error);\r\n                }\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `HttpConnection.onclose(${error}) threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _resolveUrl(url: string): string {\r\n        // startsWith is not supported in IE\r\n        if (url.lastIndexOf(\"https://\", 0) === 0 || url.lastIndexOf(\"http://\", 0) === 0) {\r\n            return url;\r\n        }\r\n\r\n        if (!Platform.isBrowser) {\r\n            throw new Error(`Cannot resolve '${url}'.`);\r\n        }\r\n\r\n        // Setting the url to the href propery of an anchor tag handles normalization\r\n        // for us. There are 3 main cases.\r\n        // 1. Relative path normalization e.g \"b\" -> \"http://localhost:5000/a/b\"\r\n        // 2. Absolute path normalization e.g \"/a/b\" -> \"http://localhost:5000/a/b\"\r\n        // 3. Networkpath reference normalization e.g \"//localhost:5000/a/b\" -> \"http://localhost:5000/a/b\"\r\n        const aTag = window.document.createElement(\"a\");\r\n        aTag.href = url;\r\n\r\n        this._logger.log(LogLevel.Information, `Normalizing '${url}' to '${aTag.href}'.`);\r\n        return aTag.href;\r\n    }\r\n\r\n    private _resolveNegotiateUrl(url: string): string {\r\n        const negotiateUrl = new URL(url);\r\n\r\n        if (negotiateUrl.pathname.endsWith('/')) {\r\n            negotiateUrl.pathname += \"negotiate\";\r\n        } else {\r\n            negotiateUrl.pathname += \"/negotiate\";\r\n        }\r\n        const searchParams = new URLSearchParams(negotiateUrl.searchParams);\r\n\r\n        if (!searchParams.has(\"negotiateVersion\")) {\r\n            searchParams.append(\"negotiateVersion\", this._negotiateVersion.toString());\r\n        }\r\n\r\n        if (searchParams.has(\"useStatefulReconnect\")) {\r\n            if (searchParams.get(\"useStatefulReconnect\") === \"true\") {\r\n                this._options._useStatefulReconnect = true;\r\n            }\r\n        } else if (this._options._useStatefulReconnect === true) {\r\n            searchParams.append(\"useStatefulReconnect\", \"true\");\r\n        }\r\n\r\n        negotiateUrl.search = searchParams.toString();\r\n\r\n        return negotiateUrl.toString();\r\n    }\r\n}\r\n\r\nfunction transportMatches(requestedTransport: HttpTransportType | undefined, actualTransport: HttpTransportType) {\r\n    return !requestedTransport || ((actualTransport & requestedTransport) !== 0);\r\n}\r\n\r\n/** @private */\r\nexport class TransportSendQueue {\r\n    private _buffer: any[] = [];\r\n    private _sendBufferedData: PromiseSource;\r\n    private _executing: boolean = true;\r\n    private _transportResult?: PromiseSource;\r\n    private _sendLoopPromise: Promise<void>;\r\n\r\n    constructor(private readonly _transport: ITransport) {\r\n        this._sendBufferedData = new PromiseSource();\r\n        this._transportResult = new PromiseSource();\r\n\r\n        this._sendLoopPromise = this._sendLoop();\r\n    }\r\n\r\n    public send(data: string | ArrayBuffer): Promise<void> {\r\n        this._bufferData(data);\r\n        if (!this._transportResult) {\r\n            this._transportResult = new PromiseSource();\r\n        }\r\n        return this._transportResult.promise;\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        this._executing = false;\r\n        this._sendBufferedData.resolve();\r\n        return this._sendLoopPromise;\r\n    }\r\n\r\n    private _bufferData(data: string | ArrayBuffer): void {\r\n        if (this._buffer.length && typeof(this._buffer[0]) !== typeof(data)) {\r\n            throw new Error(`Expected data to be of type ${typeof(this._buffer)} but was of type ${typeof(data)}`);\r\n        }\r\n\r\n        this._buffer.push(data);\r\n        this._sendBufferedData.resolve();\r\n    }\r\n\r\n    private async _sendLoop(): Promise<void> {\r\n        while (true) {\r\n            await this._sendBufferedData.promise;\r\n\r\n            if (!this._executing) {\r\n                if (this._transportResult) {\r\n                    this._transportResult.reject(\"Connection stopped.\");\r\n                }\r\n\r\n                break;\r\n            }\r\n\r\n            this._sendBufferedData = new PromiseSource();\r\n\r\n            const transportResult = this._transportResult!;\r\n            this._transportResult = undefined;\r\n\r\n            const data = typeof(this._buffer[0]) === \"string\" ?\r\n                this._buffer.join(\"\") :\r\n                TransportSendQueue._concatBuffers(this._buffer);\r\n\r\n            this._buffer.length = 0;\r\n\r\n            try {\r\n                await this._transport.send(data);\r\n                transportResult.resolve();\r\n            } catch (error) {\r\n                transportResult.reject(error);\r\n            }\r\n        }\r\n    }\r\n\r\n    private static _concatBuffers(arrayBuffers: ArrayBuffer[]): ArrayBuffer {\r\n        const totalLength = arrayBuffers.map((b) => b.byteLength).reduce((a, b) => a + b);\r\n        const result = new Uint8Array(totalLength);\r\n        let offset = 0;\r\n        for (const item of arrayBuffers) {\r\n            result.set(new Uint8Array(item), offset);\r\n            offset += item.byteLength;\r\n        }\r\n\r\n        return result.buffer;\r\n    }\r\n}\r\n\r\nclass PromiseSource {\r\n    private _resolver?: () => void;\r\n    private _rejecter!: (reason?: any) => void;\r\n    public promise: Promise<void>;\r\n\r\n    constructor() {\r\n        this.promise = new Promise((resolve, reject) => [this._resolver, this._rejecter] = [resolve, reject]);\r\n    }\r\n\r\n    public resolve(): void {\r\n        this._resolver!();\r\n    }\r\n\r\n    public reject(reason?: any): void {\r\n        this._rejecter!(reason);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AckMessage, CompletionMessage, HubMessage, IHubProtocol, InvocationMessage, MessageType, SequenceMessage, StreamItemMessage } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\n\r\nconst JSON_HUB_PROTOCOL_NAME: string = \"json\";\r\n\r\n/** Implements the JSON Hub Protocol. */\r\nexport class JsonHubProtocol implements IHubProtocol {\r\n\r\n    /** @inheritDoc */\r\n    public readonly name: string = JSON_HUB_PROTOCOL_NAME;\r\n    /** @inheritDoc */\r\n    public readonly version: number = 2;\r\n\r\n    /** @inheritDoc */\r\n    public readonly transferFormat: TransferFormat = TransferFormat.Text;\r\n\r\n    /** Creates an array of {@link @microsoft/signalr.HubMessage} objects from the specified serialized representation.\r\n     *\r\n     * @param {string} input A string containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    public parseMessages(input: string, logger: ILogger): HubMessage[] {\r\n        // The interface does allow \"ArrayBuffer\" to be passed in, but this implementation does not. So let's throw a useful error.\r\n        if (typeof input !== \"string\") {\r\n            throw new Error(\"Invalid input for JSON hub protocol. Expected a string.\");\r\n        }\r\n\r\n        if (!input) {\r\n            return [];\r\n        }\r\n\r\n        if (logger === null) {\r\n            logger = NullLogger.instance;\r\n        }\r\n\r\n        // Parse the messages\r\n        const messages = TextMessageFormat.parse(input);\r\n\r\n        const hubMessages = [];\r\n        for (const message of messages) {\r\n            const parsedMessage = JSON.parse(message) as HubMessage;\r\n            if (typeof parsedMessage.type !== \"number\") {\r\n                throw new Error(\"Invalid payload.\");\r\n            }\r\n            switch (parsedMessage.type) {\r\n                case MessageType.Invocation:\r\n                    this._isInvocationMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.StreamItem:\r\n                    this._isStreamItemMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Completion:\r\n                    this._isCompletionMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Ping:\r\n                    // Single value, no need to validate\r\n                    break;\r\n                case MessageType.Close:\r\n                    // All optional values, no need to validate\r\n                    break;\r\n                case MessageType.Ack:\r\n                    this._isAckMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Sequence:\r\n                    this._isSequenceMessage(parsedMessage);\r\n                    break;\r\n                default:\r\n                    // Future protocol changes can add message types, old clients can ignore them\r\n                    logger.log(LogLevel.Information, \"Unknown message type '\" + parsedMessage.type + \"' ignored.\");\r\n                    continue;\r\n            }\r\n            hubMessages.push(parsedMessage);\r\n        }\r\n\r\n        return hubMessages;\r\n    }\r\n\r\n    /** Writes the specified {@link @microsoft/signalr.HubMessage} to a string and returns it.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {string} A string containing the serialized representation of the message.\r\n     */\r\n    public writeMessage(message: HubMessage): string {\r\n        return TextMessageFormat.write(JSON.stringify(message));\r\n    }\r\n\r\n    private _isInvocationMessage(message: InvocationMessage): void {\r\n        this._assertNotEmptyString(message.target, \"Invalid payload for Invocation message.\");\r\n\r\n        if (message.invocationId !== undefined) {\r\n            this._assertNotEmptyString(message.invocationId, \"Invalid payload for Invocation message.\");\r\n        }\r\n    }\r\n\r\n    private _isStreamItemMessage(message: StreamItemMessage): void {\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for StreamItem message.\");\r\n\r\n        if (message.item === undefined) {\r\n            throw new Error(\"Invalid payload for StreamItem message.\");\r\n        }\r\n    }\r\n\r\n    private _isCompletionMessage(message: CompletionMessage): void {\r\n        if (message.result && message.error) {\r\n            throw new Error(\"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        if (!message.result && message.error) {\r\n            this._assertNotEmptyString(message.error, \"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for Completion message.\");\r\n    }\r\n\r\n    private _isAckMessage(message: AckMessage): void {\r\n        if (typeof message.sequenceId !== 'number') {\r\n            throw new Error(\"Invalid SequenceId for Ack message.\");\r\n        }\r\n    }\r\n\r\n    private _isSequenceMessage(message: SequenceMessage): void {\r\n        if (typeof message.sequenceId !== 'number') {\r\n            throw new Error(\"Invalid SequenceId for Sequence message.\");\r\n        }\r\n    }\r\n\r\n    private _assertNotEmptyString(value: any, errorMessage: string): void {\r\n        if (typeof value !== \"string\" || value === \"\") {\r\n            throw new Error(errorMessage);\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { DefaultReconnectPolicy } from \"./DefaultReconnectPolicy\";\r\nimport { HttpConnection } from \"./HttpConnection\";\r\nimport { HubConnection } from \"./HubConnection\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\nimport { IHubProtocol } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { IRetryPolicy } from \"./IRetryPolicy\";\r\nimport { IStatefulReconnectOptions } from \"./IStatefulReconnectOptions\";\r\nimport { HttpTransportType } from \"./ITransport\";\r\nimport { JsonHubProtocol } from \"./JsonHubProtocol\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { Arg, ConsoleLogger } from \"./Utils\";\r\n\r\nconst LogLevelNameMapping: {[k: string]: LogLevel} = {\r\n    trace: LogLevel.Trace,\r\n    debug: LogLevel.Debug,\r\n    info: LogLevel.Information,\r\n    information: LogLevel.Information,\r\n    warn: LogLevel.Warning,\r\n    warning: LogLevel.Warning,\r\n    error: LogLevel.Error,\r\n    critical: LogLevel.Critical,\r\n    none: LogLevel.None,\r\n};\r\n\r\nfunction parseLogLevel(name: string): LogLevel {\r\n    // Case-insensitive matching via lower-casing\r\n    // Yes, I know case-folding is a complicated problem in Unicode, but we only support\r\n    // the ASCII strings defined in LogLevelNameMapping anyway, so it's fine -anurse.\r\n    const mapping = LogLevelNameMapping[name.toLowerCase()];\r\n    if (typeof mapping !== \"undefined\") {\r\n        return mapping;\r\n    } else {\r\n        throw new Error(`Unknown log level: ${name}`);\r\n    }\r\n}\r\n\r\n/** A builder for configuring {@link @microsoft/signalr.HubConnection} instances. */\r\nexport class HubConnectionBuilder {\r\n    private _serverTimeoutInMilliseconds?: number;\r\n    private _keepAliveIntervalInMilliseconds ?: number;\r\n\r\n    /** @internal */\r\n    public protocol?: IHubProtocol;\r\n    /** @internal */\r\n    public httpConnectionOptions?: IHttpConnectionOptions;\r\n    /** @internal */\r\n    public url?: string;\r\n    /** @internal */\r\n    public logger?: ILogger;\r\n\r\n    /** If defined, this indicates the client should automatically attempt to reconnect if the connection is lost. */\r\n    /** @internal */\r\n    public reconnectPolicy?: IRetryPolicy;\r\n\r\n    private _statefulReconnectBufferSize?: number;\r\n\r\n    /** Configures console logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {LogLevel} logLevel The minimum level of messages to log. Anything at this level, or a more severe level, will be logged.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logLevel: LogLevel): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {ILogger} logger An object implementing the {@link @microsoft/signalr.ILogger} interface, which will be used to write all log messages.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logger: ILogger): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {string} logLevel A string representing a LogLevel setting a minimum level of messages to log.\r\n     *    See {@link https://learn.microsoft.com/aspnet/core/signalr/configuration#configure-logging|the documentation for client logging configuration} for more details.\r\n     */\r\n    public configureLogging(logLevel: string): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {LogLevel | string | ILogger} logging A {@link @microsoft/signalr.LogLevel}, a string representing a LogLevel, or an object implementing the {@link @microsoft/signalr.ILogger} interface.\r\n     *    See {@link https://learn.microsoft.com/aspnet/core/signalr/configuration#configure-logging|the documentation for client logging configuration} for more details.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logging: LogLevel | string | ILogger): HubConnectionBuilder;\r\n    public configureLogging(logging: LogLevel | string | ILogger): HubConnectionBuilder {\r\n        Arg.isRequired(logging, \"logging\");\r\n\r\n        if (isLogger(logging)) {\r\n            this.logger = logging;\r\n        } else if (typeof logging === \"string\") {\r\n            const logLevel = parseLogLevel(logging);\r\n            this.logger = new ConsoleLogger(logLevel);\r\n        } else {\r\n            this.logger = new ConsoleLogger(logging);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use HTTP-based transports to connect to the specified URL.\r\n     *\r\n     * The transport will be selected automatically based on what the server and client support.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use the specified HTTP-based transport to connect to the specified URL.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @param {HttpTransportType} transportType The specific transport to use.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string, transportType: HttpTransportType): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use HTTP-based transports to connect to the specified URL.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @param {IHttpConnectionOptions} options An options object used to configure the connection.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string, options: IHttpConnectionOptions): HubConnectionBuilder;\r\n    public withUrl(url: string, transportTypeOrOptions?: IHttpConnectionOptions | HttpTransportType): HubConnectionBuilder {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isNotEmpty(url, \"url\");\r\n\r\n        this.url = url;\r\n\r\n        // Flow-typing knows where it's at. Since HttpTransportType is a number and IHttpConnectionOptions is guaranteed\r\n        // to be an object, we know (as does TypeScript) this comparison is all we need to figure out which overload was called.\r\n        if (typeof transportTypeOrOptions === \"object\") {\r\n            this.httpConnectionOptions = { ...this.httpConnectionOptions, ...transportTypeOrOptions };\r\n        } else {\r\n            this.httpConnectionOptions = {\r\n                ...this.httpConnectionOptions,\r\n                transport: transportTypeOrOptions,\r\n            };\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use the specified Hub Protocol.\r\n     *\r\n     * @param {IHubProtocol} protocol The {@link @microsoft/signalr.IHubProtocol} implementation to use.\r\n     */\r\n    public withHubProtocol(protocol: IHubProtocol): HubConnectionBuilder {\r\n        Arg.isRequired(protocol, \"protocol\");\r\n\r\n        this.protocol = protocol;\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     * By default, the client will wait 0, 2, 10 and 30 seconds respectively before trying up to 4 reconnect attempts.\r\n     */\r\n    public withAutomaticReconnect(): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     *\r\n     * @param {number[]} retryDelays An array containing the delays in milliseconds before trying each reconnect attempt.\r\n     * The length of the array represents how many failed reconnect attempts it takes before the client will stop attempting to reconnect.\r\n     */\r\n    public withAutomaticReconnect(retryDelays: number[]): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     *\r\n     * @param {IRetryPolicy} reconnectPolicy An {@link @microsoft/signalR.IRetryPolicy} that controls the timing and number of reconnect attempts.\r\n     */\r\n    public withAutomaticReconnect(reconnectPolicy: IRetryPolicy): HubConnectionBuilder;\r\n    public withAutomaticReconnect(retryDelaysOrReconnectPolicy?: number[] | IRetryPolicy): HubConnectionBuilder {\r\n        if (this.reconnectPolicy) {\r\n            throw new Error(\"A reconnectPolicy has already been set.\");\r\n        }\r\n\r\n        if (!retryDelaysOrReconnectPolicy) {\r\n            this.reconnectPolicy = new DefaultReconnectPolicy();\r\n        } else if (Array.isArray(retryDelaysOrReconnectPolicy)) {\r\n            this.reconnectPolicy = new DefaultReconnectPolicy(retryDelaysOrReconnectPolicy);\r\n        } else {\r\n            this.reconnectPolicy = retryDelaysOrReconnectPolicy;\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Configures {@link @microsoft/signalr.HubConnection.serverTimeoutInMilliseconds} for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withServerTimeout(milliseconds: number): HubConnectionBuilder {\r\n        Arg.isRequired(milliseconds, \"milliseconds\");\r\n\r\n        this._serverTimeoutInMilliseconds = milliseconds;\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Configures {@link @microsoft/signalr.HubConnection.keepAliveIntervalInMilliseconds} for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withKeepAliveInterval(milliseconds: number): HubConnectionBuilder {\r\n        Arg.isRequired(milliseconds, \"milliseconds\");\r\n\r\n        this._keepAliveIntervalInMilliseconds = milliseconds;\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Enables and configures options for the Stateful Reconnect feature.\r\n     *\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withStatefulReconnect(options?: IStatefulReconnectOptions): HubConnectionBuilder {\r\n        if (this.httpConnectionOptions === undefined) {\r\n            this.httpConnectionOptions = {};\r\n        }\r\n        this.httpConnectionOptions._useStatefulReconnect = true;\r\n\r\n        this._statefulReconnectBufferSize = options?.bufferSize;\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Creates a {@link @microsoft/signalr.HubConnection} from the configuration options specified in this builder.\r\n     *\r\n     * @returns {HubConnection} The configured {@link @microsoft/signalr.HubConnection}.\r\n     */\r\n    public build(): HubConnection {\r\n        // If httpConnectionOptions has a logger, use it. Otherwise, override it with the one\r\n        // provided to configureLogger\r\n        const httpConnectionOptions = this.httpConnectionOptions || {};\r\n\r\n        // If it's 'null', the user **explicitly** asked for null, don't mess with it.\r\n        if (httpConnectionOptions.logger === undefined) {\r\n            // If our logger is undefined or null, that's OK, the HttpConnection constructor will handle it.\r\n            httpConnectionOptions.logger = this.logger;\r\n        }\r\n\r\n        // Now create the connection\r\n        if (!this.url) {\r\n            throw new Error(\"The 'HubConnectionBuilder.withUrl' method must be called before building the connection.\");\r\n        }\r\n        const connection = new HttpConnection(this.url, httpConnectionOptions);\r\n\r\n        return HubConnection.create(\r\n            connection,\r\n            this.logger || NullLogger.instance,\r\n            this.protocol || new JsonHubProtocol(),\r\n            this.reconnectPolicy,\r\n            this._serverTimeoutInMilliseconds,\r\n            this._keepAliveIntervalInMilliseconds,\r\n            this._statefulReconnectBufferSize);\r\n    }\r\n}\r\n\r\nfunction isLogger(logger: any): logger is ILogger {\r\n    return logger.log !== undefined;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// This is where we add any polyfills we'll need for the browser. It is the entry module for browser-specific builds.\r\n\r\n// Copy from Array.prototype into Uint8Array to polyfill on IE. It's OK because the implementations of indexOf and slice use properties\r\n// that exist on Uint8Array with the same name, and JavaScript is magic.\r\n// We make them 'writable' because the Buffer polyfill messes with it as well.\r\nif (!Uint8Array.prototype.indexOf) {\r\n    Object.defineProperty(Uint8Array.prototype, \"indexOf\", {\r\n        value: Array.prototype.indexOf,\r\n        writable: true,\r\n    });\r\n}\r\nif (!Uint8Array.prototype.slice) {\r\n    Object.defineProperty(Uint8Array.prototype, \"slice\", {\r\n        // wrap the slice in Uint8Array so it looks like a Uint8Array.slice call\r\n        // eslint-disable-next-line object-shorthand\r\n        value: function(start?: number, end?: number) { return new Uint8Array(Array.prototype.slice.call(this, start, end)); },\r\n        writable: true,\r\n    });\r\n}\r\nif (!Uint8Array.prototype.forEach) {\r\n    Object.defineProperty(Uint8Array.prototype, \"forEach\", {\r\n        value: Array.prototype.forEach,\r\n        writable: true,\r\n    });\r\n}\r\n\r\nexport * from \"./index\";\r\n"], "names": ["root", "factory", "self", "__webpack_require__", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "g", "globalThis", "this", "Function", "e", "window", "obj", "prop", "prototype", "hasOwnProperty", "call", "r", "Symbol", "toStringTag", "value", "LogLevel", "HttpError", "Error", "constructor", "errorMessage", "statusCode", "trueProto", "super", "__proto__", "TimeoutError", "AbortError", "UnsupportedTransportError", "message", "transport", "errorType", "DisabledTransportError", "FailedToStartTransportError", "FailedToNegotiateWithServerError", "AggregateErrors", "innerErrors", "HttpResponse", "statusText", "content", "HttpClient", "url", "options", "send", "method", "post", "delete", "getCookieString", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "log", "_logLevel", "_message", "instance", "VERSION", "Arg", "static", "val", "name", "match", "values", "Platform", "<PERSON><PERSON><PERSON><PERSON>", "isNode", "document", "isWebWorker", "isReactNative", "process", "release", "getDataDetail", "data", "<PERSON><PERSON><PERSON><PERSON>", "detail", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "byteLength", "view", "Uint8Array", "str", "for<PERSON>ach", "num", "toString", "substr", "length", "formatA<PERSON>y<PERSON>uffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "async", "sendMessage", "logger", "transportName", "httpClient", "headers", "getUserAgentHeader", "Trace", "logMessageContent", "responseType", "response", "timeout", "withCredentials", "SubjectSubscription", "subject", "observer", "_subject", "_observer", "dispose", "index", "observers", "indexOf", "splice", "cancelCallback", "catch", "_", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minimumLogLevel", "_minLevel", "out", "console", "logLevel", "msg", "Date", "toISOString", "Critical", "error", "Warning", "warn", "Information", "info", "userAgentHeaderName", "constructUserAgent", "getOsName", "getRuntimeVersion", "version", "os", "runtime", "runtimeVersion", "userAgent", "majorAndMinor", "split", "platform", "versions", "node", "getErrorString", "stack", "FetchHttpClient", "_logger", "fetch", "requireFunc", "_jar", "<PERSON><PERSON><PERSON><PERSON>", "_fetchType", "bind", "getGlobalThis", "AbortController", "_abortControllerType", "request", "abortSignal", "aborted", "abortController", "<PERSON>ab<PERSON>", "abort", "timeoutId", "msTimeout", "setTimeout", "undefined", "body", "cache", "credentials", "mode", "redirect", "signal", "clearTimeout", "ok", "deserializeContent", "status", "payload", "cookies", "getCookies", "c", "join", "arrayBuffer", "text", "XhrHttpClient", "Promise", "reject", "resolve", "xhr", "XMLHttpRequest", "open", "setRequestHeader", "keys", "header", "onload", "responseText", "onerror", "ontimeout", "DefaultHttpClient", "_httpClient", "TextMessageFormat", "output", "RecordSeparator", "input", "messages", "pop", "RecordSeparatorCode", "String", "fromCharCode", "HandshakeProtocol", "writeHandshakeRequest", "handshakeRequest", "write", "JSON", "stringify", "parseHandshakeResponse", "messageData", "remainingData", "binaryData", "separatorIndex", "responseLength", "apply", "Array", "slice", "buffer", "textData", "substring", "parse", "type", "MessageType", "HubConnectionState", "Subject", "next", "item", "err", "complete", "subscribe", "push", "MessageBuffer", "protocol", "connection", "bufferSize", "_bufferSize", "_messages", "_totalMessageCount", "_waitForSequenceMessage", "_nextReceivingSequenceId", "_latestReceivedSequenceId", "_bufferedByteCount", "_reconnectInProgress", "_protocol", "_connection", "serializedMessage", "writeMessage", "backpressurePromise", "_isInvocationMessage", "backpressurePromiseResolver", "backpressurePromiseRejector", "BufferedItem", "_disconnected", "_ack", "ackMessage", "newestAckedMessage", "element", "_id", "sequenceId", "_resolver", "_shouldProcessMessage", "Sequence", "currentId", "_ackTimer", "_resetSequence", "stop", "_dispose", "_rejector", "Invocation", "StreamItem", "Completion", "StreamInvocation", "CancelInvocation", "Close", "<PERSON>", "Ack", "_ack<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "id", "resolver", "rejector", "HubConnection", "reconnectPolicy", "serverTimeoutInMilliseconds", "keepAliveIntervalInMilliseconds", "statefulReconnectBufferSize", "_nextKeepAlive", "_freezeEventListener", "isRequired", "_statefulReconnectBufferSize", "_reconnectPolicy", "_handshakeProtocol", "onreceive", "_processIncomingData", "onclose", "_connectionClosed", "_callbacks", "_methods", "_closedCallbacks", "_reconnectingCallbacks", "_reconnectedCallbacks", "_invocationId", "_receivedHandshakeResponse", "_connectionState", "Disconnected", "_connectionStarted", "_cachedPingMessage", "state", "connectionId", "baseUrl", "Reconnecting", "start", "_startPromise", "_startWithStateTransitions", "Connecting", "Debug", "_startInternal", "addEventListener", "Connected", "_stopDuringStartError", "handshakePromise", "_handshakeResolver", "_handshake<PERSON><PERSON><PERSON><PERSON>", "transferFormat", "features", "reconnect", "_sendMessage", "_cleanupTimeout", "_resetTimeoutPeriod", "_resetKeepAliveInterval", "_messageBuffer", "disconnected", "resend", "_resend", "inherentKeepAlive", "_cleanupPingTimer", "startPromise", "_stopPromise", "_stopInternal", "Disconnecting", "_reconnectDelayHandle", "_completeClose", "_sendCloseMessage", "_sendWithProtocol", "_createCloseMessage", "stream", "methodName", "args", "streams", "streamIds", "_replaceStreamingParams", "invocationDescriptor", "_createStreamInvocation", "promiseQueue", "cancelInvocation", "_createCancelInvocation", "invocationId", "then", "invocationEvent", "_launchStreams", "_send", "sendPromise", "_createInvocation", "invoke", "result", "on", "newMethod", "toLowerCase", "off", "handlers", "removeIdx", "callback", "onreconnecting", "onreconnected", "_processHandshakeResponse", "parseMessages", "_invokeClientMethod", "allowReconnect", "responseMessage", "getTime", "_timeoutHandle", "serverTimeout", "_pingServerHandle", "nextPing", "invocationMessage", "target", "methods", "_createCompletionMessage", "methodsCopy", "expectsResponse", "res", "exception", "completionMessage", "m", "prevRes", "arguments", "_cancelCallbacksWithError", "_reconnect", "removeEventListener", "reconnectStartTime", "now", "previousReconnectAttempts", "retryError", "nextRetryDelay", "_getNextRetryDelay", "previousRetryCount", "elapsedMilliseconds", "retryReason", "nextRetryDelayInMilliseconds", "callbacks", "nonblocking", "streamId", "_createStreamItemMessage", "i", "argument", "_isObservable", "arg", "DEFAULT_RETRY_DELAYS_IN_MILLISECONDS", "DefaultReconnectPolicy", "re<PERSON><PERSON><PERSON><PERSON>", "_retryD<PERSON>ys", "retryContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Authorization", "<PERSON><PERSON>", "AccessTokenHttpClient", "innerClient", "accessTokenFactory", "_innerClient", "_accessTokenFactory", "allowRetry", "_accessToken", "_setAuthorizationHeader", "HttpTransportType", "TransferFormat", "_isAborted", "LongPollingTransport", "pollAborted", "_pollAbort", "_options", "_running", "isIn", "_url", "Binary", "pollOptions", "pollUrl", "_closeError", "_receiving", "_poll", "_raiseOnClose", "deleteOptions", "logMessage", "ServerSentEventsTransport", "accessToken", "encodeURIComponent", "eventSource", "opened", "Text", "EventSource", "onmessage", "_close", "onopen", "_eventSource", "close", "WebSocketTransport", "webSocketConstructor", "_logMessageContent", "_webSocketConstructor", "_headers", "token", "webSocket", "replace", "binaryType", "_event", "_webSocket", "event", "ErrorEvent", "readyState", "OPEN", "_isCloseEvent", "<PERSON><PERSON><PERSON>", "code", "reason", "HttpConnection", "_stopPromiseResolver", "_negotiateVersion", "_resolveUrl", "webSocketModule", "eventSourceModule", "WebSocket", "_startInternalPromise", "_sendQueue", "TransportSendQueue", "_stopError", "_stopConnection", "skipNegotiation", "WebSockets", "_constructTransport", "_startTransport", "negotiateResponse", "redirects", "_getNegotiationResponse", "ProtocolVersion", "_createTransport", "negotiateUrl", "_resolveNegotiateUrl", "negotiateVersion", "connectionToken", "useStatefulReconnect", "_useStatefulReconnect", "_createConnectUrl", "requestedTransport", "requestedTransferFormat", "connectUrl", "_isITransport", "transportExceptions", "transports", "availableTransports", "negotiate", "endpoint", "transportOrError", "_resolveTransportOrError", "ex", "ServerSentEvents", "LongPolling", "callStop", "connect", "actualTransport", "transportMatches", "transferFormats", "map", "s", "lastIndexOf", "aTag", "createElement", "href", "URL", "pathname", "endsWith", "searchParams", "URLSearchParams", "has", "append", "search", "_transport", "_buffer", "_executing", "_sendBufferedData", "PromiseSource", "_transportResult", "_sendLoopPromise", "_sendLoop", "_bufferData", "promise", "transportResult", "_concatBuffers", "arrayBuffers", "totalLength", "b", "reduce", "a", "offset", "set", "_rejecter", "JsonHubProtocol", "hubMessages", "parsedMessage", "_isStreamItemMessage", "_isCompletionMessage", "_isAckMessage", "_isSequenceMessage", "_assertNotEmptyString", "LogLevelNameMapping", "trace", "debug", "information", "warning", "critical", "none", "None", "HubConnectionBuilder", "configureLogging", "logging", "mapping", "parseLogLevel", "withUrl", "transportTypeOrOptions", "isNotEmpty", "httpConnectionOptions", "withHubProtocol", "withAutomaticReconnect", "retryDelaysOrReconnectPolicy", "isArray", "withServerTimeout", "milliseconds", "_serverTimeoutInMilliseconds", "withKeepAliveInterval", "_keepAliveIntervalInMilliseconds", "withStatefulReconnect", "build", "create", "writable", "end", "module", "define", "amd"], "sourceRoot": ""}