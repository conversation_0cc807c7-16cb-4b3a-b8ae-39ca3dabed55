// Common utility functions and logic for PR Data component
import { Injectable } from '@angular/core';
import { ConfigService } from '../../services/config.service';

@Injectable({ providedIn: 'root' })
export class PrDataCommonService {
  constructor(private configService: ConfigService) {}
  // --- Legacy PR count methods (kept for backward compatibility) ---
  getActivePRCount(prList: any[]): number {
    console.warn('Deprecated: Use fetchPrStatusCounts() instead');
    return prList.filter((pr) => pr.status === 'active').length;
  }
  getCompletedPRCount(prList: any[]): number {
    console.warn('Deprecated: Use fetchPrStatusCounts() instead');
    return prList.filter((pr) => pr.status === 'completed').length;
  }
  // Store/retrieve max scroll count in localStorage
  saveMaxScrollCount(count: number) {
    localStorage.setItem('pr-max-scroll-count', count.toString());
  }
  getMaxScrollCount(): number {
    const val = localStorage.getItem('pr-max-scroll-count');
    return val ? parseInt(val, 10) : 0;
  }
  updateScrollCountAfterLoad(prList: any[]) {
    const currentCount = prList.length;
    const lastMax = this.getMaxScrollCount();
    if (currentCount > lastMax) {
      this.saveMaxScrollCount(currentCount);
    }
  }
  // Pipeline status helpers
  getPipelineStatusIcon(status: number): string {
    switch (status) {
      case this.configService.PIPELINE_STATUS.IN_PROGRESS:
        return 'hourglass_empty'; // In progress
      case this.configService.PIPELINE_STATUS.SUCCEEDED:
        return 'check_circle'; // Success
      case this.configService.PIPELINE_STATUS.FAILED:
        return 'cancel'; // Failed
      default:
        return 'help_outline'; // Unknown
    }
  }
  getPipelineStatusClass(status: number): string {
    if (status === this.configService.PIPELINE_STATUS.SUCCEEDED) return 'status-success'; // Success
    if (status === this.configService.PIPELINE_STATUS.FAILED) return 'status-failed'; // Failed/Expired
    if (status === this.configService.PIPELINE_STATUS.IN_PROGRESS) return 'status-pending'; // In progress
    return 'status-unknown'; // Unknown status
  }
  // Add more shared logic as needed...

  // --- Pipeline policy helpers ---
  processPolicies(pr: any, policies: any[]) {
    if (!policies || !Array.isArray(policies) || policies.length === 0) {
      pr.pipelinePolicies = [];
      pr.activePolicy = null;
      pr.pipelineStatus = 'No pipeline found';
      pr.pipelineStatusDisplay = 0;
      return;
    }
    // Sort: running (status 1) first, then by evaluationId (newest first)
    policies.sort((a: any, b: any) => {
      if (a.displayStatus === 1 && b.displayStatus !== 1) return -1;
      if (b.displayStatus === 1 && a.displayStatus !== 1) return 1;
      if (a.evaluationId && b.evaluationId) {
        return b.evaluationId - a.evaluationId;
      }
      return 0;
    });
    pr.pipelinePolicies = policies;    // If all policies are succeeded, show the last one and set a summary
    const allSucceeded =
      policies.length > 0 && policies.every((p: any) => p.displayStatus === this.configService.PIPELINE_STATUS.SUCCEEDED);
    if (allSucceeded) {
      pr.activePolicy = policies[policies.length - 1];
      pr.pipelineStatus = 'All checks passed';
      pr.pipelineStatusDisplay = this.configService.PIPELINE_STATUS.SUCCEEDED;
    } else {
      pr.activePolicy = policies[0] || null;
      if (pr.activePolicy) {
        pr.buildId = pr.activePolicy.buildId;
        pr.pipelineStatus = pr.activePolicy.displayText;
        pr.pipelineStatusDisplay = pr.activePolicy.displayStatus;
      } else {
        pr.pipelineStatus = 'No pipeline found';
        pr.pipelineStatusDisplay = this.configService.PIPELINE_STATUS.UNKNOWN;
      }
    }
  }  getPrimaryPipelinePolicy(pr: any): any {
    if (!pr.pipelinePolicies || !pr.pipelinePolicies.length) return undefined;
    // 1. In progress
    const inProgress = pr.pipelinePolicies.find(
      (p: any) => p.displayStatus === this.configService.PIPELINE_STATUS.IN_PROGRESS
    );
    if (inProgress) return inProgress;
    // 2. Failed
    const failed = pr.pipelinePolicies.find(
      (p: any) => p.displayStatus === this.configService.PIPELINE_STATUS.FAILED
    );
    if (failed) return failed;
    // 3. All succeeded
    const allSucceeded = pr.pipelinePolicies.every(
      (p: any) => p.displayStatus === this.configService.PIPELINE_STATUS.SUCCEEDED
    );
    if (allSucceeded)
      return pr.pipelinePolicies[pr.pipelinePolicies.length - 1];
    // 4. Fallback: first policy
    return pr.pipelinePolicies[0];
  }

  // --- Filter and debounce logic ---
  setupFilterPredicate(dataSource: any, formatBranchName: (name: string) => string) {
    dataSource.filterPredicate = (data: any, filter: string) => {
      if (!filter) return true;

      // Split by spaces to allow searching for multiple terms
      const searchTerms = filter
        .toLowerCase()
        .split(' ')
        .filter((term) => term.length > 0);
      if (searchTerms.length === 0) return true;

      // Search in multiple fields
      const searchableFields = [
        data.pullRequestId?.toString() || '',
        data.title?.toLowerCase() || '',
        data.createdBy?.displayName?.toLowerCase() || '',
        data.status?.toLowerCase() || '',
        formatBranchName(data.sourceRefName || '').toLowerCase(),
        formatBranchName(data.targetRefName || '').toLowerCase(),
        data.activeComments?.toString() || '0',
      ];

      // Check if all search terms are found in at least one of the fields
      return searchTerms.every((term) =>
        searchableFields.some((field) => field.includes(term))
      );
    };
  }

  // Format API filter parameters
  formatFilterParams(currentFilter: string, statusFilter: string, searchMode: string): string {
    let filterParam = '';
    
    // Apply status filter if not 'all'
    if (statusFilter && statusFilter !== 'all') {
      filterParam = `status eq '${statusFilter}'`;
    }

    // Add search filter if we have one and using server-side search
    if (currentFilter && searchMode === 'server') {
      const searchFields = [
        'title',
        'pullRequestId',
        'createdBy/displayName',
        'status',
        'sourceRefName',
        'targetRefName',
      ];
      const searchFilters = searchFields.map(
        (field) =>
          `contains(tolower(${field}), '${encodeURIComponent(
            currentFilter.toLowerCase()
          )}')`
      );
      filterParam = filterParam
        ? `(${filterParam}) and (${searchFilters.join(' or ')})`
        : searchFilters.join(' or ');
    }
    
    return filterParam;
  }

  // Format date parameters for API
  formatDateParams(startDate: Date | null, endDate: Date | null): { minTime?: string, maxTime?: string } {
    let minTime: string | undefined;
    let maxTime: string | undefined;
    
    if (startDate) {
      // Format to ISO string: yyyy-MM-ddTHH:mm:ss.fffZ
      minTime = startDate.toISOString();
    }
    
    if (endDate) {
      // Set the time to end of day for the end date
      const endDateWithTime = new Date(endDate);
      endDateWithTime.setHours(23, 59, 59, 999);
      maxTime = endDateWithTime.toISOString();
    }
    
    return { minTime, maxTime };
  }
}
