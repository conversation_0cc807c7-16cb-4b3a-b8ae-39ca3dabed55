import { environment } from '../../environments/environment';

// Utility function to replace @<GUID> tokens with real display names using fetch
const guidRegex = /@<([0-9a-fA-F\-]{36})>/g;

export async function testReplaceGuids(content: string, patToken: string): Promise<string> {
  if (!content) return '';
  const guids = extractGuids(content);
  if (guids.length === 0) return content;

  // Get unique GUIDs
  const uniqueGuids = Array.from(new Set(guids.map(g => g.toLowerCase())));
  const guidToName: Record<string, string> = {};

  // Fetch display names for each GUID
  await Promise.all(uniqueGuids.map(async guid => {
    const url = `${environment.apiUrl}/IdentityPicker/GetIdentities?userUuid=${guid}&patToken=${patToken}`;
    try {
      const res = await fetch(url);
      const data = await res.json();
      if (data && data.data && data.data[0] && data.data[0].displayName) {
        guidToName[guid] = data.data[0].displayName;
      } else {
        guidToName[guid] = `@<${guid}>`;
      }
    } catch {
      guidToName[guid] = `@<${guid}>`;
    }
  }));

  // Replace using a regex callback for correct mapping
  const replaced = content.replace(guidRegex, (match, guid) => {
    return guidToName[guid.toLowerCase()] || match;
  });
  return replaced;
}

export function extractGuids(inputString: string): string[] {
  const matches = [...inputString.matchAll(guidRegex)].map(m => m[1]);
  return matches || [];
}
