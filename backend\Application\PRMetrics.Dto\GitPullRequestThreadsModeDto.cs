﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application
{
    /// <summary>
    /// Represents the time metrics for Git pull request threads, including the total time spent in published and draft states, 
    /// as well as the state events related to these threads.
    /// </summary>
    public class GitPullRequestThreadsModeDto
    {
        /// <summary>
        /// Gets or sets the total time spent in the **published** state for the pull request threads, in hours (or another unit).
        /// This represents the accumulated time from when a pull request enters the published state until it moves to another state.
        /// </summary>
        //public double TotalPublishedTime { get; set; }

        /// <summary>
        /// Gets or sets the total time spent in the **draft** state for the pull request threads, in hours (or another unit).
        /// This represents the accumulated time that a pull request remains in the draft state before being published or closed.
        /// </summary>
       // public double TotalDraftTime { get; set; }

        /// <summary>
        /// Gets or sets the list of state events that occurred during the lifecycle of the pull request threads.
        /// Each state event represents a transition or change in the state of the pull request (e.g., from draft to published).
        /// </summary>
        public List<StateEventDto> StateEvents { get; set; } = new List<StateEventDto>();
    }
}
