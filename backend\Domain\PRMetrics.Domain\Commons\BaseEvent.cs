﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;

namespace RIB.PRMetrics.Domain.Commons
{
    /// <summary>
    /// Abstract class representing a base event that implements the INotification interface from MediatR.
    /// Events that need to be published and handled in the MediatR pipeline can inherit from this class.
    /// </summary>
    public abstract class BaseEvent : INotification
    {
        // This class serves as a base class for all events in the system that will be published to MediatR
        // and consumed by any registered event handlers. Inheriting from INotification means that this class
        // will be treated as an event, and MediatR can publish it to any subscribers (handlers) that are listening 
        // for such events.
    }
}
