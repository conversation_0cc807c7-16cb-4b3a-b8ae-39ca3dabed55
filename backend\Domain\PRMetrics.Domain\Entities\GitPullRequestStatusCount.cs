﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities
{
    /// <summary>
    /// Represents the count of pull requests in a repository for a specific status.
    /// </summary>
    public class GitPullRequestStatusCount
    {
        /// <summary>
        /// Gets or sets the name of the Azure DevOps project.
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// Gets or sets the name of the Git repository.
        /// </summary>
        public string RepositoryName { get; set; }

        /// <summary>
        /// Gets or sets the status of the pull requests (e.g., active, completed, abandoned).
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Gets or sets the total number of pull requests matching the specified status.
        /// </summary>
        public uint Value { get; set; }
    }

}
