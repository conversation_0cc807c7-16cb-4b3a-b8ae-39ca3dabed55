﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using Microsoft.AspNetCore.Mvc;
using RIB.PRMetrics.Application.UseCases.ContributionsHierarchyQuery.Queries.GetContributionsHierarchy;

namespace RIB.PRMetrics.WebApi.Controllers
{
    /// <summary>
    /// Controller to handle API requests related to Contributions Hierarchy Queries.
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ContributionsHierarchyQueryController : ControllerBase
    {
        private readonly IMediator _mediator;

        /// <summary>
        /// Initializes a new instance of the <see cref="ContributionsHierarchyQueryController"/> class.
        /// </summary>
        /// <param name="mediator">The mediator used to send queries to the application layer.</param>
        public ContributionsHierarchyQueryController(IMediator mediator)
        {
            _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
        }

        /// <summary>
        /// Endpoint to get the contributions hierarchy based on the provided query parameters.
        /// </summary>
        /// <param name="query">The query parameters to filter the contributions hierarchy request.</param>
        /// <returns>An IActionResult containing the response from the application layer.</returns>
        [HttpGet("GetContributionsHierarchyQuery")]
        public async Task<IActionResult> GetContributionsHierarchyQuery([FromQuery] GetContributionsHierarchyQuery query)
        {
            // Send the query to the mediator to handle the request.
            var response = await _mediator.Send(query);

            // Return a successful response if the query was successful, otherwise return a BadRequest.
            if (response.Succcess)
            {
                return Ok(response);
            }

            return BadRequest(response);
        }
    }
}
