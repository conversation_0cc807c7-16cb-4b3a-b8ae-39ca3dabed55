// Utility functions for date/time formatting in PR analytics

/**
 * Returns a human-readable timelapse string for a PR
 * @param creationDate The creation date string
 * @param status The PR status string
 * @param closedDate The closed date string (optional)
 */
export function getTimelapseString(creationDate: string, status?: string, closedDate?: string): string {
  if (!creationDate) return '';
  const created = new Date(creationDate);
  let end: Date;
  if (status && status.toLowerCase() === 'completed' && closedDate) {
    end = new Date(closedDate);
  } else {
    end = new Date();
  }
  let diff = Math.max(0, end.getTime() - created.getTime());
  const hours = Math.floor(diff / (1000 * 60 * 60));
  diff -= hours * 1000 * 60 * 60;
  const mins = Math.floor(diff / (1000 * 60));
  diff -= mins * 1000 * 60;
  const secs = Math.floor(diff / 1000);
  let result = '';
  if (hours > 0) result += hours + 'h ';
  if (mins > 0 || hours > 0) result += mins + 'm ';
  result += secs + 's';
  return result.trim();
}
 
export function calculateWorkingTime(startDateStr: string, endDateStr: string) {
    // Ensure UTC format
    if (!startDateStr.endsWith('Z')) startDateStr += 'Z';
    if (!endDateStr.endsWith('Z')) endDateStr += 'Z';

    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);

    const WORK_START_HOUR = 9;
    const WORK_START_MINUTE = 30;
    const WORK_END_HOUR = 18;
    const WORK_END_MINUTE = 30;
    const WORK_DAY_HOURS = 8;
    const MS_PER_HOUR = 3600000;

    const BREAK_START_HOUR = 13;
    const BREAK_END_HOUR = 14;

    if (startDate > endDate) {
        return {
            totalWorkingHours: 0,
            totalTime: { totalDays: 0, remainingHours: 0, remainingMinutes: 0 }
        };
    }

    const cloneDate = (d: Date) => new Date(d.getTime());

    const isWeekday = (date: Date) => {
        const day = date.getDay();
        return day !== 0 && day !== 6;
    };

    const isSameDate = (d1: Date, d2: Date) =>
        d1.getFullYear() === d2.getFullYear() &&
        d1.getMonth() === d2.getMonth() &&
        d1.getDate() === d2.getDate();

    const getPartialHours = (date: Date, from: Date, to: Date): number => {
        if (!isWeekday(date)) return 0;

        const workStart = new Date(date);
        workStart.setHours(WORK_START_HOUR, WORK_START_MINUTE, 0, 0);
        const workEnd = new Date(date);
        workEnd.setHours(WORK_END_HOUR, WORK_END_MINUTE, 0, 0);

        const breakStart = new Date(date);
        breakStart.setHours(BREAK_START_HOUR, 0, 0, 0);
        const breakEnd = new Date(date);
        breakEnd.setHours(BREAK_END_HOUR, 0, 0, 0);

        const start = new Date(Math.max(from.getTime(), workStart.getTime()));
        const end = new Date(Math.min(to.getTime(), workEnd.getTime()));

        if (end <= start) return 0;

        let overlapWithBreak = 0;
        if (start < breakEnd && end > breakStart) {
            overlapWithBreak = Math.min(end.getTime(), breakEnd.getTime()) - Math.max(start.getTime(), breakStart.getTime());
        }

        return (end.getTime() - start.getTime() - overlapWithBreak) / MS_PER_HOUR;
    };

    let totalWorkingHours = 0;

    if (isSameDate(startDate, endDate)) {
        totalWorkingHours = getPartialHours(startDate, startDate, endDate);
    } else {
        const startPartial = getPartialHours(startDate, startDate, new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate(), WORK_END_HOUR, WORK_END_MINUTE));
        const endPartial = getPartialHours(endDate, new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate(), WORK_START_HOUR, WORK_START_MINUTE), endDate);

        const nextDay = cloneDate(startDate);
        nextDay.setDate(nextDay.getDate() + 1);
        nextDay.setHours(0, 0, 0, 0);

        const endMidnight = cloneDate(endDate);
        endMidnight.setHours(0, 0, 0, 0);

        const daysBetween = Math.floor((endMidnight.getTime() - nextDay.getTime()) / (1000 * 60 * 60 * 24));

        const fullWeeks = Math.floor(daysBetween / 7);
        let weekdays = fullWeeks * 5;

        for (let i = 0; i < daysBetween % 7; i++) {
            const temp = new Date(nextDay);
            temp.setDate(temp.getDate() + i);
            if (isWeekday(temp)) weekdays++;
        }

        totalWorkingHours = startPartial + endPartial + (weekdays * WORK_DAY_HOURS);
    }

    const totalDays = Math.floor(totalWorkingHours / WORK_DAY_HOURS);
    const remainingHours = Math.floor(totalWorkingHours % WORK_DAY_HOURS);
    const remainingMinutes = Math.round((totalWorkingHours % 1) * 60);
     let parts: string[] = [];
    if (totalDays > 0 && totalDays !== 1) parts.push(`${totalDays} days`);
    if (totalDays === 1 ) parts.push(`${totalDays} day`);
    if (remainingHours > 0 && remainingHours !== 1) parts.push(`${remainingHours} hours`);
    if (remainingHours === 1) parts.push(`${remainingHours} hour`);
    if (remainingMinutes > 0 || parts.length === 0) parts.push(`${remainingMinutes} minutes`);
    const totalWorkingHoursFormattedInWord = parts.join(', ');
    (`Total working hours: ${totalWorkingHours.toFixed(2)} hours`);
    (`Total time: ${totalDays} days, ${remainingHours} hours, ${remainingMinutes} minutes`);

    return {
        totalWorkingHours,
        totalWorkingHoursFormattedInWord,
        totalTime: { totalDays, remainingHours, remainingMinutes }
    };
}
 
