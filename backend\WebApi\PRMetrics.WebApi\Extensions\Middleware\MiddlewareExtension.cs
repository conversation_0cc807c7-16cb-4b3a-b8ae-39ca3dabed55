﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace  RIB.PRMetrics.WebApi.Extensions.Middleware
{
    /// <summary>
    /// Extension methods for adding custom middleware to the application's request pipeline.
    /// </summary>
    public static class MiddlewareExtension
    {
        /// <summary>
        /// Adds the <see cref="ValidationMiddleware"/> to the application's request pipeline.
        /// This middleware is responsible for handling validation logic for incoming requests.
        /// </summary>
        /// <param name="app">The application builder to configure the middleware pipeline.</param>
        /// <returns>The updated application builder with the middleware added.</returns>
        public static IApplicationBuilder AddMiddleware(this IApplicationBuilder app)
        {
            return app.UseMiddleware<ValidationMiddleware>(); // Adds ValidationMiddleware
        }
    }
}
