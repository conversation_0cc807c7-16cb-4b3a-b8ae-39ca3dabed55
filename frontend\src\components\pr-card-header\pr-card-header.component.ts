import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

/**
 * Reusable PR card header component with title, subtitle and PR counts
 */
@Component({
  selector: 'app-pr-card-header',
  standalone: true,  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './pr-card-header.component.html',
  styleUrl: './pr-card-header.component.scss'
})
export class PrCardHeaderComponent {
  @Input() title: string = 'Pull Request Analysis';
  @Input() subtitle: string = '';
  @Input() totalCount: number = 0;
  @Input() completedCount: number = 0;
  @Input() abandonedCount: number = 0;  @Input() loadingActiveCount: boolean = false;
  @Input() loadingCompletedCount: boolean = false;
  @Input() loadingAbandonedCount: boolean = false;
  @Input() currentStatusFilter: string = 'all';
  
  /**
   * Event emitted when a user clicks on one of the count tabs
   * Will send the status value ('active', 'completed', 'abandoned', or 'all')
   */
  @Output() statusFilterChange = new EventEmitter<string>();
  
  /**
   * Handle click on active PRs count
   */
  onActiveClick(): void {
    this.statusFilterChange.emit('active');
  }
  
  /**
   * Handle click on completed PRs count
   */
  onCompletedClick(): void {
    this.statusFilterChange.emit('completed');
  }
  
  /**
   * Handle click on abandoned PRs count
   */
  onAbandonedClick(): void {
    this.statusFilterChange.emit('abandoned');
  }
}
