﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using MediatR;
using RIB.PRMetrics.Application.Dto.IdentityPicker;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;

namespace RIB.PRMetrics.Application.UseCases.IdentityPicker.Queries.GetIdentities
{
    public class GetIdentitiesHandler : IRequestHandler<GetIdentitiesQuery, BaseReponseGeneric<List<IdentitiesItemDto>>>
    {
        private readonly IUnitOfWork _unitOfWork; // Manages data repositories and transactions
        private readonly IMapper _mapper; // Maps domain entities to DTOs

        /// <summary>
        /// Initializes a new instance of the handler with the required dependencies.
        /// </summary>
        public GetIdentitiesHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        /// <summary>
        /// Handles the GetIdentitiesQuery by fetching identity items and mapping them to DTOs.
        /// </summary>
        public async Task<BaseReponseGeneric<List<IdentitiesItemDto>>> Handle(GetIdentitiesQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<List<IdentitiesItemDto>>();

            try
            {
                // Fetch identity items using repository
                var identitiesItem = await _unitOfWork.IdentityPickerRepository.GetIdentitiesAsync(request.UserUuid, request.PATToken);

                if (identitiesItem is not null)
                {
                    // Map entities to DTOs
                    response.Data = _mapper.Map<List<IdentitiesItemDto>>(identitiesItem);
                    response.Succcess = true;  // Set success flag
                    response.Message = "Identities Item fetched successfully.";
                }
                else
                {
                    response.Succcess = false;
                    response.Message = "Identities Item not found.";
                }
            }
            catch (Exception ex)
            {
                // Capture exception message and mark as failure
                response.Succcess = false;
                response.Message = $"Error occurred: {ex.Message}";
            }

            return response;
        }
    }
}
