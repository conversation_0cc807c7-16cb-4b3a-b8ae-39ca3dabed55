﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.Dto.CommentThreads;
using RIB.PRMetrics.Domain.Entities;
using RIB.PRMetrics.Domain.Entities.CommentThreads;

namespace RIB.PRMetrics.Application.UseCases.Commons.Mappings
{
    /// <summary>
    /// AutoMapper Profile that defines the mappings between the domain entities and their corresponding DTOs.
    /// </summary>
    public class GitPullRequestThreadsMapper : Profile
    {
        public GitPullRequestThreadsMapper()
        {
            // Mapping between GitPullRequestThread domain entity and GitPullRequestThreadDto
            CreateMap<GitPullRequestThread, GitPullRequestThreadDto>()
                // Mapping Id property from source to destination
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                // Mapping Comments collection from source to destination
                .ForMember(dest => dest.Comments, opt => opt.MapFrom(src => src.Comments))
                // Mapping PublishedDate property from source to destination
                .ForMember(dest => dest.PublishedDate, opt => opt.MapFrom(src => src.PublishedDate))
                // Mapping LastUpdatedDate property from source to destination
                .ForMember(dest => dest.LastUpdatedDate, opt => opt.MapFrom(src => src.LastUpdatedDate));

            // Mapping between GitPullRequestComment domain entity and GitPullRequestCommentDto
            CreateMap<GitPullRequestComment, GitPullRequestCommentDto>()
                // Mapping Id property from source to destination
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                // Mapping ParentCommentId property from source to destination
                .ForMember(dest => dest.ParentCommentId, opt => opt.MapFrom(src => src.ParentCommentId))
                // Mapping LastUpdatedDate property from source to destination
                .ForMember(dest => dest.LastUpdatedDate, opt => opt.MapFrom(src => src.LastUpdatedDate))
                // Mapping PublishedDate property from source to destination
                .ForMember(dest => dest.PublishedDate, opt => opt.MapFrom(src => src.PublishedDate))
                // Mapping Author property from source to destination
                .ForMember(dest => dest.Author, opt => opt.MapFrom(src => src.Author))
                // Mapping CommentType property from source to destination
                .ForMember(dest => dest.CommentType, opt => opt.MapFrom(src => src.CommentType))
                // Mapping Content property from source to destination
                .ForMember(dest => dest.Content, opt => opt.MapFrom(src => src.Content));

            // Mapping between User domain entity and UserDto
            CreateMap<User, UserDto>();

            // Mapping between GitPullRequestThreadsResponse domain entity and GitPullRequestThreadsResponseDto
            CreateMap<GitPullRequestThreadsResponse, GitPullRequestThreadsResponseDto>();

            // Mapping between GitPullRequestThreadsMode domain entity and GitPullRequestThreadsModeDto
            CreateMap<GitPullRequestThreadsMode, GitPullRequestThreadsModeDto>();

            // Mapping between StateEvent domain entity and StateEventDto
            CreateMap<StateEvent, StateEventDto>();


            // Mapping from GitPullRequestCommentThread to GitPullRequestCommentThreadDto and vice versa
            CreateMap<GitPullRequestCommentThread, GitPullRequestCommentThreadDto>().ReverseMap();

            // Mapping from ReviewerCommentCount to ReviewerCommentCountDto and vice versa
            CreateMap<ReviewerCommentCount, ReviewerCommentCountDto>().ReverseMap();

            // Mapping from CommentThread to CommentThreadDto and vice versa
            CreateMap<CommentThread, CommentThreadDto>().ReverseMap();

            // Mapping from Comment to CommentDto and vice versa
            CreateMap<Comment, CommentDto>().ReverseMap();

        }
    }
}
