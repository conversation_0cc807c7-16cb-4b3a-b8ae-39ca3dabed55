﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto.Contributions.HierarchyQuery
{
    /// <summary>
    /// Represents the properties related to the context of a data provider in the hierarchy query.
    /// These properties contain the key data required to filter or customize the data retrieval.
    /// </summary>
    public class DataProviderContextPropertiesDto
    {
        /// <summary>
        /// Gets or sets the unique identifier for the pull request.
        /// This can be used to filter or retrieve data specific to a particular pull request.
        /// </summary>
        
        public string PullRequestId { get; set; }

        /// <summary>
        /// Gets or sets the unique identifier for the repository.
        /// This is typically used to narrow down queries to a specific repository.
        /// </summary>
        
        public string RepositoryId { get; set; }

        /// <summary>
        /// Gets or sets the type of the context or entity.
        /// This can be used to specify what kind of data or object to retrieve (e.g., contributions, issues, etc.).
        /// </summary>
        
        public int Types { get; set; }
    }
}
