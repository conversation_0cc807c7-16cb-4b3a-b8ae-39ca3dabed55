﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using MediatR;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestConflicts
{
    /// <summary>
    /// Handles the retrieval of Git Pull Request conflicts for a given pull request ID.
    /// Implements the MediatR IRequestHandler interface to process GetGitPullRequestConflictsQuery requests.
    /// Uses AutoMapper to map domain entities to Data Transfer Objects (DTOs).
    /// </summary>
    public class GetGitPullRequestConflictsHandler : IRequestHandler<GetGitPullRequestConflictsQuery, BaseReponseGeneric<List<GitPullRequestConflictDto>>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetGitPullRequestConflictsHandler"/> class.
        /// </summary>
        /// <param name="unitOfWork">The unit of work instance that handles database operations.</param>
        /// <param name="mapper">The AutoMapper instance used to map domain models to DTOs.</param>
        public GetGitPullRequestConflictsHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        /// <summary>
        /// Handles the <see cref="GetGitPullRequestConflictsQuery"/> request to fetch conflicts associated with a specific Git pull request.
        /// It communicates with the repository to retrieve conflict data and maps it to a list of DTOs.
        /// </summary>
        /// <param name="request">The request containing the pull request ID to fetch conflicts for.</param>
        /// <param name="cancellationToken">The cancellation token to monitor for task cancellation.</param>
        /// <returns>A <see cref="BaseReponseGeneric{List{GitPullRequestConflictDto}}"/> containing either the conflict data or error message.</returns>
        public async Task<BaseReponseGeneric<List<GitPullRequestConflictDto>>> Handle(GetGitPullRequestConflictsQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<List<GitPullRequestConflictDto>>()
            {
                Succcess = false // Ensuring default to false in case of error
            };

            try
            {
                // Fetching Git pull request conflicts from the repository.
                var gitPullRequestConflicts = await _unitOfWork.GitPullRequestsRepository.GetGitPullRequestConflictsAsync(request, request.PullRequestId);

                // Checking if conflicts were found and mapping them to DTOs.
                if (gitPullRequestConflicts != null && gitPullRequestConflicts.Any())
                {
                    response.Data = _mapper.Map<List<GitPullRequestConflictDto>>(gitPullRequestConflicts);
                    response.Succcess = true;
                    response.Message = "Git Pull Request Conflicts list fetch succeeded!";
                }
                else
                {
                    // No conflicts found.
                    response.Message = "No pull requests Conflicts found for the given query.";
                }
            }
            catch (Exception ex)
            {
                // Handling unexpected errors and returning failure response.
                response.Succcess = false;
                response.Message = $"An error occurred while fetching pull requests conflicts: {ex.Message}";
                // You could log the exception here (e.g., _logger.LogError(ex, "Failed to fetch pull requests"));
            }

            // Returning the response containing the result or error message.
            return response;
        }
    }
}



