﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Newtonsoft.Json;

namespace RIB.PRMetrics.Domain.Entities.PullRequestBuild
{
    public class TimeLineItem
    {
        /// <summary>
        /// Unique identifier for the timeline item.
        /// </summary>
        [JsonProperty("id")]
        public string Id { get; set; }

        /// <summary>
        /// Parent ID linking to another timeline item, if applicable.
        /// </summary>
        [JsonProperty("parentId")]
        public string ParentId { get; set; }

        /// <summary>
        /// The type of the timeline item, e.g., "Build", "Test".
        /// </summary>
        [JsonProperty("type")]
        public string Type { get; set; }

        /// <summary>
        /// The name or label for this timeline item.
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; }

        /// <summary>
        /// The start time of this timeline item.
        /// </summary>
        [JsonProperty("startTime")]
        public string StartTime { get; set; }

        /// <summary>
        /// The finish time of this timeline item.
        /// </summary>
        [JsonProperty("finishTime")]
        public string FinishTime { get; set; }

        /// <summary>
        /// The state of the timeline item, e.g., "Completed", "InProgress".
        /// </summary>
        [JsonProperty("State")]
        public string state { get; set; }

        /// <summary>
        /// The result of the timeline item, e.g., "Success", "Failure".
        /// </summary>
        [JsonProperty("result")]
        public string Result { get; set; }

        /// <summary>
        /// The ID of the change associated with this timeline item.
        /// </summary>
        [JsonProperty("changeId")]
        public double ChangeId { get; set; }

        /// <summary>
        /// The last modified date/time for the timeline item.
        /// </summary>
        [JsonProperty("lastModified")]
        public string LastModified { get; set; }

        /// <summary>
        /// The number of errors associated with this timeline item.
        /// </summary>
        [JsonProperty("errorCount")]
        public double ErrorCount { get; set; }

        /// <summary>
        /// The number of warnings associated with this timeline item.
        /// </summary>
        [JsonProperty("warningCount")]
        public double WarningCount { get; set; }

        /// <summary>
        /// A log entry associated with this timeline item.
        /// </summary>
        [JsonProperty("log")]
        public Log Log { get; set; }

        /// <summary>
        /// A list of issues related to this timeline item.
        /// </summary>
        [JsonProperty("issues")]
        public List<Issues> Issues { get; set; }

        /// <summary>
        /// The attempt number for this timeline item.
        /// </summary>
        [JsonProperty("attempt")]
        public double Attempt { get; set; }

        /// <summary>
        /// The unique identifier for this timeline item.
        /// </summary>
        [JsonProperty("identifier")]
        public string Identifier { get; set; }
    }
}
