﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities
{
    /// <summary>
    /// Represents a reference to a Git identity, typically a user or contributor.
    /// Contains details such as the display name, unique name, ID, and image URL.
    /// </summary>
    public class GitIdentityRef
    {
        /// <summary>
        /// Gets or sets the display name of the Git identity (e.g., the full name of the user).
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// Gets or sets the unique name (username) of the Git identity.
        /// </summary>
        public string UniqueName { get; set; }

        /// <summary>
        /// Gets or sets the unique identifier for the Git identity.
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the URL of the Git identity's image (e.g., avatar or profile picture).
        /// </summary>
        public string ImageUrl { get; set; }
    }
}
