﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;

namespace  RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestById
{
    /// <summary>
    /// Validator for GetGitPullRequestByIdQuery.
    /// Ensures all required fields are provided to fetch a pull request.
    /// </summary>
    public class GetGitPullRequestByIdValidator : AbstractValidator<GetGitPullRequestByIdQuery>
    {
        public GetGitPullRequestByIdValidator()
        {
            // Ensure 'Project' field is not empty or null
            RuleFor(x => x.Project)
                .NotEmpty().WithMessage("Project is required.")
                .NotNull().WithMessage("Project must not be null.");

            // Ensure 'Repositories' field is not empty or null
            RuleFor(x => x.Repositories)
                .NotEmpty().WithMessage("Repository is required.")
                .NotNull().WithMessage("Repository must not be null.");

            // Ensure 'PullRequestId' field is not empty or null
            RuleFor(x => x.PullRequestId)
                .NotEmpty().WithMessage("Pull Request ID is required.")
                .NotNull().WithMessage("Pull Request ID must not be null.");
        }
    }
}
