﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto
{
    /// <summary>
    /// Represents a Git Pull Request thread, containing details about the thread such as comments, 
    /// published date, and last updated date. Used to transfer data related to a pull request thread.
    /// </summary>
    public class GitPullRequestThreadDto
    {
        /// <summary>
        /// Gets or sets the unique identifier for the pull request thread.
        /// This ID is used to uniquely identify a specific thread in the Git repository.
        /// </summary>
        
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the array of comments associated with the pull request thread.
        /// Each comment is represented as a GitPullRequestCommentDto.
        /// </summary>
        
        public GitPullRequestCommentDto[] Comments { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the pull request thread was first published.
        /// This date is represented as a DateTimeOffset, which includes both the date and time along with the time zone offset.
        /// </summary>
        
        public DateTimeOffset PublishedDate { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the pull request thread was last updated.
        /// This is useful for tracking the most recent activity within the thread.
        /// </summary>
        
        public DateTimeOffset LastUpdatedDate { get; set; }
    }
}
