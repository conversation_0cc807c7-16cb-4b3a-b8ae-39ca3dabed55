﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace  RIB.PRMetrics.Application.Dto
{
    /// <summary>
    /// Represents a reviewer for a Git pull request in Azure DevOps.
    /// </summary>
    public class GitReviewerDto
    {
        /// <summary>
        /// The unique identifier of the reviewer.
        /// </summary>
        
        public string Id { get; set; }

        /// <summary>
        /// The display name of the reviewer.
        /// </summary>
        
        public string DisplayName { get; set; }

        /// <summary>
        /// The vote of the reviewer on the pull request (e.g., 0 for no vote, 1 for approved, -1 for rejected).
        /// </summary>
        
        public int Vote { get; set; }

        /// <summary>
        /// Indicates whether the reviewer is required to approve the pull request before it can be merged.
        /// </summary>
        
        public bool IsRequired { get; set; }
    }
}
