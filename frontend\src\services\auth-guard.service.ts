import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { Observable, map } from 'rxjs';
import { PrDataService } from './pr-data.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuardService implements CanActivate {
  constructor(
    private prDataService: PrDataService,
    private router: Router
  ) {}

  canActivate(): Observable<boolean> {
    return this.prDataService.credentials$.pipe(
      map(credentials => {
        const isLoggedIn = !!credentials;
        
        if (!isLoggedIn) {
          this.router.navigate(['/']);
        }
        
        return isLoggedIn;
      })
    );
  }
}
