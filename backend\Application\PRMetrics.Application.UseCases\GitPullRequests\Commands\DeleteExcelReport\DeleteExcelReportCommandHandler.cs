﻿using AutoMapper;
using MediatR;
using Microsoft.Extensions.Caching.Memory;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.DeleteExcelReport;
using RIB.PRMetrics.Domain.Entities;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.DeleteExcelReport
{
    public class TakeActionExcelReportCommandHandler : IRequestHandler<DeleteExcelReportCommand, BaseReponseGeneric<ProcessProgressDto>>
    {
        private readonly IUnitOfWork _unitOfWork;

        private readonly IMapper _mapper;

        public TakeActionExcelReportCommandHandler(IUnitOfWork unitOfWork,IMapper mapper)
        {
            _unitOfWork= unitOfWork;
            _mapper= mapper;
        }

        public async Task<BaseReponseGeneric<ProcessProgressDto>> Handle(DeleteExcelReportCommand request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<ProcessProgressDto>();

            try
            {

                var excelExportReport = await _unitOfWork.GitPullRequestsRepository.DeleteExcelReportProgressAsync(request.Uuid);

                if (excelExportReport is not null)
                {
                    response.Data = _mapper.Map<ProcessProgressDto>(excelExportReport);
                    response.Succcess = true;
                    response.Message = "Excel Export File Deleted successfully.";
                }
                else
                {
                    response.Succcess = false;
                    response.Message = "Excel Export File not found.";
                }
            }
            catch (Exception ex)
            {
                response.Succcess = false;
                response.Message = $"Error occurred: {ex.Message}";
            }

            return response;
        }
    }
}
