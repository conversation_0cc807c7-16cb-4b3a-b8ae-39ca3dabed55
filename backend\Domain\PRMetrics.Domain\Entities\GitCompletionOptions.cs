﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Domain.Entities
{
    /// <summary>
    /// Represents the options for completing a Git merge or pull request.
    /// This includes options such as the commit message, whether to delete the source branch after merging, the merge strategy, 
    /// and whether to transition associated work items.
    /// </summary>
    public class GitCompletionOptions
    {
        /// <summary>
        /// Gets or sets the message to use for the merge commit.
        /// </summary>
        public string MergeCommitMessage { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the source branch should be deleted after the merge.
        /// </summary>
        public bool DeleteSourceBranch { get; set; }

        /// <summary>
        /// Gets or sets the merge strategy to use.
        /// </summary>
        public string MergeStrategy { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether associated work items should be transitioned.
        /// </summary>
        public bool TransitionWorkItems { get; set; }
    }
}
