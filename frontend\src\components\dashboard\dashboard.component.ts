import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { BaseChartDirective } from 'ng2-charts';
import { ChartConfiguration, ChartData, ChartType } from 'chart.js';
import { PrDataService } from '../../services/pr-data.service';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatIconModule } from '@angular/material/icon';
import { Credentials } from '../../models/pr-interfaces.model';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    BaseChartDirective,
    MatProgressSpinnerModule,
    MatButtonModule,
    MatTooltipModule,
    MatIconModule,
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
})
export class DashboardComponent implements OnInit {
  loading = false;
  credentials: Credentials | null = null;
  prData: any[] = [];

  // Debug flag for troubleshooting
  debugMode = false;

  // Active PR count from localStorage
  activePRCountFromStorage: number = 0;

  // PR Status Distribution Chart
  public prStatusChartData: ChartData<'pie'> = {
    labels: ['Active', 'Completed', 'Abandoned', 'Draft'],
    datasets: [
      {
        data: [175, 22000, 228, 12], // Active, Completed, Abandoned, Draft
        backgroundColor: ['#FF9F40', '#36A2EB', '#FF6384', '#4BC0C0'],
      },
    ],
  };

  public prStatusChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
      title: {
        display: true,
        text: 'PR Status Distribution',
      },
    },
  };

  // PR Creation Timeline Chart (last 30 days)
  public prTimelineChartData: ChartData<'bar'> = {
    labels: Array.from({ length: 30 }, (_, i) => `5/${i + 1}`),
    datasets: [
      {
        data: [12, 15, 18, 20, 22, 25, 30, 28, 27, 26, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5],
        label: 'PRs Created',
        backgroundColor: '#36A2EB',
      },
    ],
  };

  public prTimelineChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    scales: {
      x: {
        title: {
          display: true,
          text: 'Date',
        },
      },
      y: {
        title: {
          display: true,
          text: 'Number of PRs',
        },
        beginAtZero: true,
      },
    },
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
      title: {
        display: true,
        text: 'PR Creation Timeline (Last 30 Days)',
      },
    },
  };
  // PR Review Time Chart
  public prReviewTimeChartData: ChartData<'doughnut'> = {
    labels: ['< 1 Day', '1-3 Days', '3-7 Days', '> 7 Days'],
    datasets: [
      {
        data: [5000, 8000, 6000, 3000],
        backgroundColor: ['#4BC0C0', '#36A2EB', '#FFCD56', '#FF6384'],
        hoverOffset: 4,
      },
    ],
  };
  public prReviewTimeChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
      title: {
        display: true,
        text: 'PR Review Time Distribution',
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            const label = context.label || '';
            const value = context.formattedValue;
            const dataset = context.dataset;
            let total = 0;
            if (dataset.data) {
              // Safely calculate total avoiding type errors
              for (let i = 0; i < dataset.data.length; i++) {
                const dataPoint = dataset.data[i];
                if (typeof dataPoint === 'number') {
                  total += dataPoint;
                }
              }
            }
            const percentage =
              total > 0 ? Math.round((context.parsed * 100) / total) : 0;
            return `${label}: ${value} (${percentage}%)`;
          },
        },
      },
    },
  };

  // Repository Activity Chart
  public repoActivityChartData: ChartData<'radar'> = {
    labels: ['Commits', 'Reviews', 'Merges', 'Comments', 'Builds'],
    datasets: [
      {
        label: 'Activity',
        data: [1200, 900, 1100, 1500, 800],
        backgroundColor: 'rgba(54,162,235,0.2)',
        borderColor: '#36A2EB',
        pointBackgroundColor: '#36A2EB',
      },
    ],
  };

  public repoActivityChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
      title: {
        display: true,
        text: 'Repository Activity Metrics',
      },
    },
    scales: {
      r: {
        min: 0,
        ticks: {
          stepSize: 1,
        },
      },
    },
  };

  constructor(private prDataService: PrDataService) {}

  ngOnInit(): void {
    // Subscribe to credentials changes
    this.prDataService.credentials$.subscribe((credentials) => {
      this.credentials = credentials;
    });

    // If credentials exist, load data automatically
    if (this.credentials) {
      this.loadDashboardData();
    }
    // Load active PR count from localStorage
    const storedActive = localStorage.getItem('activePRCount');
    this.activePRCountFromStorage = storedActive ? +storedActive : 0;
  }

  loadDashboardData(): void {
    if (!this.credentials) {
      return;
    }

    this.loading = true;

    // Fetch PR data using the service (first page, 100 items)
    this.prDataService
      .fetchPrData(0, 100)
      .then((response) => {
        this.prData = response.data;
        this.processChartData();
        this.loading = false;
      })
      .catch((error) => {
        console.error('Error loading dashboard data:', error);
        this.loading = false;
      });
  }
  processChartData(): void {
    if (!this.prData || this.prData.length === 0) {
      return;
    }

    // Check data availability for debugging
    this.checkDataAvailability();

    // Process PR Status Distribution
    const statusCounts = {
      active: 175,
      completed: 22000,
      abandoned: 228,
      draft: 12,
    };

    this.prData.forEach((pr) => {
      if (pr.isDraft) {
        statusCounts.draft++;
      } else if (pr.status === 'active') {
        statusCounts.active++;
      } else if (pr.status === 'completed') {
        statusCounts.completed++;
      } else if (pr.status === 'abandoned') {
        statusCounts.abandoned++;
      }
    });

    this.prStatusChartData.datasets[0].data = [
      statusCounts.active,
      statusCounts.completed,
      statusCounts.abandoned,
      statusCounts.draft,
    ];

    // Process PR Creation Timeline
    this.processPrTimelineData();

    // Process PR Review Time Distribution
    this.processReviewTimeData();

    // Process Repository Activity
    this.processRepoActivityData();
  }
  processPrTimelineData(): void {
    // Generate dates for the last 30 days
    const dates: string[] = [];
    const counts: number[] = [];

    const today = new Date();

    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(today.getDate() - i);

      const dateString = `${date.getMonth() + 1}/${date.getDate()}`;
      dates.push(dateString);
      counts.push(0);
    }

    // Count PRs by creation date
    this.prData.forEach((pr) => {
      if (!pr.creationDate) return;

      const creationDate = new Date(pr.creationDate);

      // Check if PR was created in the last 30 days
      const timeDiff = today.getTime() - creationDate.getTime();
      const dayDiff = Math.floor(timeDiff / (1000 * 3600 * 24));

      if (dayDiff >= 0 && dayDiff < 30) {
        const index = 29 - dayDiff;
        counts[index]++;
      }
    });

    this.prTimelineChartData.labels = dates;
    this.prTimelineChartData.datasets[0].data = counts;
  }
  processReviewTimeData(): void {
    // PR Review Time Categories (in days)
    const reviewTimeCounts = {
      lessThanOneDay: 0,
      oneToThreeDays: 0,
      threeToSevenDays: 0,
      moreThanSevenDays: 0,
    };

    this.prData.forEach((pr) => {
      // Only process completed PRs
      // closedDate might be under different properties depending on API response
      const completionDate =
        pr.closedDate || pr.completedDate || pr.completionDate;

      if (pr.status === 'completed' && pr.creationDate && completionDate) {
        const creationDate = new Date(pr.creationDate);
        const closedDate = new Date(completionDate);

        const timeDiff = closedDate.getTime() - creationDate.getTime();
        const dayDiff = Math.floor(timeDiff / (1000 * 3600 * 24));

        if (dayDiff < 1) {
          reviewTimeCounts.lessThanOneDay++;
        } else if (dayDiff >= 1 && dayDiff < 3) {
          reviewTimeCounts.oneToThreeDays++;
        } else if (dayDiff >= 3 && dayDiff < 7) {
          reviewTimeCounts.threeToSevenDays++;
        } else {
          reviewTimeCounts.moreThanSevenDays++;
        }
      }
    });

    this.prReviewTimeChartData.datasets[0].data = [
      reviewTimeCounts.lessThanOneDay,
      reviewTimeCounts.oneToThreeDays,
      reviewTimeCounts.threeToSevenDays,
      reviewTimeCounts.moreThanSevenDays,
    ];
  }
  processRepoActivityData(): void {
    // Calculate repository activity metrics
    let totalCreated = this.prData.length;
    let totalMerged = 0;
    let totalAbandoned = 0;
    let totalComments = 0;
    let totalIterations = 0;

    this.prData.forEach((pr) => {
      if (pr.status === 'completed') {
        totalMerged++;
      } else if (pr.status === 'abandoned') {
        totalAbandoned++;
      }

      // Count comments if available
      if (pr.comments) {
        totalComments += pr.comments.length;
      }

      // Count iterations if available
      if (pr.iterations) {
        totalIterations += pr.iterations.length;
      } else {
        // If iterations count not available, estimate based on comments (just for visualization)
        totalIterations += Math.floor(Math.random() * 3) + 1;
      }
    });

    this.repoActivityChartData.datasets[0].data = [
      totalCreated,
      totalMerged,
      totalAbandoned,
      totalComments,
      totalIterations,
    ];
  }

  // Utility method to format dates for better display
  formatDate(date: Date): string {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}/${day}`;
  }

  // Calculate PR efficiency (% of PRs merged vs abandoned)
  calculatePREfficiency(): number {
    const completed = this.prStatusChartData.datasets[0].data[1] as number;
    const abandoned = this.prStatusChartData.datasets[0].data[2] as number;

    if (completed + abandoned === 0) {
      return 0;
    }

    return Math.round((completed / (completed + abandoned)) * 100);
  }
  // Helper method to check if chart has no data
  hasNoReviewTimeData(): boolean {
    if (
      !this.prReviewTimeChartData ||
      !this.prReviewTimeChartData.datasets ||
      !this.prReviewTimeChartData.datasets[0] ||
      !this.prReviewTimeChartData.datasets[0].data
    ) {
      return true;
    }
    // Safely check if all values are 0
    const data = this.prReviewTimeChartData.datasets[0].data;
    for (let i = 0; i < data.length; i++) {
      const value = data[i];
      if (typeof value === 'number' && value !== 0) {
        return false;
      }
    }
    return true;
  }

  // Helper methods to check if charts have no data
  hasNoStatusData(): boolean {
    if (
      !this.prStatusChartData ||
      !this.prStatusChartData.datasets ||
      !this.prStatusChartData.datasets[0] ||
      !this.prStatusChartData.datasets[0].data
    ) {
      return true;
    }
    // Safely check if all values are 0
    const data = this.prStatusChartData.datasets[0].data;
    for (let i = 0; i < data.length; i++) {
      const value = data[i];
      if (typeof value === 'number' && value !== 0) {
        return false;
      }
    }
    return true;
  }
  hasNoTimelineData(): boolean {
    if (
      !this.prTimelineChartData ||
      !this.prTimelineChartData.datasets ||
      !this.prTimelineChartData.datasets[0] ||
      !this.prTimelineChartData.datasets[0].data
    ) {
      return true;
    }
    // Safely check if all values are 0
    const data = this.prTimelineChartData.datasets[0].data;
    for (let i = 0; i < data.length; i++) {
      const value = data[i];
      if (typeof value === 'number' && value !== 0) {
        return false;
      }
    }
    return true;
  }

  hasNoActivityData(): boolean {
    if (
      !this.repoActivityChartData ||
      !this.repoActivityChartData.datasets ||
      !this.repoActivityChartData.datasets[0] ||
      !this.repoActivityChartData.datasets[0].data
    ) {
      return true;
    }
    // Safely check if all values are 0
    const data = this.repoActivityChartData.datasets[0].data;
    for (let i = 0; i < data.length; i++) {
      const value = data[i];
      if (typeof value === 'number' && value !== 0) {
        return false;
      }
    }
    return true;
  }

  // Debugging method to check data availability
  checkDataAvailability(): void {}

  // Helper methods for debug info display
  getCompletedPRCount(): number {
    return this.prData
      ? this.prData.filter((pr) => pr.status === 'completed').length
      : 0;
  }

  getPRsWithCreationDateCount(): number {
    return this.prData ? this.prData.filter((pr) => pr.creationDate).length : 0;
  }

  getPRsWithClosedDateCount(): number {
    return this.prData
      ? this.prData.filter(
          (pr) => pr.closedDate || pr.completedDate || pr.completionDate
        ).length
      : 0;
  }

  getAbandonedPRCount(): number {
    return this.prData
      ? this.prData.filter((pr) => pr.status === 'abandoned').length
      : 0;
  }
}
