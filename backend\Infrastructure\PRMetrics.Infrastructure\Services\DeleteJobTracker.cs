﻿using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Domain.Entities;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.PRMetrics.Infrastructure.Services
{
    public class DeleteJobTracker : IDeleteJobTracker
    {
        private readonly ConcurrentDictionary<Guid, ProcessProgress> _jobs = new();

        public void Add(ProcessProgress job) => _jobs[job.Uuid] = job;

        public ProcessProgress Get(Guid uuid) => _jobs.TryGetValue(uuid, out var job) ? job : null;
    }
}
