#triggering yaml template to be copied inside new Repository
trigger:
  branches:
    include:
      - master
  paths:
    exclude:
      - build/*.yml

resources:
  repositories:
    - repository: devops
      type: git
      name: devops
      ref: master

variables:
  # Do not change these variables
  - name: devopsRepoName
    value: $[ resources.repositories.devops.name ]
  - name: FeedId
    value: '4220ea73-9afe-4b22-9145-a7603305bbbb/ba736670-b1fa-4924-80e9-694896de067e'
  - name: artifactName
    value: 'DevCenterArtifact'

jobs:
    - job: ValidateBuild
      pool: vmss-d4s_v5-windows2022-spot
      condition: always()
      workspace:
        clean: all
      steps:
        - checkout: self
        - checkout: devops
        - template: tools\devCenter\steps-templates\build-asp.net-template.yml@devops

    - ${{ if eq(variables['Build.Reason'], 'IndividualCI')}}:
      - deployment: Deploy
        displayName: Deploy
        pool: iTWO40-WEB-Server
        condition: succeeded()
        workspace:
          clean: all
        environment: 'DevCenter'
        dependsOn: ValidateBuild
        variables:
          ApplicationPool: $[ dependencies.ValidateBuild.outputs['LoadPipelineConfiguration.ApplicationPool'] ]
          TargetPath: $[ dependencies.ValidateBuild.outputs['LoadPipelineConfiguration.TargetPath'] ]
          FrontendTargetPath: $[ dependencies.ValidateBuild.outputs['LoadPipelineConfiguration.frontendTargetPath'] ]
          FrontendType: $[ dependencies.ValidateBuild.outputs['LoadPipelineConfiguration.frontendType'] ]
          FrontendPath: $[ dependencies.ValidateBuild.outputs['LoadPipelineConfiguration.frontendPath'] ]
          FrontendRoute: $[ dependencies.ValidateBuild.outputs['LoadPipelineConfiguration.frontendRoute'] ]
        strategy:
          runOnce:
            deploy:
              steps:
                - download: none
                - checkout: devops
                - template: tools\devCenter\steps-templates\deploy-asp.net-devcenter-template.yml@devops
