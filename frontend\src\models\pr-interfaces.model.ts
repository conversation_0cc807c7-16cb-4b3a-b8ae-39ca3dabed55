// This file contains interfaces used throughout the PR analytics application
// Especially those related to API responses and pagination

/**
 * Interface for pagination information
 */
export interface PaginationInfo {
  pageIndex: number;
  pageSize: number;
  totalCount: number;
  continuationToken?: string;
}

/**
 * Interface for generic API responses with array data
 */
export interface ApiResponse {
  data: any[];
  totalCount: number;
  success: boolean;
  message?: string;
}

/**
 * Interface for generic API responses with single object data
 */
export interface ApiResponseSingle<T> {
  data: T;
  success: boolean;
  message?: string;
}

/**
 * Interface for credentials used throughout the app
 */
export interface Credentials {
  patToken: string;
  project: string;
  repository: string;
  projectId?: string;
  repositoryId?: string;
}

/**
 * Interface for Azure DevOps Project
 */
export interface Project {
  id: string;
  name: string;
}

/**
 * Interface for Azure DevOps Repository
 */
export interface Repository {
  id: string;
  name: string;
  url: string;
  project: Project[];
  defaultBranch: string;
  sshUrl: string;
  webUrl: string;
  isDisabled: boolean;
}

/**
 * Interface for Pull Request basic data
 */
export interface PullRequestData {
  id: string;
  title: string;
  // Other properties as needed for the basic PR data
}

/**
 * Interface for detailed Pull Request information
 */
export interface PullRequestDetails {
  pullRequestId: number;
  title: string;
  description: string;
  status: string;
  creationDate: string;
  createdBy: {
    displayName: string;
    uniqueName: string;
    imageUrl: string;
  };
  sourceRefName: string;
  targetRefName: string;
  mergeStatus: string;
  isDraft: string;
  reviewers: any[];  workItemRefs: any[];
}

/**
 * Interface for pull request comment thread
 */
export interface CommentThread {
  id: number;
  comments: {
    id: number;
    parentCommentId: number;
    content: string;
    publishedDate: string;
    lastUpdatedDate: string;
    author: {
      id: string;
      displayName: string;
      uniqueName: string;
    };
    threadType: string;
    activeComment: string;
    activeCommentContent: string;
    commentType: string | null;
    // Added for enhanced comments with durations
    responseTime?: number;
    responseTimeFormatted?: string;
    activeTime?: number;
    activeTimeFormatted?: string;
  }[];
  hoursSincePRCreation: string;
  reviewerResponseDelayHours: string;
  authorResponseDelayHours: string;
  commentActiveDuration: string;
  publishedDate: string;
  lastUpdatedDate: string;
  status: string;
  pullRequestThreadContext?: {
    filePath: string;
    rightFileStart?: {
      line: number;
    };
  };
  // Additional metrics for the thread
  activeDuration?: number;
  activeDurationFormatted?: string;
  fixedDuration?: number;
  fixedDurationFormatted?: string;
  hoursSincePRCreationFormatted?: string;
}

/**
 * Interface for comment thread API response
 */
export interface CommentThreadResponse {
  succcess: boolean;
  data: {
    draftMode: {
      totalPublishedTime: number;
      totalDraftTime: number;
      stateEvents: any[];
    };
    commentThread: {
      status: string;
      createdBy: {
        id: string;
        displayName: string;
        uniqueName: string;
      };      creationDate: string;
      activeCommentCount: number;
      resolvedCommentCount: number;
      reviewsCount: {
        resolveCommentCount: number;
        activeCommentCount: number;
        id: string;
        displayName: string;
        uniqueName: string | null;
      }[];
      commentThreads: any | null;
    };
  };
  message: string;
  errors: any;
}

/**
 * Interface for contributions hierarchy query response
 */
export interface ContributionsHierarchyQueryResponse {
  succcess: boolean;
  data: {
    dataProviders: {
      prDetailDataProvider: {
        policies: Array<{
          configurationId: number;
          displayName: string;
          displayText: string;
          displayStatus: number;
          buildId?: number;
          validBuildExists?: boolean;
          buildIsExpired?: boolean;
          isBlocking: boolean;
          status: number;
        }>
      }
    }
  };
  message: string;
  errors: any;
}

/**
 * Interface for build timeline log reference
 */
export interface BuildTimelineLog {
  id: number;
  url: string;
}

/**
 * Interface for build timeline issue
 */
export interface BuildTimelineIssue {
  type: string; // 'error' | 'warning'
  category: string | null;
  message: string;
}

/**
 * Interface for build timeline record
 */
export interface BuildTimelineRecord {
  id: string;
  parentId: string | null;
  type: string;
  name: string;
  startTime: string;
  finishTime: string;
  state: string;
  result: string;
  changeId: number;
  lastModified: string;
  errorCount: number;
  warningCount: number;
  log: BuildTimelineLog | null;
  issues: BuildTimelineIssue[];
  attempt: number;
  identifier: string | null;
}

/**
 * Interface for GitPullRequestBuildTimeLine API response
 */
export interface GitPullRequestBuildTimeLineResponse {
  succcess: boolean;
  data: {
    records: BuildTimelineRecord[];
    lastChangedBy: string;
    lastChangedOn: string;
    id: string;
    changeId: number;
    url: string;
  };
  message: string;
  errors: any;
}
