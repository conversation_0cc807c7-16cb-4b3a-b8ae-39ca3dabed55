﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using RIB.PRMetrics.Domain.Entities;
using System.ComponentModel.DataAnnotations;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Commands.DeleteExcelReport
{
    public class DeleteExcelReportCommand :  IRequest<BaseReponseGeneric<ProcessProgressDto>>
    {
        [Required(ErrorMessage = "uuid is required.")]
        [FromQuery(Name = "uuid")]
        public string Uuid { get; set; }
    }
}
