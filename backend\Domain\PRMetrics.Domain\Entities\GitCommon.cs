﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;

namespace RIB.PRMetrics.Domain.Entities
{
    /// <summary>
    /// Represents common properties for Git-related operations such as project name, repository, and authentication token.
    /// This class is typically used as a base class for requests or queries involving Git repositories.
    /// </summary>
    public class GitCommon
    {
        /// <summary>
        /// Gets or sets the name of the Git project.
        /// This property is required for identifying the specific project in Git.
        /// </summary>
        [Required(ErrorMessage = "Project is required.")]
        [FromQuery(Name = "project")]
        public string? Project { get; set; }

        /// <summary>
        /// Gets or sets the name of the Git repository.
        /// This property is required for identifying the specific repository within the project.
        /// </summary>
        [Required(ErrorMessage = "Repositories is required.")]
        [FromQuery(Name = "repositories")]
        public string? Repositories { get; set; }

        /// <summary>
        /// Gets or sets the personal access token (PAT) for authentication.
        /// This property is required for making authorized requests to the Git repository.
        /// </summary>
        [Required(ErrorMessage = "Token is required.")]
        [FromQuery(Name = "patToken")]
        public string? PATToken { get; set; }
    }
}
