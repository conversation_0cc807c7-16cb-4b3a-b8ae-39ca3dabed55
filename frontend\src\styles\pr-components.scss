// PR Component Shared Styles
// Contains styles specifically for PR-related components

@import './components.scss';

// Common Comment Styles
@mixin comment-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
}

@mixin comment-content {
  white-space: pre-line;
  line-height: 1.5;
  padding: 12px;
  background-color: rgba(0, 0, 0, 0.01);
  border-radius: 4px;
}

// Common no content style
@mixin no-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;
  color: var(--text-light);
}

// Thread Timeline Styles
@mixin thread-timeline {
  @include timeline-base;
  
  .timeline-item {
    position: relative;
    padding-bottom: 16px;
    display: flex;
    
    &:last-child {
      padding-bottom: 0;
      overflow-wrap: anywhere;
    }
    
    .timeline-avatar {
      position: absolute;
      top: 0;
      left: -24px;
      width: 20px;
      height: 20px;
      background-color: white;
      border-radius: 50%;
      border: 2px solid #e0e0e0;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;
      
      mat-icon {
        font-size: 12px;
        width: 12px;
        height: 12px;
        color: #666;
      }
    }
    
    .timeline-content {
      background-color: white;
      border: 1px solid #eee;
      border-radius: 8px;
      padding: 12px;
      margin-left: 8px;
      flex-grow: 1;
    }
  }
}

// Common card header and content styles
@mixin card-header-content {
  .mat-mdc-card-header {
    padding: 16px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #eee;
  }
  
  .mat-mdc-card-content {
    padding: 16px;
  }
}

// Comment Stats and Metrics
@mixin stats-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

@mixin stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 100px;
}

@mixin metrics-container {
  margin-top: 16px;
  padding: 16px;
  border-radius: 8px;
  background-color: #f8f9fa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

@mixin section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #333;
  
  mat-icon {
    margin-right: 8px;
    color: $primary-color;
  }
}

// Chart styles
@mixin chart-bar {
  height: 12px;
  display: flex;
  border-radius: 6px;
  overflow: hidden;
  background-color: #e9ecef;
  
  .chart-segment {
    height: 100%;
    transition: width 0.3s ease;
  }
}

// Common grid layout for cards and items
@mixin card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  
  @include responsive-mobile {
    grid-template-columns: 1fr;
  }
}
