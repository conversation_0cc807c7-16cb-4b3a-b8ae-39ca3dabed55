{"name": "@frontend/source", "version": "0.0.0", "license": "MIT", "scripts": {"start": "nx serve", "build": "nx build", "test": "nx test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.11", "@angular/cdk": "^19.2.16", "@angular/common": "~19.2.0", "@angular/compiler": "~19.2.0", "@angular/core": "~19.2.0", "@angular/forms": "~19.2.0", "@angular/material": "^19.2.16", "@angular/platform-browser": "~19.2.0", "@angular/platform-browser-dynamic": "~19.2.0", "@angular/router": "~19.2.0", "@microsoft/signalr": "^8.0.7", "@swimlane/ngx-charts": "^22.0.0", "ansi-to-html": "^0.7.2", "chart.js": "^4.4.9", "file-saver": "^2.0.5", "ng2-charts": "^8.0.0", "rxjs": "~7.8.0", "xlsx": "^0.18.5", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "~19.2.0", "@angular-devkit/core": "~19.2.0", "@angular-devkit/schematics": "~19.2.0", "@angular/cli": "~19.2.0", "@angular/compiler-cli": "~19.2.0", "@angular/language-service": "~19.2.0", "@eslint/js": "^9.8.0", "@nx/angular": "21.0.0", "@nx/eslint": "21.0.0", "@nx/eslint-plugin": "21.0.0", "@nx/js": "21.0.0", "@nx/web": "21.0.0", "@nx/workspace": "21.0.0", "@schematics/angular": "~19.2.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/file-saver": "^2.0.7", "@typescript-eslint/utils": "^8.19.0", "angular-eslint": "^19.2.0", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "nx": "21.0.0", "prettier": "^2.6.2", "tslib": "^2.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.19.0"}, "nx": {"includedScripts": []}}