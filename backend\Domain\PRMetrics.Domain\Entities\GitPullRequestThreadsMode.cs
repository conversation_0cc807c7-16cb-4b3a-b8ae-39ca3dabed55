﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.PRMetrics.Domain.Entities
{
    public class GitPullRequestThreadsMode
    {
        public double TotalPublishedTime { get; set; }

        public double TotalDraftTime { get; set; }
        public List<StateEvent> StateEvents { get; set; } = new List<StateEvent>();
    }
}
