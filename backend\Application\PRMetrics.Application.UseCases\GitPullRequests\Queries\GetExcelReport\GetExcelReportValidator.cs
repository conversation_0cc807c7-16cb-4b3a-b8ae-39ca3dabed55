﻿using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetExcelReport
{
    public class GetExcelReportValidator : AbstractValidator<GetExcelReportQuery>
    {
        public GetExcelReportValidator()
        {
            // Ensure 'Project' field is not empty or null
            RuleFor(x => x.Uuid)
                .NotEmpty().WithMessage("Uuid is required.")
                .NotNull().WithMessage("Uuid must not be null.");
        }
    }
}
