﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.PRMetrics.Domain.Entities
{
    public class StateEvent
    {
        public DateTime Timestamp { get; set; }
        public string State { get; set; } // "draft" or "published"

        public TimeSpan Interval { get; set; }

        public string IntervalContent { get; set; }

        public DateTime NextStateUpdatedDate { get; set; }
    }
}
