#!/usr/bin/env node

/**
 * Bundle Analysis Script for Frontend Optimization
 * 
 * This script analyzes the webpack bundle and provides optimization recommendations
 */

const fs = require('fs');
const path = require('path');

class BundleAnalyzer {
  constructor() {
    this.distPath = path.join(__dirname, 'dist');
    this.statsPath = path.join(__dirname, 'dist', 'stats.json');
  }

  analyze() {
    console.log('🔍 Analyzing bundle...\n');

    if (!fs.existsSync(this.distPath)) {
      console.error('❌ Dist folder not found. Please build the project first.');
      return;
    }

    this.analyzeFileSize();
    this.analyzeChunks();
    this.provideBundleOptimizations();
  }

  analyzeFileSize() {
    console.log('📊 File Size Analysis:');
    console.log('='.repeat(50));

    const files = this.getJSFiles();
    let totalSize = 0;

    files.forEach(file => {
      const filePath = path.join(this.distPath, file);
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      totalSize += stats.size;

      const status = this.getSizeStatus(stats.size);
      console.log(`${status} ${file}: ${sizeKB} KB`);
    });

    const totalSizeKB = (totalSize / 1024).toFixed(2);
    const totalSizeMB = (totalSize / (1024 * 1024)).toFixed(2);
    
    console.log(`\n📦 Total Bundle Size: ${totalSizeKB} KB (${totalSizeMB} MB)`);
    
    if (totalSize > 2 * 1024 * 1024) { // 2MB
      console.log('⚠️  Bundle size is large. Consider code splitting and lazy loading.');
    } else if (totalSize > 1 * 1024 * 1024) { // 1MB
      console.log('⚡ Bundle size is moderate. Some optimizations possible.');
    } else {
      console.log('✅ Bundle size is good!');
    }
    
    console.log('');
  }

  analyzeChunks() {
    console.log('🧩 Chunk Analysis:');
    console.log('='.repeat(50));

    const files = this.getJSFiles();
    const chunks = {
      main: files.filter(f => f.includes('main')),
      vendor: files.filter(f => f.includes('vendor') || f.includes('polyfills')),
      lazy: files.filter(f => !f.includes('main') && !f.includes('vendor') && !f.includes('polyfills'))
    };

    Object.entries(chunks).forEach(([type, chunkFiles]) => {
      if (chunkFiles.length > 0) {
        console.log(`\n${type.toUpperCase()} chunks:`);
        chunkFiles.forEach(file => {
          const filePath = path.join(this.distPath, file);
          const stats = fs.statSync(filePath);
          const sizeKB = (stats.size / 1024).toFixed(2);
          console.log(`  - ${file}: ${sizeKB} KB`);
        });
      }
    });

    console.log('');
  }

  provideBundleOptimizations() {
    console.log('💡 Optimization Recommendations:');
    console.log('='.repeat(50));

    const recommendations = [
      {
        title: 'Enable Tree Shaking',
        description: 'Remove unused code from bundles',
        implementation: 'Ensure "sideEffects": false in package.json for libraries'
      },
      {
        title: 'Implement Code Splitting',
        description: 'Split code into smaller chunks for better caching',
        implementation: 'Use dynamic imports: import("./module").then(...)'
      },
      {
        title: 'Lazy Load Routes',
        description: 'Load route components only when needed',
        implementation: 'Use loadChildren in route configuration'
      },
      {
        title: 'Optimize Dependencies',
        description: 'Use lighter alternatives for heavy libraries',
        implementation: 'Consider date-fns instead of moment.js, etc.'
      },
      {
        title: 'Enable Compression',
        description: 'Use gzip/brotli compression for smaller transfer sizes',
        implementation: 'Configure web server compression'
      },
      {
        title: 'Implement Service Worker',
        description: 'Cache resources for offline functionality',
        implementation: 'Add @angular/service-worker'
      }
    ];

    recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. ${rec.title}`);
      console.log(`   📝 ${rec.description}`);
      console.log(`   🔧 ${rec.implementation}\n`);
    });
  }

  getJSFiles() {
    if (!fs.existsSync(this.distPath)) {
      return [];
    }

    return fs.readdirSync(this.distPath)
      .filter(file => file.endsWith('.js'))
      .sort();
  }

  getSizeStatus(size) {
    if (size > 500 * 1024) return '🔴'; // > 500KB
    if (size > 250 * 1024) return '🟡'; // > 250KB
    return '🟢'; // <= 250KB
  }
}

// Performance monitoring recommendations
function providePerformanceRecommendations() {
  console.log('⚡ Performance Optimization Checklist:');
  console.log('='.repeat(50));

  const checklist = [
    '✅ Use OnPush change detection strategy',
    '✅ Implement virtual scrolling for large lists',
    '✅ Use trackBy functions in *ngFor',
    '✅ Lazy load images and components',
    '✅ Implement HTTP caching',
    '✅ Use memoization for expensive calculations',
    '✅ Optimize bundle size with tree shaking',
    '✅ Use Web Workers for heavy computations',
    '✅ Implement proper error boundaries',
    '✅ Monitor Core Web Vitals'
  ];

  checklist.forEach(item => console.log(item));
  console.log('');
}

// Main execution
if (require.main === module) {
  const analyzer = new BundleAnalyzer();
  analyzer.analyze();
  providePerformanceRecommendations();
}

module.exports = BundleAnalyzer;
