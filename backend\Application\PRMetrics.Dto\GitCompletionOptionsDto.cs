﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace  RIB.PRMetrics.Application.Dto
{
    /// <summary>
    /// Git Completion Option
    /// </summary>
    public class GitCompletionOptionsDto
    {
        /// <summary>
        /// Git Merge Commit Message
        /// </summary>
        public string MergeCommitMessage { get; set; }

        /// <summary>
        /// Git Delete Source Branch Flag
        /// </summary>
        public bool DeleteSourceBranch { get; set; }

        /// <summary>
        /// Git Merge Strategy
        /// </summary>
        public string MergeStrategy { get; set; }

        /// <summary>
        /// Git Transition Work Items
        /// </summary>
        public bool TransitionWorkItems { get; set; }
        
    }
}
