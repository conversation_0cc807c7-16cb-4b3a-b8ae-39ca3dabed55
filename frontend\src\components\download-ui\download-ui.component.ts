import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule, ProgressBarMode } from '@angular/material/progress-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Observable, Subscription } from 'rxjs';
import { DownloadProgress, DownloadProgressService } from '../../services/download-progress.service';
import { ExcelExportService } from '../../services/excel-export.service';

@Component({
  selector: 'app-download-ui',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatTooltipModule
  ],
  templateUrl: './download-ui.component.html',
  styleUrl: './download-ui.component.scss'
})
export class DownloadUiComponent implements OnInit, OnDestroy {
  @Input() downloadId!: string;
  @Input() showCard: boolean = true;

  downloadProgress$!: Observable<DownloadProgress>;
  currentProgress: DownloadProgress | null = null;
  private subscription?: Subscription;

  constructor(
    private downloadProgressService: DownloadProgressService,
    private excelExportService: ExcelExportService
  ) {}

  ngOnInit(): void {
    if (this.downloadId) {
      const progress$ = this.downloadProgressService.getDownloadProgress(this.downloadId);
      if (progress$) {
        this.downloadProgress$ = progress$;
        this.subscription = this.downloadProgress$.subscribe(progress => {
          this.currentProgress = progress;
        });
      }
    }
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  /**
   * Pause the download
   */
  async pauseDownload(): Promise<void> {
    if (this.downloadId) {
      try {
        await this.downloadProgressService.pauseDownload(this.downloadId);
      } catch (error) {
        console.error('Error pausing download:', error);
      }
    }
  }

  /**
   * Resume the download
   */
  async resumeDownload(): Promise<void> {
    if (this.downloadId) {
      try {
        await this.downloadProgressService.resumeDownload(this.downloadId);
      } catch (error) {
        console.error('Error resuming download:', error);
      }
    }
  }

  /**
   * Cancel the download
   */
  async cancelDownload(): Promise<void> {
    if (this.downloadId) {
      try {
        await this.downloadProgressService.cancelDownload(this.downloadId);
      } catch (error) {
        console.error('Error cancelling download:', error);
      }
    }
  }

  /**
   * Download the completed file
   */
  async downloadFile(): Promise<void> {
    if (this.downloadId && this.currentProgress?.fileName && this.isCompleted()) {
      try {
        await this.excelExportService.downloadExportFile(this.downloadId, this.currentProgress.fileName);
      } catch (error) {
        console.error('Error downloading file:', error);
      }
    }
  }

  /**
   * Get status icon based on download status
   */
  getStatusIcon(): string {
    if (!this.currentProgress) return 'download';
    
    switch (this.currentProgress.status) {
      case 'preparing':
        return 'hourglass_empty';
      case 'downloading':
        return 'download';
      case 'paused':
        return 'pause';
      case 'completed':
        return 'check_circle';
      case 'cancelled':
        return 'cancel';
      case 'error':
        return 'error';
      default:
        return 'download';
    }
  }

  /**
   * Get status color based on download status
   */
  getStatusColor(): string {
    if (!this.currentProgress) return 'primary';
    
    switch (this.currentProgress.status) {
      case 'preparing':
      case 'downloading':
        return 'primary';
      case 'paused':
        return 'accent';
      case 'completed':
        return 'primary';
      case 'cancelled':
        return 'warn';
      case 'error':
        return 'warn';
      default:
        return 'primary';
    }
  }

  /**
   * Get status text
   */
  getStatusText(): string {
    if (!this.currentProgress) return 'Preparing...';
    
    switch (this.currentProgress.status) {
      case 'preparing':
        return 'Preparing download...';
      case 'downloading':
        return 'Downloading...';
      case 'paused':
        return 'Paused';
      case 'completed':
        return 'Download completed';
      case 'cancelled':
        return 'Download cancelled';
      case 'error':
        return `Error: ${this.currentProgress.error || 'Unknown error'}`;
      default:
        return 'Unknown status';
    }
  }

  /**
   * Check if download can be paused
   */
  canPause(): boolean {
    return this.currentProgress?.status === 'downloading';
  }

  /**
   * Check if download can be resumed
   */
  canResume(): boolean {
    return this.currentProgress?.status === 'paused';
  }

  /**
   * Check if download can be cancelled
   */
  canCancel(): boolean {
    return this.currentProgress?.status === 'preparing' || 
           this.currentProgress?.status === 'downloading' || 
           this.currentProgress?.status === 'paused';
  }

  /**
   * Check if download is active (showing progress)
   */
  isActive(): boolean {
    return this.currentProgress?.status === 'preparing' || 
           this.currentProgress?.status === 'downloading' || 
           this.currentProgress?.status === 'paused';
  }

  /**
   * Check if download is completed
   */
  isCompleted(): boolean {
    return this.currentProgress?.status === 'completed';
  }

  /**
   * Check if download has error
   */
  hasError(): boolean {
    return this.currentProgress?.status === 'error';
  }

  /**
   * Get progress bar mode
   */
  getProgressMode(): ProgressBarMode {
    if (!this.currentProgress) return 'indeterminate';

    switch (this.currentProgress.status) {
      case 'preparing':
        return 'indeterminate';
      case 'downloading':
      case 'paused':
        return 'determinate';
      default:
        return 'determinate';
    }
  }

  /**
   * Get progress bar color
   */
  getProgressColor(): string {
    if (!this.currentProgress) return 'primary';
    
    switch (this.currentProgress.status) {
      case 'completed':
        return 'primary';
      case 'error':
      case 'cancelled':
        return 'warn';
      case 'paused':
        return 'accent';
      default:
        return 'primary';
    }
  }
}
