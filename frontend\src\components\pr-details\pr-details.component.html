<div class="pr-details-container">
  <!-- Back button -->
  <button mat-button class="back-button" (click)="goBack()">
    <mat-icon>arrow_back</mat-icon> Back to Pull Requests
  </button>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loader" style="width: 20px; height: 20px"></div>
    <p>Loading pull request details...</p>
  </div>

  <!-- Error message -->
  <div *ngIf="error && !isLoading" class="error-container">
    <mat-icon color="warn">error</mat-icon>
    <p>{{ error }}</p>
  </div>

  <!-- PR Details -->
  <div *ngIf="pullRequest && !isLoading" class="pr-content">
    <!-- PR Header Card -->
    <mat-card class="pr-header-card"> <mat-card-header>
        <div class="pr-title-container">
          <div class="pr-id-status">
            <span class="pr-id">#{{ pullRequest.pullRequestId }}</span>
            <span class="status-badge" [ngClass]="getStatusClass(pullRequest.status)">
              <span class="status-dot"></span>
              {{ pullRequest.status }}
            </span>
            <span *ngIf="pullRequest.isDraft === 'true'" class="draft-badge">DRAFT</span>
          </div>
          <h1 class="pr-title">{{ pullRequest.title }}</h1>
        </div>
      </mat-card-header>

      <mat-card-content>          <div class="pr-metadata">
            <div class="pr-author-container">
              <div class="pr-author">
                <div class="avatar-container" *ngIf="pullRequest.createdBy?.imageUrl">
                  <img [src]="pullRequest.createdBy.imageUrl" alt="Author avatar" class="author-avatar">
                </div>
                <div class="author-info">
                  <span class="author-name">{{ pullRequest.createdBy?.displayName }}</span>
                  <span class="creation-date">Created on {{ formatDate(pullRequest.creationDate) }}</span>
                </div>
              </div>
              <div class="comment-stats">
                <div class="stat-item" (click)="scrollToComments()" matTooltip="Click to view comments">
                  <div class="stat-badge total">
                    <span class="count">{{ getThreadsByStatus('fixed').length + getThreadsByStatus('active').length }}</span>
                  </div>
                  <span class="stat-label">Total Comments</span>
                </div>
                <div class="stat-item" (click)="scrollToComments()" matTooltip="Click to view active comments">
                  <div class="stat-badge active">
                    <span class="count">{{ getThreadsByStatus('active').length }}</span>
                  </div>
                  <span class="stat-label">Active</span>
                </div>
                <div class="stat-item" (click)="scrollToComments()" matTooltip="Click to view resolved comments">
                  <div class="stat-badge resolved">
                    <span class="count">{{ getThreadsByStatus('fixed').length }}</span>
                  </div>
                  <span class="stat-label">Resolved</span>
                </div>
              </div>
            </div>
            
            <!-- Draft mode metrics -->
            <div class="draft-metrics" *ngIf="draftModeMetrics">
              <h3 class="section-title">
                <mat-icon>schedule</mat-icon>
                PR State Metrics
              </h3>
              <div class="metrics-container">
                <div class="metric-group">
                  <div class="metric">
                    <span class="metric-label">Time in Draft:</span>
                    <span class="metric-value">{{ draftModeMetrics.totalDraftFormatted }}</span>
                  </div>
                  <div class="metric">
                    <span class="metric-label">Time in Published:</span>
                    <span class="metric-value">{{ draftModeMetrics.totalPublishedFormatted }}</span>
                  </div>
                </div>
                <!-- <div class="metric-chart">
                  <div class="chart-bar">
                    <div class="chart-segment draft" 
                         [style.width.%]="(draftModeMetrics.totalDraftHours / (draftModeMetrics.totalDraftHours + draftModeMetrics.totalPublishedHours)) * 100"
                         *ngIf="draftModeMetrics.totalDraftHours > 0"
                         matTooltip="Draft: {{ draftModeMetrics.totalDraftFormatted }}">
                    </div>
                    <div class="chart-segment published" 
                         [style.width.%]="(draftModeMetrics.totalPublishedHours / (draftModeMetrics.totalDraftHours + draftModeMetrics.totalPublishedHours)) * 100"
                         *ngIf="draftModeMetrics.totalPublishedHours > 0"
                         matTooltip="Published: {{ draftModeMetrics.totalPublishedFormatted }}">
                    </div>
                  </div>
                </div> -->
              </div>
            </div>

            <!-- Review Cycle Metrics -->
            <div class="review-cycle-metrics" *ngIf="reviewCycleMetrics">
              <h3 class="section-title">
                <mat-icon>loop</mat-icon>
                Review Cycle Metrics
              </h3>
              <div class="metrics-container">
                <div class="metric-group">
                  <div class="metric">
                    <span class="metric-label">Review Cycles:</span>
                    <span class="metric-value">{{ reviewCycleMetrics.cycleCount }}</span>
                  </div>
                  <div class="metric">
                    <span class="metric-label">Description:</span>
                    <span class="metric-value">{{ reviewCycleMetrics.description }}</span>
                  </div>
                </div>
                <div class="metric-chart" *ngIf="reviewCycleMetrics.cycleCount > 0">
                  <div class="chart-bar review-cycles">
                    <div *ngFor="let i of [].constructor(reviewCycleMetrics.cycleCount); let idx = index"
                         class="chart-segment cycle-segment"
                         [style.width.%]="(100 / reviewCycleMetrics.cycleCount)"
                         [ngClass]="'cycle-' + (idx % 4)"
                         matTooltip="Cycle {{ idx + 1 }}">
                    </div>
                  </div>
                </div>
              </div>
            </div>

          <div class="pr-branches">
            <div class="branch source-branch">
              <span class="branch-label">Source:</span>
              <div class="branch-info">
                <mat-icon class="branch-icon">call_split</mat-icon>
                <span>{{ formatBranchName(pullRequest.sourceRefName) }}</span>
              </div>
            </div>
            <mat-icon class="arrow-icon">arrow_forward</mat-icon>
            <div class="branch target-branch">
              <span class="branch-label">Target:</span>
              <div class="branch-info">
                <mat-icon class="branch-icon">merge_type</mat-icon>
                <span>{{ formatBranchName(pullRequest.targetRefName) }}</span>
              </div>
            </div>
          </div>

          <div class="pr-merge-status" *ngIf="pullRequest.mergeStatus">
            <span class="merge-label">Merge Status:</span>
            <span class="merge-value">{{ pullRequest.mergeStatus }}</span>
          </div>
        </div>

        <mat-divider></mat-divider>

        <div class="pr-description" *ngIf="pullRequest.description">
          <h3>Description</h3>
          <div class="description-text">{{ pullRequest.description }}</div>
        </div>

        <div class="pr-no-description" *ngIf="!pullRequest.description">
          <p>No description provided</p>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- PR Details Tabs -->
    <mat-card class="pr-details-card">
      <mat-tab-group animationDuration="300ms">
        <!-- Comments Tab -->
        <mat-tab label="Comments ({{ commentThreads.length }})">
          <div class="tab-content">
            <div *ngIf="commentThreads.length === 0" class="no-content">
              <mat-icon>forum</mat-icon>
              <p>No comments found for this pull request</p>
            </div>

            <div *ngIf="commentThreads.length > 0" class="comment-threads">
              <!-- Reviewer Activity Section -->
              <div class="reviewer-activity" *ngIf="reviewsCount && reviewsCount.length > 0">
                <h3 class="section-title">
                  <mat-icon>people</mat-icon>
                  Reviewer Activity
                </h3>
                <div class="reviewer-list">
                  <div class="reviewer-item" *ngFor="let reviewer of reviewsCount">
                    <div class="reviewer-name">{{ reviewer.displayName }}</div>
                    <div class="reviewer-counts">
                      <span class="active-count">{{ reviewer.activeCommentCount }} active</span>
                      <span class="resolved-count">{{ reviewer.resolveCommentCount }} resolved</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Active Comments Section -->
              <div class="thread-section" *ngIf="getThreadsByStatus('active').length > 0">
                <h3 class="section-title">
                  <mat-icon>comment</mat-icon>
                  Active Comments ({{ getThreadsByStatus('active').length }})
                </h3>
                <div class="thread-cards">
                  <mat-card class="thread-card" *ngFor="let thread of getThreadsByStatus('active')">
                    <mat-card-header>
                      <div class="thread-header-content">
                        <span class="thread-status" [ngClass]="getStatusClass('active')">Active</span>
                        <span class="thread-id">Thread #{{ thread.id }}</span>
                      </div>
                    </mat-card-header>

                    <mat-card-content>
                      <div class="thread-context" *ngIf="thread.pullRequestThreadContext">
                        <div class="file-path">
                          <mat-icon>description</mat-icon>
                          <span>{{ thread.pullRequestThreadContext.filePath }}</span>
                        </div>

                        <div class="code-context" *ngIf="thread.pullRequestThreadContext.rightFileStart">
                          Line {{ thread.pullRequestThreadContext.rightFileStart.line }}
                        </div>
                      </div>

                      <div class="thread-timeline">
                        <div class="timeline-item" *ngFor="let comment of thread.comments">
                          <div class="timeline-avatar">
                            <mat-icon>person</mat-icon>
                          </div>
                          <div class="timeline-content">
                            <div class="comment-header">
                              <span class="comment-author">{{ comment.author?.displayName }}</span>
                              <span class="comment-date">{{ formatDate(comment.publishedDate) }}</span>
                            </div>
                            <div class="comment-content">
                              <ng-container *ngIf="comment.content">
                                <span *ngIf="comment.content | userGuidToName:patToken | async as replacedContent; else rawContent">{{ replacedContent }}</span>
                                <ng-template #rawContent>{{ comment.content }}</ng-template>
                              </ng-container>
                            </div>
                            <!-- <div class="comment-metadata" style="display: flex;" *ngIf="comment.activeComment">
                              <mat-icon>schedule</mat-icon>                              <span style="margin-left: 2px;">Response time: {{ comment.responseTimeFormatted || formatLegacyDuration(comment.activeComment) }}</span>
                            </div> -->
                          </div>
                        </div>
                      </div>

                      <div class="thread-metrics">
                        <div class="metric">
                          <mat-icon>hourglass_full</mat-icon>
                          <span>Thread duration: {{ thread.activeDurationFormatted || formatLegacyDuration(thread.commentActiveDuration) }}</span>
                        </div>
                        <div class="metric" *ngIf="thread.hoursSincePRCreationFormatted">
                          <mat-icon>history</mat-icon>
                          <span>Created after PR: {{ thread.hoursSincePRCreationFormatted }}</span>
                        </div>
                      </div>
                    </mat-card-content>
                  </mat-card>
                </div>
              </div>

              <!-- Fixed/Resolved Comments Section -->
              <div class="thread-section" *ngIf="getThreadsByStatus('fixed').length > 0">
                <h3 class="section-title">
                  <mat-icon>done_all</mat-icon>
                  Resolved Comments ({{ getThreadsByStatus('fixed').length }})
                </h3>
                <div class="thread-cards">
                  <mat-card class="thread-card" *ngFor="let thread of getThreadsByStatus('fixed')">
                    <mat-card-header>
                      <div class="thread-header-content">
                        <span class="thread-status" [ngClass]="getStatusClass('fixed')">Resolved</span>
                        <span class="thread-id">Thread #{{ thread.id }}</span>
                      </div>
                    </mat-card-header>

                    <mat-card-content>
                      <div class="thread-context" *ngIf="thread.pullRequestThreadContext">
                        <div class="file-path">
                          <mat-icon>description</mat-icon>
                          <span>{{ thread.pullRequestThreadContext.filePath }}</span>
                        </div>
                      </div>

                      <div class="thread-timeline">
                        <div class="timeline-item" *ngFor="let comment of thread.comments">
                          <div class="timeline-avatar">
                            <mat-icon>person</mat-icon>
                          </div>
                          <div class="timeline-content">
                            <div class="comment-header">
                              <span class="comment-author">{{ comment.author.displayName }}</span>
                              <span class="comment-date">{{ formatDate(comment.publishedDate) }}</span>
                            </div>
                            <div class="comment-content">
                              <ng-container *ngIf="comment.content">
                                <span *ngIf="comment.content | userGuidToName:patToken | async as replacedContent; else rawContent">{{ replacedContent }}</span>
                                <ng-template #rawContent>{{ comment.content }}</ng-template>
                              </ng-container>
                            </div>
                            <!-- <div class="comment-metadata" *ngIf="comment.responseTimeFormatted">
                              <mat-icon>schedule</mat-icon>
                              <span style="margin-left: 2px;">Response time: {{ comment.responseTimeFormatted }}</span>
                            </div> -->
                          </div>
                        </div>
                      </div>
                      
                      <!-- Thread metrics for fixed threads -->
                      <div class="thread-metrics">
                        <div class="metric" *ngIf="thread.fixedDurationFormatted">
                          <mat-icon>timer_off</mat-icon>
                          <span>Time to fix: {{ thread.fixedDurationFormatted }}</span>
                        </div>
                        <div class="metric" *ngIf="thread.hoursSincePRCreationFormatted">
                          <mat-icon>history</mat-icon>
                          <span>Created after PR: {{ thread.hoursSincePRCreationFormatted }}</span>
                        </div>
                      </div>
                    </mat-card-content>
                  </mat-card>
                </div>
              </div>

              <!-- Other Status Comments -->
              <div class="thread-section" *ngFor="let status of ['closed', 'wontfix', 'pending']">
                <ng-container *ngIf="getThreadsByStatus(status).length > 0">
                  <h3 class="section-title">
                    <mat-icon>{{ status === 'closed' ? 'cancel' : (status === 'wontfix' ? 'block' : 'pause_circle')
                      }}</mat-icon>
                    {{ status.charAt(0).toUpperCase() + status.slice(1) }} Comments ({{
                    getThreadsByStatus(status).length }})
                  </h3>
                  <div class="thread-cards">
                    <mat-card class="thread-card" *ngFor="let thread of getThreadsByStatus(status)">
                      <mat-card-header>
                        <div class="thread-header-content">
                          <span class="thread-status" [ngClass]="getStatusClass(status)">{{
                            status.charAt(0).toUpperCase() + status.slice(1) }}</span>
                          <span class="thread-id">Thread #{{ thread.id }}</span>
                        </div>
                      </mat-card-header>

                      <mat-card-content>
                        <div class="thread-context" *ngIf="thread.pullRequestThreadContext">
                          <div class="file-path">
                            <mat-icon>description</mat-icon>
                            <span>{{ thread.pullRequestThreadContext.filePath }}</span>
                          </div>
                        </div>

                        <div class="thread-timeline">
                          <div class="timeline-item" *ngFor="let comment of thread.comments">
                            <div class="timeline-avatar">
                              <mat-icon>person</mat-icon>
                            </div>
                            <div class="timeline-content">
                              <div class="comment-header">
                                <span class="comment-author">{{ comment.author.displayName }}</span>
                                <span class="comment-date">{{ formatDate(comment.publishedDate) }}</span>
                              </div>
                              <div class="comment-content">
                                <ng-container *ngIf="comment.content">
                                  <span *ngIf="comment.content | userGuidToName:patToken | async as replacedContent; else rawContent">{{ replacedContent }}</span>
                                  <ng-template #rawContent>{{ comment.content }}</ng-template>
                                </ng-container>
                              </div>
                            </div>
                          </div>
                        </div>
                      </mat-card-content>
                    </mat-card>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
        </mat-tab>

        <!-- Work Items Tab -->
        <mat-tab label="Work Items">
          <div class="tab-content">
            <div *ngIf="!pullRequest.workItemRefs || pullRequest.workItemRefs.length === 0" class="no-content">
              <mat-icon>task</mat-icon>
              <p>No work items linked to this pull request</p>
            </div>

            <div *ngIf="pullRequest.workItemRefs && pullRequest.workItemRefs.length > 0" class="work-items">
              <div *ngFor="let workItem of pullRequest.workItemRefs" class="work-item">
                <span class="work-item-id">#{{ workItem.id }}</span>
                <span class="work-item-title">{{ workItem.title || 'Work Item' }}</span>
              </div>
            </div>
          </div>
        </mat-tab>

        <!-- Reviewers Tab -->
        <mat-tab label="Reviewers">
          <div class="tab-content">
            <div *ngIf="!pullRequest.reviewers || pullRequest.reviewers.length === 0" class="no-content">
              <mat-icon>people</mat-icon>
              <p>No reviewers assigned to this pull request</p>
            </div>

            <div *ngIf="pullRequest.reviewers && pullRequest.reviewers.length > 0" class="reviewers">
              <div *ngFor="let reviewer of pullRequest.reviewers" class="reviewer">
                <div class="reviewer-avatar" *ngIf="reviewer.imageUrl">
                  <img [src]="reviewer.imageUrl" alt="Reviewer avatar">
                </div>
                <div class="reviewer-info">
                  <span class="reviewer-name">{{ reviewer.displayName }} <span style="color: red;">{{reviewer.isRequired
                      ? '*' : ''}}</span></span>
                  <span class="reviewer-vote" [ngClass]="{
                    'approved': reviewer.vote === 10,
                    'approved-with-suggestions': reviewer.vote === 5,
                    'no-vote': reviewer.vote === 0,
                    'waiting': reviewer.vote === -5,
                    'rejected': reviewer.vote === -10
                  }">
                    <mat-icon *ngIf="reviewer.vote === 10">check_circle</mat-icon>
                    <mat-icon *ngIf="reviewer.vote === 5">thumb_up</mat-icon>
                    <mat-icon *ngIf="reviewer.vote === 0">radio_button_unchecked</mat-icon>
                    <mat-icon *ngIf="reviewer.vote === -5">hourglass_empty</mat-icon>
                    <mat-icon *ngIf="reviewer.vote === -10">cancel</mat-icon>
                    {{
                    reviewer.vote === 10 ? 'Approved' :
                    reviewer.vote === 5 ? 'Approved with suggestions' :
                    reviewer.vote === 0 ? 'No vote' :
                    reviewer.vote === -5 ? 'Waiting for author' :
                    'Rejected'
                    }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </mat-card>
  </div>
</div>