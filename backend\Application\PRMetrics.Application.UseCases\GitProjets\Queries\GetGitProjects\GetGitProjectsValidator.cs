﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;

namespace  RIB.PRMetrics.Application.UseCases.GitProjets.Queries.GetGitProjets
{
    /// <summary>
    /// Validator for GetGitProjectsQuery using FluentValidation.
    /// Ensures that the required Personal Access Token is provided.
    /// </summary>
    internal class GetGitProjectsValidator : AbstractValidator<GetGitProjectsQuery>
    {
        public GetGitProjectsValidator()
        {
            // Validate that the PATToken is not null or empty
            RuleFor(x => x.PATToken)
                .NotEmpty().WithMessage("PAT Token must not be empty.")
                .NotNull().WithMessage("PAT Token is required.");
        }
    }
}

