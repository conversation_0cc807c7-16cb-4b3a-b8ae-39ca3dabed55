<div class="header-layout">
  <header class="app-header">
    <div class="header-left">
      <button *ngIf="isLoggedIn" mat-icon-button class="menu-button" (click)="toggleSidenav()">
        <mat-icon>menu</mat-icon>
      </button>
      <img src="assets/rib-logo-text.svg" alt="RIB Logo" class="header-logo">
      <h2 class="header-title">DevOps 4.0</h2>
    </div>
    <div class="header-actions" *ngIf="isLoggedIn">
      <!-- User info display -->
      <div class="user-info">
        <span class="user-name">{{ userDisplayName }}</span>
        <span class="user-email" *ngIf="userEmail">{{ userEmail }}</span>
      </div>

      <!-- User profile icon with dropdown menu -->
      <button mat-icon-button [matMenuTriggerFor]="userMenu" class="user-profile-button">
        <mat-icon>account_circle</mat-icon>
      </button>

      <!-- User dropdown menu -->
      <mat-menu #userMenu="matMenu" xPosition="before">
        <div class="menu-user-info" mat-menu-item disabled>
          <div class="menu-user-details">
            <div class="menu-user-name">{{ userDisplayName }}</div>
            <div class="menu-user-email" *ngIf="userEmail">{{ userEmail }}</div>
          </div>
        </div>
        <mat-divider></mat-divider>
        <button mat-menu-item>
          <mat-icon>person</mat-icon>
          <span>Your Profile</span>
        </button>
        <button mat-menu-item (click)="logout()">
          <mat-icon>logout</mat-icon>
          <span>Logout</span>
        </button>
      </mat-menu>
    </div>
  </header>

  <mat-sidenav-container class="sidenav-container" [hasBackdrop]="true">
    <mat-sidenav #sidenav mode="over" opened="false" [@slideInOut]="sideNavState" class="sidenav" *ngIf="isLoggedIn"
      (backdropClick)="sidenav.close()">
      <div class="sidenav-header">
        <h2>PR Metrics</h2>
        <!-- <button mat-icon-button class="close-sidenav-button" (click)="sidenav.close()">
          <mat-icon>close</mat-icon>
        </button> -->
      </div>
      <mat-nav-list>
        <a mat-list-item routerLink="/dashboard" [class.active]="currentRoute === '/dashboard'"
          (click)="sidenav.close()">
          <mat-icon>dashboard</mat-icon>
          <span>Dashboard</span>
        </a>
        <a mat-list-item routerLink="/pull-requests" [class.active]="currentRoute === '/pull-requests'"
          (click)="sidenav.close()">
          <mat-icon>compare_arrows</mat-icon>
          <span>Pull Requests</span>
        </a>
        <a mat-list-item routerLink="/threads" [class.active]="currentRoute === '/threads'" (click)="sidenav.close()">
          <mat-icon>forum</mat-icon>
          <span>Threads</span>
        </a>
      </mat-nav-list>
    </mat-sidenav>

    <mat-sidenav-content>
      <div class="content-container">
        <ng-content></ng-content>
      </div>
    </mat-sidenav-content>
  </mat-sidenav-container>
</div>