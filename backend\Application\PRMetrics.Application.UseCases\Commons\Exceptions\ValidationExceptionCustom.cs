﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using   RIB.PRMetrics.Application.UseCases.Commons.Bases;

namespace  RIB.PRMetrics.Application.UseCases.Commons.Exceptions
{
    /// <summary>
    /// Custom exception class used to represent validation errors.
    /// This exception is thrown when one or more validation failures occur.
    /// </summary>
    public class ValidationExceptionCustom : Exception
    {
        /// <summary>
        /// Gets a collection of validation errors associated with this exception.
        /// </summary>
        public IEnumerable<BaseError> Errors { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="ValidationExceptionCustom"/> class
        /// with a default error message and an empty list of errors.
        /// </summary>
        public ValidationExceptionCustom()
            : base("One or more validation failures have occurred.")
        {
            Errors = new List<BaseError>(); // Initialize an empty list for errors.
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ValidationExceptionCustom"/> class
        /// with a collection of validation errors.
        /// </summary>
        /// <param name="errors">A collection of validation errors that caused the exception.</param>
        public ValidationExceptionCustom(IEnumerable<BaseError> errors)
            : this() // Call the default constructor to set the default message.
        {
            Errors = errors; // Set the provided validation errors.
        }
    }
}
