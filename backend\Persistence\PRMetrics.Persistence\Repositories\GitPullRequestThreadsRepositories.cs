﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using System;
using System.Net;
using System.Xml.Linq;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Domain.Entities;
using RIB.PRMetrics.Domain.Entities.CommentThreads;
using RIB.PRMetrics.Persistence.Common;
using RIB.PRMetrics.Persistence.Commont;

namespace RIB.PRMetrics.Persistence.Repositories
{
    /// <summary>
    /// Repository class responsible for fetching Git pull request threads.
    /// Implements the <see cref="IGitPullRequestThreadsRepository"/> interface.
    /// </summary>
    public class GitPullRequestThreadsRepositories : IGitPullRequestThreadsRepository
    {
        private readonly AzureDevopsUrlBuilder azureDevopsUrlBuilder;  // Helper class to build the Azure DevOps API URL.
        private readonly AZDevopsClientCommon aZDevopsClientCommon;    // Client for making HTTP requests to Azure DevOps API.
        private readonly IGitPullRequestRepository gitPullRequestRepository;
        //private readonly IMemoryCache cache;
        /// <summary>
        /// Constructor to initialize dependencies.
        /// </summary>
        /// <param name="_azureDevopsUrlBuilder">Instance of AzureDevopsUrlBuilder to build API URL.</param>
        /// <param name="aZDevopsClientCommon">Common client for making requests to Azure DevOps.</param>

        public GitPullRequestThreadsRepositories(AzureDevopsUrlBuilder _azureDevopsUrlBuilder, AZDevopsClientCommon aZDevopsClientCommon, IMemoryCache _cache)
        {
            azureDevopsUrlBuilder = _azureDevopsUrlBuilder ?? throw new ArgumentNullException(nameof(azureDevopsUrlBuilder));
            this.aZDevopsClientCommon = aZDevopsClientCommon;

            this.gitPullRequestRepository = new GitPullRequestRepository(_azureDevopsUrlBuilder, aZDevopsClientCommon,_cache);
        }

        /// <summary>
        /// Fetches the Git pull request threads for a given PullRequestId.
        /// </summary>
        /// <param name="gitCommon">Contains common Git information like project and repositories.</param>
        /// <param name="pullRequestId">The ID of the pull request for which threads need to be fetched.</param>
        /// <param name="prAuthorId">The ID of the pull request Author.</param>
        /// <returns>Returns a Task containing the response with pull request threads.</returns>
        /// <exception cref="Exception">Throws an exception if the API response is not successful.</exception>
        public async Task<GitPullRequestThreadsResponse> GetGitPullRequestThreadsAsync(GitCommon gitCommon, int pullRequestId, string prAuthorId)
        {
            // Build the URI using the provided Git information and pull request ID
            var url = azureDevopsUrlBuilder.BuildGitPullRequestThreadsUri(gitCommon, pullRequestId);

            // Make the GET request to the Azure DevOps API to fetch pull request threads
            var response = await aZDevopsClientCommon.GetAsync(url, gitCommon.PATToken);

            // Check if the response is successful and status code is OK
            if (response.IsSuccessStatusCode && response.StatusCode == HttpStatusCode.OK)
            {
                // Read the response body as a string
                var jsonResponse = await response.Content.ReadAsStringAsync();

                // Deserialize the JSON response into a GitCommonResponse object containing pull request threads
                var pullRequestsThreadsResponse = JsonConvert.DeserializeObject<GitCommonResponse<GitPullRequestThread>>(jsonResponse);

                var threadsResponse = new GitPullRequestThreadsResponse();

                // If the Value property contains pull request threads, process them
                if (pullRequestsThreadsResponse.Value != null)
                {
                    // Prepare draft/published states based on comments in the threads
                    threadsResponse.draftMode = PrepareDraftMode(pullRequestsThreadsResponse);
                    threadsResponse.commentThread = PrepareCommentThreadAsync(pullRequestsThreadsResponse, prAuthorId).Result;

                    // Return the response containing the pull request threads
                    return threadsResponse ?? new GitPullRequestThreadsResponse();
                }
            }

            // If response is not successful, throw an exception with the error message
            throw new Exception($"Error retrieving pull requests threads: {response.ReasonPhrase}");
        }

        /// <summary>
        /// Processes the pull request threads to extract state events such as "draft" and "published" states.
        /// Calculates the total time spent in each state and creates state events.
        /// </summary>
        /// <param name="gitCommonResponse">The response containing the pull request threads.</param>
        /// <returns>Returns a GitPullRequestThreadsMode object with state events and total time spent in each state.</returns>
        private GitPullRequestThreadsMode PrepareDraftMode(GitCommonResponse<GitPullRequestThread> gitCommonResponse)
        {
            //// List to store state events (draft/published state transitions)
            //var stateEvents = new List<StateEvent>();

            //// Loop through each thread in the response
            //foreach (var thread in gitCommonResponse.Value)
            //{
            //    // If the thread has comments, check each comment for state change events
            //    if (thread.Comments != null)
            //    {
            //        foreach (var comment in thread.Comments)
            //        {
            //            var contentLower = comment.Content?.ToLowerInvariant();

            //            // Check if comment indicates the pull request was marked as a draft
            //            if (contentLower?.Contains(" marked the pull request as a draft") == true)
            //            {
            //                stateEvents.Add(new StateEvent { Timestamp = comment.PublishedDate, State = "draft" });
            //            }
            //            // Check if comment indicates the pull request was published
            //            else if (contentLower?.Contains("published the pull request") == true)
            //            {
            //                stateEvents.Add(new StateEvent { Timestamp = comment.PublishedDate, State = "published" });
            //            }
            //        }
            //    }
            //}

            //// Sort state events by timestamp to process them in chronological order
            //stateEvents.Sort((se1, se2) => se1.Timestamp.CompareTo(se2.Timestamp));

            //// Calculate the interval time for each state event (time between two state transitions)
            //for (int i = 0; i < stateEvents.Count; i++)
            //{
            //    var stateEvent = stateEvents[i];
            //    DateTime endTime = (i + 1 < stateEvents.Count) ? stateEvents[i + 1].Timestamp.DateTime : DateTime.UtcNow;


            //    stateEvent.NextStateUpdatedDate=endTime;

            //}


            //return new GitPullRequestThreadsMode()
            //{
            //    StateEvents = stateEvents,

            //};

            // Use LINQ to flatten and filter the comments in a single pass.
            // This is often more readable and can be optimized by the JIT compiler.
            var stateEvents = gitCommonResponse.Value
                .Where(thread => thread.Comments != null) // Avoid null reference exceptions
                .SelectMany(thread => thread.Comments)    // Flatten all comments into a single sequence
                .Where(comment => comment.Content != null) // Filter out comments with no content early
                .Select(comment =>
                {
                    var contentLower = comment.Content.ToLowerInvariant();
                    if (contentLower.Contains(" marked the pull request as a draft"))
                    {
                        return new StateEvent { Timestamp = comment.PublishedDate, State = "draft" };
                    }
                    else if (contentLower.Contains("published the pull request"))
                    {
                        return new StateEvent { Timestamp = comment.PublishedDate, State = "published" };
                    }
                    return null; // Return null for comments that are not state events
                })
                .Where(stateEvent => stateEvent != null) // Filter out the nulls
                .OrderBy(stateEvent => stateEvent.Timestamp) // Sort at the end, after filtering
                .ToList(); // Materialize into a list only once

            // Optimize the interval calculation loop
            // Use DateTimeOffset.UtcNow for consistency with PublishedDate (if using DateTimeOffset)
            //DateTimeOffset now = DateTimeOffset.UtcNow; // Capture once outside the loop
           

            for (int i = 0; i < stateEvents.Count; i++)
            {
                var stateEvent = stateEvents[i];
                // Use the actual DateTimeOffset value from the next event, or 'now' if it's the last event
                stateEvent.NextStateUpdatedDate = (i + 1 < stateEvents.Count)
                    ? stateEvents[i + 1].Timestamp
                    : DateTime.UtcNow;
            }

            return new GitPullRequestThreadsMode
            {
                StateEvents = stateEvents,
            };
        }


        /// <summary>
        /// Prepares the <see cref="GitPullRequestCommentThread"/> object from raw comment thread data,
        /// enriching it with calculated metrics such as working hours, response delays, and comment statistics.
        /// </summary>
        /// <param name="gitCommonResponse">The response containing raw comment thread data fetched from Azure DevOps.</param>
        /// <param name="authorId">The unique identifier of the pull request Author.</param>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result contains the fully populated
        /// <see cref="GitPullRequestCommentThread"/> object.
        /// </returns>
        private async Task<GitPullRequestCommentThread> PrepareCommentThreadAsync(GitCommonResponse<GitPullRequestThread> gitCommonResponse, string authorId)
        {
            var gitPRCommentThread = new GitPullRequestCommentThread();

            // Capture UtcNow once for potential last comment calculation
            DateTimeOffset currentUtcNow = DateTimeOffset.UtcNow;

            gitPRCommentThread.CommentThreads = gitCommonResponse.Value
                .Where(thread => thread.Status is "active" or "fixed")
                .Select(thread => // Project each GitPullRequestThread into a CommentThread
                {
                    var commentsForThisThread = new List<Comment>();

                    if (thread.Comments != null && thread.Comments.Any()) // Ensure comments exist before processing
                    {
                        // Use Select with an index to get 'i' and 'isLast' like in a for loop
                        commentsForThisThread.AddRange(thread.Comments
                            .Select((comment, i) => new { comment, i, isLast = i == thread.Comments.Count - 1 })
                            .Where(x => x.comment != null && x.comment.CommentType == "text" && !string.IsNullOrWhiteSpace(x.comment.Content))
                            .Select(x => // Project into your 'Comment' DTO
                            {
                                DateTimeOffset responseTimestamp;
                                if (x.isLast)
                                {
                                    responseTimestamp = (thread.Status == "active") ? currentUtcNow : thread.LastUpdatedDate;
                                }
                                else
                                {
                                    // Ensure next comment exists before accessing its PublishedDate
                                    // Use null-conditional and null-coalescing for safety
                                    responseTimestamp = thread.Comments[x.i + 1]?.PublishedDate ?? currentUtcNow;
                                }

                                return new Comment
                                {
                                    Id = x.comment.Id,
                                    ParentCommentId = x.comment.ParentCommentId,
                                    Content = x.comment.Content,
                                    ThreadType = (x.comment.Author?.Id == authorId) ? "review-response" : "review-comment",
                                    PublishedDate = x.comment.PublishedDate,
                                    LastUpdatedDate = x.comment.LastUpdatedDate,
                                    Author = x.comment.Author,
                                    // IMPORTANT: Consider changing ResponseUpdatedDate to DateTimeOffset in your Comment class
                                    ResponseUpdatedDate = responseTimestamp.DateTime
                                };
                            })
                        );
                    }

                    // Only create and return a CommentThread if it has relevant comments
                    return commentsForThisThread.Any()
                        ? new CommentThread
                        {
                            Id = thread.Id,
                            Status = thread.Status,
                            PublishedDate = thread.PublishedDate,
                            LastUpdatedDate = thread.LastUpdatedDate,
                            Comments = commentsForThisThread
                        }
                        : null; // Return null if no comments, to be filtered out later
                })
                .Where(commentThread => commentThread != null) // Filter out null CommentThreads
                .ToList(); // Materialize the list of CommentThreads

            return gitPRCommentThread;
        }

}
}
