﻿################################################################################
# This .gitignore file was automatically created by Microsoft(R) Visual Studio.
################################################################################


## User-specific files
.idea/
.vscode/
.vs/

## Windows Installer files
Thumbs.db
ehthumbs.db

## .NET Core build results
bin/
obj/
out/

## User-specific files
*.user
*.userosscache
*.suo
*.userprefs

## Windows Installer files
*.cab
*.msi
*.msm
*.msp

## JetBrains Rider files
.idea/

## ASP.NET Scaffolding
ScaffoldingReadMe.txt

## VS Code directories
.vscode/

# Rider
.idea/

# User-specific files
*.suo
*.user
*.userosscache
*.sln.docstates

# .NET Core global tool cache
.dotnet/

# VS for Mac
.vs/

# .NET Core Runtime, SDK and Tools cache
.nuget/packages/

# ASP.NET Core generated files
wwwroot/

# Data files
*.sqlite
*.mdf
*.ldf

# Logs and temp files
logs/
*.log
*.dmp
*.bak

# Temporary build files
*.tmp
