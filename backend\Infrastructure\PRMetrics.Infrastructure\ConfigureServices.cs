﻿using Microsoft.Extensions.DependencyInjection;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Infrastructure.BackgroundJobs;
using RIB.PRMetrics.Infrastructure.Services;


namespace RIB.PRMetrics.Infrastructure
{
    /// <summary>
    /// Provides extension methods to configure the dependency injection (DI) container for the persistence layer.
    /// This class registers all the services, repositories, and common utilities used in the persistence layer.
    /// </summary>
    public static class ConfigureServices
    {
        /// <summary>
        /// Registers all persistence-related services with the dependency injection container.
        /// This includes the configuration of repositories, common utilities, and contexts for Azure DevOps interactions.
        /// </summary>
        /// <param name="services">The <see cref="IServiceCollection"/> used to register services.</param>
        /// <returns>The updated <see cref="IServiceCollection"/> with persistence-related services added.</returns>
        public static IServiceCollection AddInjectionInfrastructure(this IServiceCollection services)
        {
            

            // Registering scoped services which are created once per request/operation
            services.AddSingleton<IJobQueue, InMemoryJobQueue>(); // Git repository related operations
            services.AddScoped<IJobService, JobService>(); // Git pull request operations
            services.AddHostedService<JobWorker>();
            services.AddSingleton<IDeleteJobQueue, InMemoryDeleteJobQueue>();
            services.AddSingleton<IJobTracker, InMemoryJobTracker>();
            services.AddHostedService<FileCleanupWorker>();

            // Return the services collection for chaining
            return services;
        }
    }
}
