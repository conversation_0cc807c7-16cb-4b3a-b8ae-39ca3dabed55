﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Microsoft.AspNetCore.Mvc;
using MediatR;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetPullRequests;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestById;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestStatusCount;
using RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestConflicts;

namespace RIB.PRMetrics.WebApi.Controllers
{
    /// <summary>
    /// Controller responsible for handling Git pull request-related API requests.
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class GitPullRequestsController : ControllerBase
    {
        private readonly IMediator _mediator;

        /// <summary>
        /// Initializes a new instance of the <see cref="GitPullRequestsController"/> class.
        /// </summary>
        /// <param name="mediator">The mediator used to send queries to the application layer.</param>
        public GitPullRequestsController(IMediator mediator)
        {
            _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
        }

        /// <summary>
        /// Handles GET requests to retrieve Git pull requests based on the provided query.
        /// </summary>
        /// <param name="query">The query object containing parameters for retrieving Git pull requests.</param>
        /// <returns>A response containing the list of Git pull requests, or a BadRequest if unsuccessful.</returns>
        [HttpGet("GetGitPullRequest")]
        public async Task<IActionResult> GetGitPullRequest([FromQuery] GetGitPullRequestQuery query)
        {
            // Send the query to the mediator to handle the request and retrieve the data.
            var response = await _mediator.Send(query);

            // Return a successful response if the query was successful, otherwise return a BadRequest.
            if (response.Succcess)
            {
                return Ok(response);
            }

            return BadRequest(response);
        }

        /// <summary>
        /// Handles GET requests to retrieve a specific Git pull request by its ID.
        /// </summary>
        /// <param name="query">The query object containing the ID of the Git pull request to retrieve.</param>
        /// <returns>A response containing the Git pull request details, or a BadRequest if unsuccessful.</returns>
        [HttpGet("GetGitPullRequestById")]
        public async Task<IActionResult> GetGitPullRequestById([FromQuery] GetGitPullRequestByIdQuery query)
        {
            // Send the query to the mediator to handle the request and retrieve the data.
            var response = await _mediator.Send(query);

            // Return a successful response if the query was successful, otherwise return a BadRequest.
            if (response.Succcess)
            {
                return Ok(response);
            }

            return BadRequest(response);
        }

        /// <summary>
        /// Handles GET requests to retrieve Git pull request conflicts based on the provided query.
        /// </summary>
        /// <param name="query">The query object containing parameters for retrieving Git pull request conflicts.</param>
        /// <returns>A response containing the list of Git pull request conflicts, or a BadRequest if unsuccessful.</returns>
        [HttpGet("GetGitPullRequestConflicts")]
        public async Task<IActionResult> GetGitPullRequestConflicts([FromQuery] GetGitPullRequestConflictsQuery query)
        {
            // Send the query to the mediator to handle the request and retrieve the data.
            var response = await _mediator.Send(query);

            // Return a successful response if the query was successful, otherwise return a BadRequest.
            if (response.Succcess)
            {
                return Ok(response);
            }

            return BadRequest(response);
        }


        /// <summary>
        /// Retrieves the count of Git pull requests for a specified status (e.g., active, completed, abandoned)
        /// from Azure DevOps based on the provided query parameters.
        /// </summary>
        /// <param name="query">The query parameters including project name, repository name, status, etc.</param>
        /// <returns>
        /// Returns an <see cref="IActionResult"/> containing the status count if successful; otherwise, a BadRequest result.
        /// </returns>
        [HttpGet("GetGitPullRequestStatusCount")]
        public async Task<IActionResult> GetGitPullRequestStatusCountAsync([FromQuery] GetGitPullRequestStatusCountQuery query)
        {
            // Send the query to the mediator to handle the business logic and return the response.
            var response = await _mediator.Send(query);

            // If the query was successful, return an HTTP 200 OK with the result.
            if (response.Succcess)
            {
                return Ok(response);
            }

            // If the query failed, return an HTTP 400 Bad Request with the response object.
            return BadRequest(response);
        }

    }
}
