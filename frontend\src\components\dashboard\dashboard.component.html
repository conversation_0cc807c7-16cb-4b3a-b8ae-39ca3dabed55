<div class="dashboard-container">
  <div *ngIf="!credentials" class="no-credentials">
    <mat-card>
      <mat-card-header>
        <mat-card-title>Dashboard</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <p>Please login with your PAT token to view analytics.</p>
      </mat-card-content>
    </mat-card>
  </div>

  <div *ngIf="credentials" class="dashboard-content">
    <div class="dashboard-header">
      <h1>Pull Request Analytics Dashboard</h1>
      <div class="action-buttons">
        <button mat-raised-button color="primary" (click)="loadDashboardData()" [disabled]="loading">
          <span *ngIf="!loading">Refresh Data</span>
          <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
        </button>
        <button mat-icon-button *ngIf="debugMode" color="accent" matTooltip="Debug mode active"
          (click)="debugMode = false">
          <mat-icon>bug_report</mat-icon>
        </button>
        <button mat-icon-button *ngIf="!debugMode" matTooltip="Enable debug mode" (click)="debugMode = true">
          <mat-icon>bug_report</mat-icon>
        </button>
      </div>
    </div>

    <div *ngIf="loading" class="loading-spinner">
      <mat-spinner></mat-spinner>
    </div>

    <div *ngIf="!loading && prData.length === 0" class="no-data">
      <mat-card>
        <mat-card-content>
          <p>No PR data available. Click 'Refresh Data' to load.</p>
        </mat-card-content>
      </mat-card>
    </div>

    <div *ngIf="!loading && prData.length > 0" class="charts-container"> <!-- PR Summary Stats -->
      <mat-card class="stats-card">
        <mat-card-header>
          <mat-card-title>Summary Statistics
            <mat-icon matTooltip="Shows key PR metrics at a glance">info</mat-icon>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">22,456</div>
              <div class="stat-label">Total PRs</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">22,000</div>
              <div class="stat-label">Completed PRs</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">175</div>
              <div class="stat-label">Active PRs</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">228</div>
              <div class="stat-label">Abandoned PRs</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">12</div>
              <div class="stat-label">Draft PRs</div>
            </div>
          </div>
        </mat-card-content>
      </mat-card> <!-- PR Status Distribution Chart -->
      <mat-card class="chart-card">
        <mat-card-header>
          <mat-card-title>PR Status Distribution
            <mat-icon matTooltip="Shows the distribution of PRs by their current status">info</mat-icon>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="chart-wrapper">
            <div *ngIf="hasNoStatusData()" class="no-data-message">
              No PR status data available
            </div>
            <canvas baseChart [type]="'pie'" [data]="prStatusChartData" [options]="prStatusChartOptions"
              [legend]="true">
            </canvas>
          </div>
        </mat-card-content>
      </mat-card> <!-- PR Timeline Chart -->
      <mat-card class="chart-card">
        <mat-card-header>
          <mat-card-title>PR Creation Timeline
            <mat-icon matTooltip="Shows PR creation activity over the last 30 days">info</mat-icon>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="chart-wrapper">
            <div *ngIf="hasNoTimelineData()" class="no-data-message">
              No PR timeline data available
            </div>
            <canvas baseChart [type]="'bar'" [data]="prTimelineChartData" [options]="prTimelineChartOptions"
              [legend]="true">
            </canvas>
          </div>
        </mat-card-content>
      </mat-card><!-- PR Review Time Chart -->
      <mat-card class="chart-card">
        <mat-card-header>
          <mat-card-title>PR Review Time Distribution
            <mat-icon matTooltip="Shows how long PRs take to be reviewed and merged">info</mat-icon>
          </mat-card-title>
        </mat-card-header> <mat-card-content>
          <div class="chart-wrapper">
            <div *ngIf="hasNoReviewTimeData()" class="no-data-message">
              No completed PRs with valid date information available
            </div>
            <canvas baseChart [type]="'doughnut'" [data]="prReviewTimeChartData" [options]="prReviewTimeChartOptions"
              [legend]="true">
            </canvas>
          </div>
          <div *ngIf="debugMode" class="debug-info">
            <p>Debug Info:</p>
            <ul>
              <li>Chart data: {{prReviewTimeChartData.datasets[0].data | json}}</li>
              <li>Completed PRs: {{getCompletedPRCount()}}</li>
              <li>PRs with creation date: {{getPRsWithCreationDateCount()}}</li>
              <li>PRs with closed date: {{getPRsWithClosedDateCount()}}</li>
            </ul>
          </div>
        </mat-card-content>
      </mat-card> <!-- Repository Activity Chart -->
      <mat-card class="chart-card">
        <mat-card-header>
          <mat-card-title>Repository Activity
            <mat-icon matTooltip="Shows various repository activity metrics in a radar chart">info</mat-icon>
          </mat-card-title>
        </mat-card-header> <mat-card-content>
          <div class="chart-wrapper">
            <div *ngIf="hasNoActivityData()" class="no-data-message">
              No repository activity data available
            </div>
            <canvas baseChart [type]="'radar'" [data]="repoActivityChartData" [options]="repoActivityChartOptions"
              [legend]="true">
            </canvas>
          </div>
          <div *ngIf="debugMode" class="debug-info">
            <p>Debug Info:</p>
            <ul>
              <li>Chart data: {{repoActivityChartData.datasets[0].data | json}}</li>
              <li>Total PRs: {{prData.length}}</li>
              <li>Merged PRs: {{getCompletedPRCount()}}</li>
              <li>Abandoned PRs: {{getAbandonedPRCount()}}</li>
            </ul>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>