﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.105.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Core" Version="1.2.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Application\PRMetrics.Interface\RIB.PRMetrics.Application.Interface.csproj" />
    <ProjectReference Include="..\..\Domain\PRMetrics.Domain\RIB.PRMetrics.Domain.csproj" />
  </ItemGroup>

</Project>
