﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto
{
    /// <summary>
    /// Represents a comment associated with a pull request in a Git repository.
    /// This DTO encapsulates details about the comment such as its content, author, timestamps, and related metadata.
    /// </summary>
    public class GitPullRequestCommentDto
    {
        /// <summary>
        /// Gets or sets the unique identifier for the comment.
        /// This ID is used to uniquely identify the comment within the system.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the ID of the parent comment.
        /// This allows for threaded or nested comments where a comment can be a reply to another comment.
        /// If there is no parent, this field will typically be 0 or null.
        /// </summary>
        public int ParentCommentId { get; set; }

        /// <summary>
        /// Gets or sets the content of the comment.
        /// This field contains the actual text or message that the author wrote for the comment.
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// Gets or sets the published date of the comment.
        /// This is the timestamp indicating when the comment was originally posted.
        /// </summary>
        public DateTime PublishedDate { get; set; }

        /// <summary>
        /// Gets or sets the last updated date of the comment.
        /// This timestamp represents the last time the comment was edited or updated.
        /// </summary>
        public DateTime LastUpdatedDate { get; set; }

        /// <summary>
        /// Gets or sets the author of the comment.
        /// This property references the user who created the comment, typically using a `UserDto` object.
        /// </summary>
        public UserDto Author { get; set; }

        /// <summary>
        /// Gets or sets the type of the comment.
        /// This could indicate whether the comment is a normal comment, a review comment, or any other categorization.
        /// </summary>
        public string CommentType { get; set; }
    }
}
