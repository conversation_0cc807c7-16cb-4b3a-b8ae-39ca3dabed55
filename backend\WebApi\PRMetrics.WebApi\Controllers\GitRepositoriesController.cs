﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Microsoft.AspNetCore.Mvc;
using MediatR;
using   RIB.PRMetrics.Application.UseCases.GitRepositories.Queries.GetGitRepositories;

namespace  RIB.PRMetrics.WebApi.Controllers
{
    /// <summary>
    /// Controller responsible for handling Git repository-related API requests.
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class GitRepositoriesController : ControllerBase
    {
        private readonly IMediator _mediator;

        /// <summary>
        /// Initializes a new instance of the <see cref="GitRepositoriesController"/> class.
        /// </summary>
        /// <param name="mediator">The mediator used to send queries to the application layer.</param>
        public GitRepositoriesController(IMediator mediator)
        {
            _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
        }

        /// <summary>
        /// Handles GET requests to retrieve a list of Git repositories.
        /// </summary>
        /// <param name="query">The query object containing parameters for retrieving Git repositories.</param>
        /// <returns>A response containing a list of Git repositories, or a BadRequest if unsuccessful.</returns>
        [HttpGet("GetGitRepositories")]
        public async Task<IActionResult> GetGitRepositories([FromQuery] GetGitRepositoriesQuery query)
        {
            // Send the query to the mediator to handle the request.
            var response = await _mediator.Send(query);

            // Return a successful response if the query was successful, otherwise return a BadRequest.
            if (response.Succcess)
            {
                return Ok(response);
            }

            return BadRequest(response);
        }

        /// <summary>
        /// Handles GET requests to retrieve a specific Git repository by its ID.
        /// </summary>
        /// <param name="query">The query object containing the ID of the Git repository to retrieve.</param>
        /// <returns>A response containing the Git repository details, or a BadRequest if unsuccessful.</returns>
        [HttpGet("GetGitRepositoryById")]
        public async Task<IActionResult> GetGitRepositoryById([FromQuery] GetGitRepositoryByIdQuery query)
        {
            // Send the query to the mediator to handle the request.
            var response = await _mediator.Send(query);

            // Return a successful response if the query was successful, otherwise return a BadRequest.
            if (response.Succcess)
            {
                return Ok(response);
            }

            return BadRequest(response);
        }
    }
}
