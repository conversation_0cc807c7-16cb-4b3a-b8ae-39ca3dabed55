﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace  RIB.PRMetrics.Application.UseCases.Commons.Behaviours
{
    /// <summary>
    /// A logging behavior that logs the request and response of each MediatR request.
    /// It implements the <see cref="IPipelineBehavior{TRequest,TResponse}"/> interface.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request.</typeparam>
    /// <typeparam name="TResponse">The type of the response.</typeparam>
    public class LoggingBehaviour<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse> where TRequest : IRequest<TResponse>
    {
        private readonly ILogger<LoggingBehaviour<TRequest, TResponse>> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="LoggingBehaviour{TRequest,TResponse}"/> class.
        /// </summary>
        /// <param name="logger">An instance of <see cref="ILogger{LoggingBehaviour{TRequest,TResponse}}"/> used for logging request and response details.</param>
        public LoggingBehaviour(ILogger<LoggingBehaviour<TRequest, TResponse>> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles the logging of the request and response, and then passes the request to the next handler in the pipeline.
        /// </summary>
        /// <param name="request">The request being processed.</param>
        /// <param name="next">A delegate that allows the request to continue to the next handler in the pipeline.</param>
        /// <param name="cancellationToken">A token used to propagate notifications that the operation should be canceled.</param>
        /// <returns>The response generated by the request handler.</returns>
        public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
        {
            // Log the incoming request
            _logger.LogInformation("Request Handling: { name } {@request }", typeof(TRequest).Name, JsonSerializer.Serialize(request));

            // Pass the request to the next handler in the pipeline and get the response
            var response = await next();

            // Log the outgoing response
            _logger.LogInformation("Response Handling: { name } {@response }", typeof(TResponse).Name, JsonSerializer.Serialize(response));

            return response;
        }
    }
}
