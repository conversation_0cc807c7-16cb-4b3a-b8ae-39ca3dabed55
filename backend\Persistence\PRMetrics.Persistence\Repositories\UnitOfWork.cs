﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using   RIB.PRMetrics.Application.Interface.Persistence;

namespace  RIB.PRMetrics.Persistence.Repositories
{
    /// <summary>
    /// Represents the Unit of Work pattern, which coordinates the work of multiple repositories.
    /// This class ensures that all operations on repositories are handled within a single transaction-like scope.
    /// It allows the consumer to access repositories like GitRepo, GitPullRequest, and GitProject in a unified manner.
    /// </summary>
    internal class UnitOfWork : IUnitOfWork
    {
        /// <summary>
        /// Gets the repository responsible for interacting with Git repositories.
        /// </summary>
        public IGitRepoRepository GitRepoRepository { get; }

        /// <summary>
        /// Gets the repository responsible for interacting with Git pull requests.
        /// </summary>
        public IGitPullRequestRepository GitPullRequestsRepository { get; }

        /// <summary>
        /// Gets the repository responsible for interacting with Git project repositories.
        /// </summary>
        public IGitProjectRepository GitProjectRepository { get; }

        /// <summary>
        /// Represents a repository for handling Git pull request threads operations.
        /// </summary>
        public IGitPullRequestThreadsRepository GitPullRequestThreadsRepository { get; set; }

        /// <summary>
        /// Represents a repository for querying Git contributions hierarchy.
        /// </summary>
        public IContributionsHierarchyQueryRepository ContributionsHierarchyQueryRepository { get; }

        /// <summary>
        /// Represents a repository for handling Git pull request build timeline operations.
        /// </summary>
        public IGitPullRequestBuildTimeLineRepository GitPullRequestBuildTimeLineRepository { get; }

        public IIdentityPickerRepository IdentityPickerRepository { get; }


        /// <summary>
        /// Initializes a new instance of the <see cref="UnitOfWork"/> class with the provided repositories.
        /// The constructor allows for dependency injection of repositories for Git repositories, Git pull requests, and Git projects.
        /// </summary>
        /// <param name="_gitRepositories">An instance of the <see cref="IGitRepoRepository"/> for working with Git repositories.</param>
        /// <param name="_gitPullRequests">An instance of the <see cref="IGitPullRequestRepository"/> for working with Git pull requests.</param>
        /// <param name="gitProjectRepositories">An instance of the <see cref="IGitProjectRepository"/> for working with Git projects.</param>
        public UnitOfWork(IGitRepoRepository _gitRepoRepository, IGitPullRequestRepository _gitPullRequestsRepository, IGitProjectRepository _gitProjectRepository, IGitPullRequestThreadsRepository _gitPullRequestThreadsRepository, IContributionsHierarchyQueryRepository _contributionsHierarchyQueryRepository, IGitPullRequestBuildTimeLineRepository _gitPullRequestBuildTimeLineRepository, IIdentityPickerRepository _identityPickerRepository)
        {
            GitRepoRepository = _gitRepoRepository;
            GitPullRequestsRepository = _gitPullRequestsRepository;
            GitProjectRepository = _gitProjectRepository;
            GitPullRequestThreadsRepository = _gitPullRequestThreadsRepository;
            ContributionsHierarchyQueryRepository = _contributionsHierarchyQueryRepository;
            GitPullRequestBuildTimeLineRepository = _gitPullRequestBuildTimeLineRepository;  
            IdentityPickerRepository = _identityPickerRepository;
        }

        /// <summary>
        /// Disposes the UnitOfWork instance, releasing any resources held by it.
        /// Calls <see cref="System.GC.SuppressFinalize"/> to prevent unnecessary finalization.
        /// </summary>
        public void Dispose()
        {
            System.GC.SuppressFinalize(this);
        }
    }
}
