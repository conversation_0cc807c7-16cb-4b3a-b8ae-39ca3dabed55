/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using RIB.PRMetrics.Application.UseCases;
using RIB.PRMetrics.Persistence;
using RIB.PRMetrics.Infrastructure;
using Serilog;
using System.Text.Json;
using RIB.PRMetrics.Infrastructure.SignalR;

namespace  RIB.PRMetrics.WebApi
{
    /// <summary>
    /// Entry point for the PRMetrics Web API application.
    /// Configures the application's services, logging, and middleware pipeline.
    /// </summary>
    public class Program
    {
        /// <summary>
        /// Main method that runs the application, configuring services, middleware, and request pipeline.
        /// </summary>
        /// <param name="args">Command-line arguments.</param>
        public static void Main(string[] args)
        {
            // Configure logging using Serilog. Logs are written to the console.
            Log.Logger = new LoggerConfiguration()
                    .WriteTo.Console() // Console logging
                                       //.WriteTo.File("logs/PRMetricsApp_log.txt", rollingInterval: RollingInterval.Day) // Uncomment to enable file logging
                    .CreateLogger();

            // Create the application builder
            var builder = WebApplication.CreateBuilder(args);

            // Add services to the container
            builder.Services.AddControllers(); // Add controllers for API handling
            
            builder.Host.UseSerilog(); // Use Serilog for request logging

            builder.Services.AddMemoryCache(); // Use of Memory Cache 

            builder.Services.AddSignalR();

            // Configure CORS to allow requests from Angular running on localhost:4200
            builder.Services.AddCors(options =>
            {
                options.AddPolicy("AllowAngularLocalhost", policy =>
                {
                    policy.WithOrigins("http://localhost:4200")  // Allow requests from Angular app on localhost
                          .AllowAnyHeader() // Allow any headers
                          .AllowAnyMethod() // Allow any HTTP method
                          .AllowCredentials(); // Required for SignalR
                });
            });

            // Configure Swagger for API documentation and exploration
            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen();

            // Add HttpClient service
            builder.Services.AddHttpClient();

            // Add persistence and application layer services
            builder.Services.AddInjectionPersistence();
            builder.Services.AddInjectionApplication();
            builder.Services.AddInjectionInfrastructure();

            // Build the application
            var app = builder.Build();

            

            // Use CORS policy for allowing Angular app to communicate
            app.UseCors("AllowAngularLocalhost");



            // Use Serilog for request logging
            app.UseSerilogRequestLogging();

            // Enable Swagger UI for API documentation in development
            app.UseSwagger();
            app.UseSwaggerUI();

            // Enable authorization middleware
            app.UseAuthorization();

            app.UseRouting();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapHub<ProgressHub>("/progressHub");
            });

            app.UseStaticFiles(new StaticFileOptions
            {
                FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(
                Path.Combine(Directory.GetCurrentDirectory(), "TempFiles")),
                RequestPath = "/files"
            });

            // Map controllers to handle incoming requests
            // app.MapControllers();

            // Run the application
            app.Run();
        }
    }
}
