﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto.Contributions.HierarchyQuery
{
    /// <summary>
    /// Represents the type of a policy within the hierarchy query context.
    /// This DTO encapsulates details about the policy type, such as its ID, URL, and display name.
    /// </summary>
    public class PolicyTypeDto
    {
        /// <summary>
        /// Gets or sets the unique identifier for the policy type.
        /// This ID can be used to uniquely identify and reference the policy type in the system.
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the URL associated with the policy type.
        /// This URL can be used to access more information or documentation related to the policy type.
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        /// Gets or sets the display name of the policy type.
        /// This name is used to present the policy type in a human-readable format, often in the UI.
        /// </summary>
        public string DisplayName { get; set; }
    }
}
