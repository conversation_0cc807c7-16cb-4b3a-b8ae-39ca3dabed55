﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Newtonsoft.Json;

namespace RIB.PRMetrics.Domain.Entities.PullRequestBuild
{
    public class Log
    {
        /// <summary>
        /// Gets or sets the unique identifier for the log entry.
        /// </summary>
        [JsonProperty("id")]
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the URL where the log can be accessed.
        /// </summary>
        [JsonProperty("url")]
        public string Url { get; set; }
    }
}
