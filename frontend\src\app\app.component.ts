import { Component, OnInit } from '@angular/core';
import { RouterModule, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { PrDataService } from '../services/pr-data.service';
import { HeaderComponent } from 'src/components/header/header.component';

@Component({
  standalone: true,
  imports: [
    RouterModule,
    CommonModule,
    HeaderComponent
  ],
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit {
  title = 'PR Metrics Dashboard';
  isLoggedIn = false;

  constructor(
    private router: Router,
    private prDataService: PrDataService
  ) {}

  ngOnInit() {
    // Subscribe to auth state changes
    this.prDataService.credentials$.subscribe(credentials => {
      this.isLoggedIn = !!credentials;
      
      // If logged in and at root, redirect to pull requests
      if (this.isLoggedIn && this.router.url === '/') {
        this.router.navigate(['/pull-requests']);
      } else if (!this.isLoggedIn && this.router.url !== '/') {
        // If not logged in and not at login page, redirect to login
        this.router.navigate(['/']);
      }
    });
  }
}
