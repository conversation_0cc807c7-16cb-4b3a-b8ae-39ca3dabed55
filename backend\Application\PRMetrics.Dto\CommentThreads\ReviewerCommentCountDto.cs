﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto.CommentThreads
{
    /// <summary>
    /// Data Transfer Object (DTO) representing the count of comments made by a reviewer.
    /// Includes the counts of active and resolved comments.
    /// </summary>
    public class ReviewerCommentCountDto : UserDto
    {
        /// <summary>
        /// Gets or sets the number of resolved comments made by the reviewer.
        /// Resolved comments are those that have been addressed or closed.
        /// </summary>
        public int ResolveCommentCount { get; set; }

        /// <summary>
        /// Gets or sets the number of active comments made by the reviewer.
        /// Active comments are those that are still open and require attention.
        /// </summary>
        public int ActiveCommentCount { get; set; }
    }
}
