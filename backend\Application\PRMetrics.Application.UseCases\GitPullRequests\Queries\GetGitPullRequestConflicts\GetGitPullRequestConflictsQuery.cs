﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using Microsoft.AspNetCore.Mvc;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using RIB.PRMetrics.Domain.Entities;
using System.ComponentModel.DataAnnotations;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestConflicts
{
    /// <summary>
    /// Represents the query to retrieve Git pull request conflicts for a specific pull request ID.
    /// This class inherits from the <see cref="GitCommon"/> class and implements the MediatR IRequest interface,
    /// which is used by the handler to process the query.
    /// </summary>
    public class GetGitPullRequestConflictsQuery : GitCommon, IRequest<BaseReponseGeneric<List<GitPullRequestConflictDto>>>
    {
        /// <summary>
        /// Gets or sets the PullRequestId for which to retrieve the conflicts.
        /// This property is marked with the <see cref="RequiredAttribute"/> to enforce that the ID is provided,
        /// and <see cref="FromQueryAttribute"/> indicates that the value should be fetched from the query string of the HTTP request.
        /// </summary>
        [Required(ErrorMessage = "PullRequestId is required.")]
        [FromQuery(Name = "pullRequestId")]
        public int PullRequestId { get; set; }
    }
}
