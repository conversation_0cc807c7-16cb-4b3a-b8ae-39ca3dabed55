﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using System.ComponentModel.DataAnnotations;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetExcelReport
{
    public class GetExcelReportQuery : IRequest<BaseReponseGeneric<ExportExcelReportDto>>
    {
        [Required(ErrorMessage = "Uuid is required.")]
        [FromQuery(Name = "uuid")]
        public string Uuid { get; set; }

    }
}
