﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto.Contributions.HierarchyQuery
{
    /// <summary>
    /// Represents the data providers for a hierarchy query related to contributions.
    /// This class encapsulates the data provider for pull request details.
    /// </summary>
    public class DataProvidersDto
    {
        /// <summary>
        /// Gets or sets the data provider for pull request details.
        /// This property contains data and configuration related to a specific pull request.
        /// </summary>
        public PullRequestDataProviderDto prDetailDataProvider { get; set; }
    }
}
