﻿using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Domain.Entities.JobStatus;
using RIB.PRMetrics.Domain.Entities;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Caching.Memory;
using RIB.PRMetrics.Infrastructure.SignalR;
using DocumentFormat.OpenXml.Presentation;

namespace RIB.PRMetrics.Infrastructure.Services
{
    public class InMemoryJobTracker : IJobTracker
    {
        private readonly IMemoryCache _cache;
        private readonly IHubContext<ProgressHub> _hub;

        public InMemoryJobTracker(IMemoryCache cache,
            IHubContext<ProgressHub> hub)
        {
            _cache = cache;
            _hub = hub;
        }
        private readonly ConcurrentDictionary<Guid, ExportJob> _jobs = new();

        public void Add(ExportJob job) => _jobs[job.Uuid] = job;

        public ExportJob Get(Guid uuid) => _jobs.TryGetValue(uuid, out var job) ? job : null;

        public void Pause(Guid uuid, ProcessProgress process=null)
        {
            if (_jobs.TryGetValue(uuid, out var job))
            {
                if (JobStatus.Running == job.Status)
                {
                    job.Status = JobStatus.Paused;
                    job.PauseHandle.Reset();
                    if (process != null)
                    {
                        _cache.Set(job.Uuid.ToString(), process);

                        _hub.Clients.All.SendAsync("ReceiveProgress", process);
                    }
                }
            }
        }

        public void Resume(Guid uuid, ProcessProgress process=null)
        {
            if (_jobs.TryGetValue(uuid, out var job))
            {
               if (JobStatus.Paused == job.Status) { 
                    job.Status = JobStatus.Running;
                job.PauseHandle.Set();
                if (process != null)
                {
                    _cache.Set(job.Uuid.ToString(), process);
                    _hub.Clients.All.SendAsync("ReceiveProgress", process);
                }
               }
            }
        }

        public void Cancel(Guid uuid, ProcessProgress process=null)
        {
            if (_jobs.TryGetValue(uuid, out var job))
            {
                job.Status = JobStatus.Cancelled;
                job.TokenSource.Cancel();
                if (process != null)
                {
                    _cache.Remove(job.Uuid);
                    // _cache.Set(job.Uuid.ToString(), process);
                    process.FileUrl = "";
                    _hub.Clients.All.SendAsync("ReceiveProgress", process);
                }
            }
        }
    }

}
