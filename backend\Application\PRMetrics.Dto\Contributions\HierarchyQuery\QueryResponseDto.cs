﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto.Contributions.HierarchyQuery
{
    /// <summary>
    /// Represents the response from a query related to contributions or hierarchy.
    /// This DTO encapsulates the data providers required to process or display the query results.
    /// </summary>
    public class QueryResponseDto
    {
        /// <summary>
        /// Gets or sets the data providers associated with the query response.
        /// The data providers may include information related to pull requests or other contribution-related data.
        /// </summary>
        public DataProvidersDto DataProviders { get; set; }
    }
}
