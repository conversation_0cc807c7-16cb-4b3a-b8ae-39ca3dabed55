﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using System.Text;
using Microsoft.Extensions.Configuration;
using   RIB.PRMetrics.Domain.Entities;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace  RIB.PRMetrics.Persistence.Common
{
    /// <summary>
    /// This class is responsible for constructing various Azure DevOps API URLs 
    /// based on the provided search criteria and configuration settings.
    /// The class uses settings such as the base URL, organization, and API version 
    /// from the application's configuration and builds URLs for Git repositories, 
    /// pull requests, and projects.
    /// </summary>
    public class AzureDevopsUrlBuilder
    {
        private readonly IConfiguration _configuration;
        private readonly string _url;
        private string _organization;
        private readonly string _apiVersion;

        public string Organization
        {
            get { return _organization; }
            set
            {
                // 'value' is the implicit parameter that holds the incoming value
                if (string.IsNullOrWhiteSpace(value))
                {
                    // Optional: Add validation or custom logic here
                    // throw new ArgumentException("Organization name cannot be empty.");
                    _organization = "itwo40"; // Example: set a default
                }
                else
                {
                    _organization = value; // Assign the incoming value to the backing field
                }
            }
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="AzureDevopsUrlBuilder"/> class.
        /// This constructor retrieves configuration settings like base URL, organization, 
        /// and API version from the application's configuration.
        /// </summary>
        /// <param name="configuration">The application's configuration.</param>
        /// <exception cref="ArgumentNullException">Thrown when the <paramref name="configuration"/> is null.</exception>
        public AzureDevopsUrlBuilder(IConfiguration configuration)
        {
            this._configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _url = configuration["AzureDevopsContext:Url"];
            _organization = configuration["AzureDevopsContext:Organization"];
            _apiVersion = configuration["AzureDevopsContext:ApiVersion"];
        }

        /// <summary>
        /// Builds the URI for pulling request data based on the provided criteria and optional pull request ID.
        /// This method constructs a URL that can be used to fetch pull requests, either a list or a specific pull request,
        /// from Azure DevOps.
        /// </summary>
        /// <param name="criteria">The search criteria for filtering the pull requests.</param>
        /// <param name="pullRequestId">The optional pull request ID (if 0, fetches a list of pull requests).</param>
        /// <returns>The fully constructed URI string for querying pull requests.</returns>
        public string BuildPullRequestRequestUri(PullRequestSearchCriteria criteria, int? pullRequestId = 0)
        {
            StringBuilder uriBuilder;
            if (pullRequestId == 0)
            {
                uriBuilder = new StringBuilder($"{_url}{this._organization}/{criteria.Project}/_apis/git/repositories/{criteria.Repositories}/pullrequests?api-version={_apiVersion}");
            }
            else
            {
                uriBuilder = new StringBuilder($"{_url}{this._organization}/{criteria.Project}/_apis/git/repositories/{criteria.Repositories}/pullrequests/{pullRequestId}?api-version={this._apiVersion}");
            }

            var queryParams = new List<string>();

            // Add filters based on the provided criteria
            if (!string.IsNullOrEmpty(criteria.Status))
            {
                queryParams.Add($"searchCriteria.status={criteria.Status}");
            }
            if (!string.IsNullOrEmpty(criteria.CreatorId))
            {
                queryParams.Add($"searchCriteria.creatorId={criteria.CreatorId}");
            }
            if (!string.IsNullOrEmpty(criteria.AssigneeId))
            {
                queryParams.Add($"searchCriteria.assigneeId={criteria.AssigneeId}");
            }
            if (!string.IsNullOrEmpty(criteria.SourceRefName))
            {
                queryParams.Add($"searchCriteria.sourceRefName={criteria.SourceRefName}");
            }
            if (!string.IsNullOrEmpty(criteria.TargetRefName))
            {
                queryParams.Add($"searchCriteria.targetRefName={criteria.TargetRefName}");
            }

            if (criteria.Top.HasValue)
            {
                queryParams.Add($"$top={criteria.Top.Value}");
            }
            if (criteria.Skip.HasValue)
            {
                queryParams.Add($"$skip={criteria.Skip.Value}");
            }
            if (criteria.IncludeLinks.HasValue && criteria.IncludeLinks.Value)
            {
                queryParams.Add($"searchCriteria.includeLinks={criteria.IncludeLinks}");
            }

            if (criteria.MaxTime.HasValue)
            {
                queryParams.Add($"searchCriteria.maxTime={Uri.EscapeDataString(criteria.MaxTime?.ToString("o"))}");
            }

            if (criteria.MinTime.HasValue)
            {
                queryParams.Add($"searchCriteria.minTime={Uri.EscapeDataString(criteria.MinTime?.ToString("o"))}");
            }

            if (!string.IsNullOrEmpty(criteria.ReviewerId))
            {
                queryParams.Add($"searchCriteria.reviewerId={criteria.ReviewerId}");
            }

            if (!string.IsNullOrEmpty(criteria.SourceRepositoryId))
            {
                queryParams.Add($"searchCriteria.sourceRepositoryId={criteria.SourceRepositoryId}");
            }

            // Append query parameters to the URI
            if (queryParams.Count > 0)
            {
                uriBuilder.Append("&" + string.Join("&", queryParams));
            }

            return uriBuilder.ToString();
        }

        /// <summary>
        /// Builds the URI for fetching all Git repositories in a given project.
        /// </summary>
        /// <param name="project">The project name for which the Git repositories need to be fetched.</param>
        /// <returns>The URI string to fetch the Git repositories for the given project.</returns>
        public string BuildGitRepositoryUri(string project)
        {
            var uriBuilder = new StringBuilder($"{_url}{this._organization}/{project}/_apis/git/repositories?api-version={this._apiVersion}");
            return uriBuilder.ToString();
        }

        /// <summary>
        /// Builds the URI to fetch details of a Git project based on its project ID.
        /// If no project ID is provided, this method returns a URI to fetch all projects.
        /// </summary>
        /// <param name="projectId">The optional project ID to fetch specific project details.</param>
        /// <returns>The URI string to fetch Git project details.</returns>
        public string BuildGitProjectUri(string projectId)
        {
            StringBuilder uriBuilder;
            if (projectId == null)
            {
                uriBuilder = new StringBuilder($"{_url}{this._organization}/_apis/projects?api-version={this._apiVersion}");
            }
            else
            {
                uriBuilder = new StringBuilder($"{_url}{this._organization}/_apis/projects/{projectId}?api-version={this._apiVersion}");
            }

            return uriBuilder.ToString();
        }

        /// <summary>
        /// Builds the URI to fetch a specific Git repository within a project.
        /// </summary>
        /// <param name="gitCommon">Contains project and repository details.</param>
        /// <param name="repository">The repository name to fetch details for.</param>
        /// <returns>The URI string to fetch details for a specific Git repository within a project.</returns>
        public string BuildGitRepositoryUri(GitCommon gitCommon, string repository)
        {
            var uriBuilder = new StringBuilder($"{_url}{this._organization}/{gitCommon.Project}/_apis/git/repositories/{repository}?api-version={this._apiVersion}");
            return uriBuilder.ToString();
        }


        /// <summary>
        /// Builds the URI (Uniform Resource Identifier) for accessing Git Pull Request Threads API.
        /// </summary>
        /// <param name="gitCommon">Contains common Git information like project and repositories.</param>
        /// <param name="PullRequestId">The ID of the pull request for which threads are to be fetched.</param>
        /// <returns>A string representing the complete URI to fetch pull request threads from the Git API.</returns>
        public string BuildGitPullRequestThreadsUri(GitCommon gitCommon, int PullRequestId)
        {
            // Use StringBuilder to construct the URI string for accessing the Git pull request threads API.
            var uriBuilder = new StringBuilder($"{_url}{this._organization}/{gitCommon.Project}/_apis/git/repositories/{gitCommon.Repositories}/pullrequests/{PullRequestId}/threads?api-version={this._apiVersion}");

            // Convert the StringBuilder to a string and return it.
            return uriBuilder.ToString();
        }

        /// <summary>
        /// Builds the URI string for accessing the Git Pull Request Contribution HierarchyQuery API.
        /// </summary>
        /// <param name="projectId">The project ID to be included in the URI.</param>
        /// <returns>
        /// Returns the URI string for the Git Pull Request Contribution HierarchyQuery API endpoint.
        /// </returns>
        public string BuildGitPullRequestContributionHierarchyQueryUri(string projectId)
        {
            // Use StringBuilder to efficiently construct the URI string.
            var uriBuilder = new StringBuilder($"{_url}{this._organization}/_apis/Contribution/HierarchyQuery/project/{projectId}?api-version={this._apiVersion}");

            // Convert the StringBuilder to a string and return it as the final URI.
            return uriBuilder.ToString();
        }

        /// <summary>
        /// Builds the URI string for accessing the Git Pull Request Build Timeline API.
        /// </summary>
        /// <param name="projectId">The project ID to be included in the URI.</param>
        /// <param name="buildId">The build ID to be included in the URI.</param>
        /// <returns>
        /// Returns the URI string for the Git Pull Request Build Timeline API endpoint.
        /// </returns>
        public string BuildGitPullRequestBuildTimeLineUri(string projectId, string buildId)
        {
            // Use StringBuilder to efficiently construct the URI string.
            var uriBuilder = new StringBuilder($"{_url}{this._organization}/{projectId}/_apis/build/builds/{buildId}/timeline");

            // Convert the StringBuilder to a string and return it as the final URI.
            return uriBuilder.ToString();
        }

        /// <summary>
        /// Builds the URI (Uniform Resource Identifier) for accessing Git Pull Request Conflicts API.
        /// </summary>
        /// <param name="gitCommon">Contains common Git information like project and repositories.</param>
        /// <param name="PullRequestId">The ID of the pull request for which threads are to be fetched.</param>
        /// <returns>A string representing the complete URI to fetch pull request threads from the Git API.</returns>
        public string BuildGitPullRequestConflictsUri(GitCommon gitCommon, int PullRequestId)
        {
            // Use StringBuilder to construct the URI string for accessing the Git pull request threads API.
            var uriBuilder = new StringBuilder($"{_url}{this._organization}/{gitCommon.Project}/_apis/git/repositories/{gitCommon.Repositories}/pullrequests/{PullRequestId}/conflicts?api-version={this._apiVersion}");

            // Convert the StringBuilder to a string and return it.
            return uriBuilder.ToString();
        }

        /// <summary>
        /// Constructs and returns the full Azure DevOps Identity Picker API URI.
        /// </summary>
        /// <returns>
        /// A string representing the complete URI to be used for identity picker queries.
        /// </returns>        
        public string BuildIdentityPickerUri()
        {
            // Use StringBuilder to efficiently construct the URI string.
            var uriBuilder = new StringBuilder($"{_url}{this._organization}/_apis/IdentityPicker/Identities?api-version={this._apiVersion}");

            // Convert the StringBuilder to a string and return it as the final URI.
            return uriBuilder.ToString();
        }
    }
}
