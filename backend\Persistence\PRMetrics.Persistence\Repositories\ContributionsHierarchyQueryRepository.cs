﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using System.Net;
using Newtonsoft.Json;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Domain.Entities.Contributions.HierarchyQuery;
using RIB.PRMetrics.Persistence.Common;

namespace RIB.PRMetrics.Persistence.Repositories
{
    /// <summary>
    /// Repository class for interacting with the Contributions Hierarchy Query API.
    /// </summary>
    public class ContributionsHierarchyQueryRepository : IContributionsHierarchyQueryRepository
    {
        private readonly AzureDevopsUrlBuilder azureDevopsUrlBuilder;
        private readonly AZDevopsClientCommon aZDevopsClientCommon;

        /// <summary>
        /// Constructor that initializes dependencies required for making API calls.
        /// </summary>
        /// <param name="_azureDevopsUrlBuilder">The URL builder used for constructing API URIs.</param>
        /// <param name="httpClientFactory">Factory used for creating HTTP clients.</param>
        /// <param name="_aZDevopsClientCommon">The client used for making HTTP requests to Azure DevOps services.</param>
        public ContributionsHierarchyQueryRepository(AzureDevopsUrlBuilder _azureDevopsUrlBuilder, IHttpClientFactory httpClientFactory, AZDevopsClientCommon _aZDevopsClientCommon)
        {
            azureDevopsUrlBuilder = _azureDevopsUrlBuilder ?? throw new ArgumentNullException(nameof(_azureDevopsUrlBuilder));
            aZDevopsClientCommon = _aZDevopsClientCommon ?? throw new ArgumentNullException(nameof(_aZDevopsClientCommon));
        }

        /// <summary>
        /// Fetches the Git Pull Request Contributions Hierarchy from the Azure DevOps API.
        /// </summary>
        /// <param name="PullRequestId">The ID of the pull request to fetch the hierarchy for.</param>
        /// <param name="RepositoryId">The ID of the repository where the pull request resides.</param>
        /// <param name="ProjectId">The ID of the project containing the pull request.</param>
        /// <param name="PATToken">The Personal Access Token used for authentication with Azure DevOps.</param>
        /// <returns>Returns a <see cref="QueryResponse"/> object containing the pull request contribution hierarchy.</returns>
        /// <exception cref="Exception">Throws an exception if the request to the API fails.</exception>
        public async Task<QueryResponse> GetGitPullRequestContributionsHierarchyQueryAsync(string PullRequestId, string RepositoryId, string ProjectId, string PATToken)
        {
            // Build the API URL using the project ID
            var url = azureDevopsUrlBuilder.BuildGitPullRequestContributionHierarchyQueryUri(ProjectId);

            // Prepare the data payload to send with the request
            var postData = new QueryPayload()
            {
                ContributionIds = new[] { "ms.vss-code-web.pr-detail-data-provider" },
                DataProviderContext = new DataProviderContext()
                {
                    Properties = new DataProviderContextProperties()
                    {
                        PullRequestId = PullRequestId,
                        RepositoryId = RepositoryId,
                        Types = 192, // This seems like an arbitrary constant, possibly indicating a specific type for the query
                    }
                }
            };

            // Convert the payload to JSON format
            string jsonBody = JsonConvert.SerializeObject(postData);

            // Send the HTTP POST request to the Azure DevOps API
            var response = await aZDevopsClientCommon.PostAsync(url, PATToken, jsonBody);

            // If the response is successful, parse the JSON response into the expected data structure
            if (response.IsSuccessStatusCode && response.StatusCode == HttpStatusCode.OK)
            {
                var jsonResponse = await response.Content.ReadAsStringAsync();
                var template = new QueryResponse();
                var pullRequestsResponse = JsonConvert.DeserializeAnonymousType(jsonResponse, template);
                return pullRequestsResponse ?? new QueryResponse();
            }

            // Throw an exception if the API request was unsuccessful
            throw new Exception($"Error retrieving PR Build Overall Status: {response.ReasonPhrase}");
        }
    }
}
