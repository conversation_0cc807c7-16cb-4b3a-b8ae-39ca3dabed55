import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, debounceTime, distinctUntilChanged } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PerformanceService {
  
  // Debounced search functionality
  private searchSubjects = new Map<string, BehaviorSubject<string>>();
  
  constructor() {}

  /**
   * Creates a debounced search observable for a given key
   * @param key Unique identifier for the search
   * @param debounceMs Debounce time in milliseconds
   * @returns Observable that emits debounced search terms
   */
  createDebouncedSearch(key: string, debounceMs: number = 300): {
    search$: Observable<string>;
    next: (value: string) => void;
  } {
    if (!this.searchSubjects.has(key)) {
      this.searchSubjects.set(key, new BehaviorSubject<string>(''));
    }
    
    const subject = this.searchSubjects.get(key)!;
    const search$ = subject.pipe(
      debounceTime(debounceMs),
      distinctUntilChanged()
    );
    
    return {
      search$,
      next: (value: string) => subject.next(value)
    };
  }

  /**
   * Memoization utility for expensive calculations
   */
  private memoCache = new Map<string, { value: any; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Memoize function results with TTL
   * @param key Cache key
   * @param fn Function to memoize
   * @param ttl Time to live in milliseconds
   */
  memoize<T>(key: string, fn: () => T, ttl: number = this.CACHE_TTL): T {
    const now = Date.now();
    const cached = this.memoCache.get(key);
    
    if (cached && (now - cached.timestamp) < ttl) {
      return cached.value;
    }
    
    const value = fn();
    this.memoCache.set(key, { value, timestamp: now });
    
    // Clean up expired entries
    this.cleanupCache();
    
    return value;
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, entry] of this.memoCache.entries()) {
      if (now - entry.timestamp > this.CACHE_TTL) {
        this.memoCache.delete(key);
      }
    }
  }

  /**
   * Clear all caches
   */
  clearCaches(): void {
    this.memoCache.clear();
    this.searchSubjects.clear();
  }

  /**
   * Throttle function execution
   */
  private throttleTimers = new Map<string, number>();

  throttle(key: string, fn: () => void, delay: number): void {
    if (this.throttleTimers.has(key)) {
      return;
    }

    this.throttleTimers.set(key, window.setTimeout(() => {
      fn();
      this.throttleTimers.delete(key);
    }, delay));
  }

  /**
   * Virtual scrolling item size calculation
   */
  calculateItemSize(element: HTMLElement): number {
    return this.memoize(`item-size-${element.tagName}`, () => {
      const computedStyle = window.getComputedStyle(element);
      const height = parseInt(computedStyle.height, 10);
      const marginTop = parseInt(computedStyle.marginTop, 10);
      const marginBottom = parseInt(computedStyle.marginBottom, 10);
      return height + marginTop + marginBottom;
    });
  }

  /**
   * Optimize array operations for large datasets
   */
  optimizeArrayOperations<T>(
    array: T[],
    operation: 'filter' | 'sort' | 'map',
    predicate: (item: T, index?: number) => any,
    batchSize: number = 1000
  ): T[] {
    if (array.length <= batchSize) {
      // For small arrays, use native methods
      switch (operation) {
        case 'filter':
          return array.filter(predicate as (item: T) => boolean);
        case 'sort':
          return [...array].sort(predicate as (a: T, b: T) => number);
        case 'map':
          return array.map(predicate);
        default:
          return array;
      }
    }

    // For large arrays, process in batches
    const result: T[] = [];
    for (let i = 0; i < array.length; i += batchSize) {
      const batch = array.slice(i, i + batchSize);
      
      switch (operation) {
        case 'filter':
          result.push(...batch.filter(predicate as (item: T) => boolean));
          break;
        case 'map':
          result.push(...batch.map(predicate));
          break;
        case 'sort':
          // For sorting, we need to handle differently
          result.push(...batch);
          break;
      }
      
      // Allow other tasks to run
      if (i % (batchSize * 5) === 0) {
        await new Promise(resolve => setTimeout(resolve, 0));
      }
    }

    if (operation === 'sort') {
      return result.sort(predicate as (a: T, b: T) => number);
    }

    return result;
  }

  /**
   * Track component performance
   */
  private performanceMetrics = new Map<string, number[]>();

  trackPerformance(key: string, startTime: number): void {
    const duration = performance.now() - startTime;
    
    if (!this.performanceMetrics.has(key)) {
      this.performanceMetrics.set(key, []);
    }
    
    const metrics = this.performanceMetrics.get(key)!;
    metrics.push(duration);
    
    // Keep only last 100 measurements
    if (metrics.length > 100) {
      metrics.shift();
    }
    
    // Log slow operations
    if (duration > 100) {
      console.warn(`Slow operation detected: ${key} took ${duration.toFixed(2)}ms`);
    }
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats(key: string): { avg: number; min: number; max: number } | null {
    const metrics = this.performanceMetrics.get(key);
    if (!metrics || metrics.length === 0) {
      return null;
    }

    const avg = metrics.reduce((sum, val) => sum + val, 0) / metrics.length;
    const min = Math.min(...metrics);
    const max = Math.max(...metrics);

    return { avg, min, max };
  }
}
