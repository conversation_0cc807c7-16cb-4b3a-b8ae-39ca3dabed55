import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, catchError, forkJoin, map, of } from 'rxjs';
import { ApiUrlService } from '../services/api-url.service';
import { environment } from '../environments/environment';

/**
 * PR Status Count interface for the API response
 */
export interface PrStatusCount {
  active: number;
  completed: number;
  abandoned: number;
  total: number;
}

/**
 * Fetches the count of PRs by status using the dedicated API endpoint.
 * This is the preferred method to get PR counts by status as it's more efficient.
 * 
 * @param http Angular HttpClient instance
 * @param project Project name
 * @param repository Repository name  
 * @param patToken Personal Access Token
 * @param apiUrlService Optional ApiUrlService instance to build URLs
 * @returns Observable<PrStatusCount> with counts for each status
 */
export function getPullRequestStatusCount(
  http: HttpClient,
  project: string,
  repository: string,
  patToken: string,
  apiUrlService?: ApiUrlService
): Observable<PrStatusCount> {
  // Create a function to get the URL for a specific status
  const getUrl = (status: string): string => {
    if (apiUrlService) {
      return apiUrlService.getPullRequestStatusCountUrl(status, project, repository, patToken);
    }
    // Use environment.apiUrl as fallback
    return `${environment.apiUrl}/GitPullRequests/GetGitPullRequestStatusCount?prStatus=${status}&project=${project}&repositories=${repository}&patToken=${patToken}`;
  };
  
  // Create observables for each status count
  const activeCount$ = http.get<any>(getUrl('active')).pipe(
    map(response => response?.data?.value || 0),
    catchError(() => of(0))
  );
  
  const completedCount$ = http.get<any>(getUrl('completed')).pipe(
    map(response => response?.data?.value || 0),
    catchError(() => of(0))
  );
  
  const abandonedCount$ = http.get<any>(getUrl('abandoned')).pipe(
    map(response => response?.data?.value || 0),
    catchError(() => of(0))
  );
  
  // Use forkJoin to wait for all observables to complete
  return forkJoin({
    active: activeCount$,
    completed: completedCount$,
    abandoned: abandonedCount$
  }).pipe(
    map(results => {
      const total = results.active + results.completed + results.abandoned;
      return { ...results, total };
    })
  );
}

/**
 * Legacy function for backward compatibility.
 * Fetches the true PR count using $skip and $top to page through all PRs (works for some orgs).
 * @deprecated Use getPullRequestStatusCount instead for more efficient counting
 */
export function getAllPullRequestsCountWithSkip(
  http: HttpClient,
  organization: string,
  project: string,
  repoIdOrName: string,
  patToken: string,
  apiVersion: string = '7.1-preview.1'
): Observable<number> {
  return getPullRequestStatusCount(http, project, repoIdOrName, patToken).pipe(
    map(counts => counts.active)
  );
}

// This function id not in use anymore.
/**
 * Fetches the count for a single PR status using the dedicated API endpoint.
 * This allows showing counts as they become available rather than waiting for all.
 * 
 * @param http Angular HttpClient instance
 * @param status PR status ('active', 'completed', or 'abandoned')
 * @param project Project name
 * @param repository Repository name  
 * @param patToken Personal Access Token
 * @param apiUrlService Optional ApiUrlService instance to build URLs
 * @returns Observable<number> with count for the specified status
 */
export function getSinglePullRequestStatusCount(
  http: HttpClient,
  status: string,
  project: string,
  repository: string,
  patToken: string,
  apiUrlService?: ApiUrlService
): Observable<number> {
  // Create URL for the specific status
  let url: string;
  if (apiUrlService) {
    url = apiUrlService.getPullRequestStatusCountUrl(status, project, repository, patToken);
  } else {
    // Use environment.apiUrl as fallback
    url = `${environment.apiUrl}/GitPullRequests/GetGitPullRequestStatusCount?prStatus=${status}&project=${project}&repositories=${repository}&patToken=${patToken}`;
  }
  
  // Return an observable with just the count for this status
  return http.get<any>(url).pipe(
    map(response => response?.data?.value || 0),
    catchError(() => of(0))
  );
}
