import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject, Subscription } from 'rxjs';
import { SignalRService, ProcessProgress } from './signalr.service';
import { ExportApiService } from './export-api.service';

export interface DownloadProgress {
  id: string;
  fileName: string;
  progress: number; // 0-100
  status: 'preparing' | 'downloading' | 'paused' | 'completed' | 'cancelled' | 'error';
  startTime: Date;
  estimatedTimeRemaining?: string;
  downloadSpeed?: string;
  error?: string;
  jobStatus?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DownloadProgressService {
  private downloads = new Map<string, DownloadProgress>();
  private downloadSubjects = new Map<string, BehaviorSubject<DownloadProgress>>();
  private signalRSubscriptions = new Map<string, Subscription>();

  constructor(
    private signalRService: SignalRService,
    private exportApiService: ExportApiService
  ) {
    // Ensure SignalR connection is established
    this.signalRService.ensureConnection().catch(error => {
      console.error('Failed to establish SignalR connection:', error);
    });
  }

  /**
   * Start tracking a download process with SignalR
   * @param uuid UUID from backend export job
   * @param fileName Name of the file being downloaded
   * @returns Observable for progress tracking
   */
  startDownloadTracking(uuid: string, fileName: string): Observable<DownloadProgress> {
    const startTime = new Date();

    const initialProgress: DownloadProgress = {
      id: uuid,
      fileName,
      progress: 0,
      status: 'preparing',
      startTime
    };

    this.downloads.set(uuid, initialProgress);
    const subject = new BehaviorSubject<DownloadProgress>(initialProgress);
    this.downloadSubjects.set(uuid, subject);

    // Subscribe to SignalR updates for this specific job
    const signalRSubscription = this.signalRService.getProgressUpdatesForJob(uuid)
      .subscribe(progress => {
        this.handleSignalRProgress(uuid, progress);
      });

    this.signalRSubscriptions.set(uuid, signalRSubscription);

    return subject.asObservable();
  }

  /**
   * Handle SignalR progress updates
   * @param uuid Download UUID
   * @param signalRProgress Progress from SignalR
   */
  private handleSignalRProgress(uuid: string, signalRProgress: ProcessProgress): void {
    const download = this.downloads.get(uuid);
    const subject = this.downloadSubjects.get(uuid);

    if (!download || !subject) {
      return;
    }

    // Map SignalR status to download status
    const status = this.mapJobStatusToDownloadStatus(signalRProgress.jobStatus);

    // Calculate estimated time remaining and download speed
    const elapsed = Date.now() - download.startTime.getTime();
    const estimatedTimeRemaining = this.calculateEstimatedTime(elapsed, signalRProgress.percentage);
    const downloadSpeed = this.calculateDownloadSpeed(elapsed, signalRProgress.percentage);

    const updatedProgress: DownloadProgress = {
      ...download,
      progress: signalRProgress.percentage,
      status,
      estimatedTimeRemaining,
      downloadSpeed,
      jobStatus: signalRProgress.jobStatus
    };

    this.downloads.set(uuid, updatedProgress);
    subject.next(updatedProgress);

    // Handle completion
    if (signalRProgress.jobStatus === 'Completed') {
      this.completeDownload(uuid);
    }
  }

  /**
   * Map backend job status to frontend download status
   */
  private mapJobStatusToDownloadStatus(jobStatus: string): DownloadProgress['status'] {
    switch (jobStatus) {
      case 'Pending':
        return 'preparing';
      case 'Running':
      case 'Resume':
        return 'downloading';
      case 'Paused':
        return 'paused';
      case 'Completed':
        return 'completed';
      case 'Cancelled':
      case 'Deleted':
        return 'cancelled';
      default:
        return 'error';
    }
  }

  /**
   * Pause a download via API
   * @param downloadId Download ID
   */
  async pauseDownload(downloadId: string): Promise<void> {
    try {
      await this.exportApiService.pauseExport(downloadId).toPromise();
      // Status will be updated via SignalR
    } catch (error) {
      console.error('Error pausing download:', error);
      this.setDownloadError(downloadId, 'Failed to pause download');
    }
  }

  /**
   * Resume a paused download via API
   * @param downloadId Download ID
   */
  async resumeDownload(downloadId: string): Promise<void> {
    try {
      await this.exportApiService.resumeExport(downloadId).toPromise();
      // Status will be updated via SignalR
    } catch (error) {
      console.error('Error resuming download:', error);
      this.setDownloadError(downloadId, 'Failed to resume download');
    }
  }

  /**
   * Cancel a download via API
   * @param downloadId Download ID
   */
  async cancelDownload(downloadId: string): Promise<void> {
    try {
      await this.exportApiService.cancelExport(downloadId).toPromise();
      // Status will be updated via SignalR

      // Clean up after a delay
      setTimeout(() => {
        this.cleanupDownload(downloadId);
      }, 2000);
    } catch (error) {
      console.error('Error cancelling download:', error);
      this.setDownloadError(downloadId, 'Failed to cancel download');
    }
  }

  /**
   * Complete a download
   * @param downloadId Download ID
   */
  private completeDownload(downloadId: string): void {
    this.updateDownloadStatus(downloadId, 'completed');
    
    // Clean up after a delay
    setTimeout(() => {
      this.cleanupDownload(downloadId);
    }, 5000);
  }

  /**
   * Set download error
   * @param downloadId Download ID
   * @param error Error message
   */
  setDownloadError(downloadId: string, error: string): void {
    const download = this.downloads.get(downloadId);
    const subject = this.downloadSubjects.get(downloadId);
    
    if (download && subject) {
      const updatedProgress: DownloadProgress = {
        ...download,
        status: 'error',
        error
      };
      
      this.downloads.set(downloadId, updatedProgress);
      subject.next(updatedProgress);
    }
  }

  /**
   * Get download progress observable
   * @param downloadId Download ID
   */
  getDownloadProgress(downloadId: string): Observable<DownloadProgress> | null {
    const subject = this.downloadSubjects.get(downloadId);
    return subject ? subject.asObservable() : null;
  }

  /**
   * Get all active downloads
   */
  getActiveDownloads(): DownloadProgress[] {
    return Array.from(this.downloads.values()).filter(
      download => download.status !== 'completed' && download.status !== 'cancelled'
    );
  }

  /**
   * Update download status
   */
  private updateDownloadStatus(downloadId: string, status: DownloadProgress['status']): void {
    const download = this.downloads.get(downloadId);
    const subject = this.downloadSubjects.get(downloadId);
    
    if (download && subject) {
      const updatedProgress: DownloadProgress = {
        ...download,
        status
      };
      
      this.downloads.set(downloadId, updatedProgress);
      subject.next(updatedProgress);
    }
  }

  /**
   * Clean up download resources
   */
  private cleanupDownload(downloadId: string): void {
    const subject = this.downloadSubjects.get(downloadId);
    const signalRSubscription = this.signalRSubscriptions.get(downloadId);

    if (subject) {
      subject.complete();
      this.downloadSubjects.delete(downloadId);
    }

    if (signalRSubscription) {
      signalRSubscription.unsubscribe();
      this.signalRSubscriptions.delete(downloadId);
    }

    this.downloads.delete(downloadId);
  }

  /**
   * Calculate estimated time remaining
   */
  private calculateEstimatedTime(elapsedMs: number, progress: number): string {
    if (progress <= 0 || elapsedMs <= 0) return '';

    const estimatedTotal = (elapsedMs / progress) * 100;
    const remaining = estimatedTotal - elapsedMs;

    return this.formatTime(remaining);
  }

  /**
   * Format time in milliseconds to human readable string
   */
  private formatTime(milliseconds: number): string {
    if (milliseconds <= 0) return '0s';
    
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Calculate simulated download speed
   */
  private calculateDownloadSpeed(elapsedMs: number, progress: number): string {
    if (elapsedMs <= 0 || progress <= 0) return '0 KB/s';
    
    // Simulate file size and calculate speed
    const simulatedFileSizeKB = 500; // Assume 500KB file
    const downloadedKB = (progress / 100) * simulatedFileSizeKB;
    const elapsedSeconds = elapsedMs / 1000;
    const speedKBps = downloadedKB / elapsedSeconds;
    
    if (speedKBps > 1024) {
      return `${(speedKBps / 1024).toFixed(1)} MB/s`;
    } else {
      return `${speedKBps.toFixed(1)} KB/s`;
    }
  }
}
