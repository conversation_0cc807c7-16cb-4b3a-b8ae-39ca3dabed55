import { Pipe, PipeTransform } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, forkJoin, of } from 'rxjs';
import { map, catchError, shareReplay } from 'rxjs/operators';
import { environment } from '../../environments/environment'; // Added import

const guidRegex = /@<([0-9a-fA-F\-]{36})>/g;

@Pipe({
  name: 'userGuidToName',
  pure: false // impure pipe to allow async updates
})
export class UserGuidToNamePipe implements PipeTransform {
  private cache: { [guid: string]: Observable<string> } = {};

  constructor(private http: HttpClient) {}

  // Implements the pipe transform to replace GUIDs with display names using the API, with caching
  transform(content: string, patToken: string): Observable<string> {
    if (!content) return of('');
    const guids = this.extractGuids(content);
    if (guids.length === 0) return of(content);

    // Use unique GUIDs for API calls
    const uniqueGuids = Array.from(new Set(guids.map(g => g.toLowerCase())));
    const observables = uniqueGuids.map(guid => {
      if (!this.cache[guid]) {
        const url = `${environment.apiUrl}/IdentityPicker/GetIdentities?userUuid=${guid}&patToken=${patToken}`; // Changed to use environment.apiUrl
        this.cache[guid] = this.http.get<any>(url).pipe(
          map(res => (res && res.data && res.data[0] && res.data[0].displayName) ? res.data[0].displayName : `@<${guid}>`),
          catchError(() => of(`@<${guid}>`)),
          shareReplay(1)
        );
      }
      return this.cache[guid];
    });

    // Wait for all display names, then replace each GUID with its name
    return forkJoin(observables).pipe(
      map((names: string[]) => {
        // Map GUID to display name
        const guidToName: Record<string, string> = {};
        uniqueGuids.forEach((guid, idx) => {
          guidToName[guid] = names[idx];
        });
        // Replace using a regex callback for correct mapping
        return content.replace(guidRegex, (match, guid) => {
          return guidToName[guid.toLowerCase()] || match;
        });
      })
    );
  }

  private extractGuids(inputString: string): string[] {
    if (!inputString) return [];
    const matches = [...inputString.matchAll(guidRegex)].map(m => m[1]);
    return matches || [];
  }
}
