﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using RIB.PRMetrics.Application.Dto.CommentThreads;

namespace RIB.PRMetrics.Application.Dto
{
    /// <summary>
    /// Represents the response DTO for Git pull request threads, 
    /// containing the thread mode information (e.g., total time in published/draft states).
    /// </summary>
    public class GitPullRequestThreadsResponseDto
    {
        /// <summary>
        /// Gets or sets the thread mode details for the pull request thread.
        /// This includes metrics such as the total time the thread spent in the published and draft states.
        /// </summary>
        public GitPullRequestThreadsModeDto draftMode { get; set; }

        /// <summary>
        /// Gets or sets the detailed comment thread information for a Git pull request.
        /// </summary>
        public GitPullRequestCommentThreadDto commentThread { get; set; }
    }
}
