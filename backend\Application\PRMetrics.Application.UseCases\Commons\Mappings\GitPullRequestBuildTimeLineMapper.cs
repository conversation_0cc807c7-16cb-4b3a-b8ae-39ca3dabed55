﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using RIB.PRMetrics.Application.Dto.PullRequestBuild;
using RIB.PRMetrics.Domain.Entities.PullRequestBuild;

namespace RIB.PRMetrics.Application.UseCases.Commons.Mappings
{
    /// <summary>
    /// AutoMapper Profile for mapping between domain entities and DTOs related to Git pull request build timeline.
    /// This class contains all the mappings that convert the domain model entities to the corresponding Data Transfer Objects (DTOs) and vice versa.
    /// </summary>
    public class GitPullRequestBuildTimeLineMapper : Profile
    {
        public GitPullRequestBuildTimeLineMapper()
        {
            // Mapping between Log (domain model) and LogDto (DTO)
            // ReverseMap() allows bidirectional mapping (Log -> LogDto and LogDto -> Log)
            CreateMap<Log, LogDto>().ReverseMap();

            // Mapping between Issues (domain model) and IssuesDto (DTO)
            // ReverseMap() allows bidirectional mapping (Issues -> IssuesDto and IssuesDto -> Issues)
            CreateMap<Issues, IssuesDto>().ReverseMap();

            // Mapping between TimeLineItem (domain model) and TimeLineItemDto (DTO)
            // ReverseMap() allows bidirectional mapping (TimeLineItem -> TimeLineItemDto and TimeLineItemDto -> TimeLineItem)
            CreateMap<TimeLineItem, TimeLineItemDto>().ReverseMap();

            // Mapping between TimeLineResponse (domain model) and TimeLineResponseDto (DTO)
            // ReverseMap() allows bidirectional mapping (TimeLineResponse -> TimeLineResponseDto and TimeLineResponseDto -> TimeLineResponse)
            CreateMap<TimeLineResponse, TimeLineResponseDto>().ReverseMap();
        }
    }
}
