﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;

namespace RIB.PRMetrics.Application.UseCases.ContributionsHierarchyQuery.Queries.GetContributionsHierarchy
{
    /// <summary>
    /// FluentValidation validator for <see cref="GetContributionsHierarchyQuery"/>.
    /// Ensures that all required fields are present and not empty when the query is submitted.
    /// </summary>
    public class GetContributionsHierarchyValidator : AbstractValidator<GetContributionsHierarchyQuery>
    {
        public GetContributionsHierarchyValidator()
        {
            // Validates that RepositoryId is provided and not empty
            RuleFor(x => x.RepositoryId)
                .NotEmpty().WithMessage("Repository Id must not be empty.")
                .NotNull().WithMessage("Repository Id is required.");

            // Validates that ProjectId is provided and not empty
            RuleFor(x => x.ProjectId)
                .NotEmpty().WithMessage("Project Id must not be empty.")
                .NotNull().WithMessage("Project Id is required.");

            // Validates that PullRequestId is provided and not empty
            RuleFor(x => x.PullRequestId)
                .NotEmpty().WithMessage("Pull Request Id must not be empty.")
                .NotNull().WithMessage("Pull Request Id is required.");

            // Validates that PATToken is provided and not empty
            RuleFor(x => x.PATToken)
                .NotEmpty().WithMessage("PAT Token must not be empty.")
                .NotNull().WithMessage("PAT Token is required.");
        }
    }
}
