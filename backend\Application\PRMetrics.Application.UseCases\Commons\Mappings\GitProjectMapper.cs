﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Domain.Entities;

namespace  RIB.PRMetrics.Application.UseCases.Commons.Mappings
{
    /// <summary>
    /// AutoMapper Profile for mapping between GitProject and GitProjectDto.
    /// </summary>
    public class GitProjectMapper : Profile
    {
        /// <summary>
        /// Constructor that defines the mapping configuration for GitProject to GitProjectDto.
        /// </summary>
        public GitProjectMapper()
        {
            // Defining the mapping configuration for GitProject to GitProjectDto
            CreateMap<GitProject, GitProjectDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))              // Mapping 'Id' property from source to destination
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))          // Mapping 'Name' property from source to destination
                .ForMember(dest => dest.State, opt => opt.MapFrom(src => src.State))        // Mapping 'State' property from source to destination
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))  // Mapping 'Description' property from source to destination
                .ForMember(dest => dest.Url, opt => opt.MapFrom(src => src.Url));          // Mapping 'Url' property from source to destination
        }
    }
}
