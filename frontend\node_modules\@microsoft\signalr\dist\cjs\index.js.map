{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE;;;AAIvE,mCAA+D;AAAtD,oGAAA,UAAU,OAAA;AAAE,mGAAA,SAAS,OAAA;AAAE,sGAAA,YAAY,OAAA;AAC5C,2CAAqE;AAA5D,wGAAA,UAAU,OAAA;AAAe,0GAAA,YAAY,OAAA;AAC9C,yDAAwD;AAA/C,sHAAA,iBAAiB,OAAA;AAG1B,iDAAoE;AAA3D,8GAAA,aAAa,OAAA;AAAE,mHAAA,kBAAkB,OAAA;AAC1C,+DAA8D;AAArD,4HAAA,oBAAoB,OAAA;AAC7B,+CAE6F;AAFvD,2GAAA,WAAW,OAAA;AAGjD,qCAA8C;AAA5B,mGAAA,QAAQ,OAAA;AAC1B,2CAA6E;AAApE,+GAAA,iBAAiB,OAAA;AAAE,4GAAA,cAAc,OAAA;AAE1C,qCAAuC;AAA9B,qGAAA,UAAU,OAAA;AACnB,qDAAoD;AAA3C,kHAAA,eAAe,OAAA;AACxB,qCAAoC;AAA3B,kGAAA,OAAO,OAAA;AAEhB,iCAAkC;AAAzB,gGAAA,OAAO,OAAA", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Everything that users need to access must be exported here. Including interfaces.\r\nexport { AbortSignal } from \"./AbortController\";\r\nexport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nexport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nexport { DefaultHttpClient } from \"./DefaultHttpClient\";\r\nexport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\nexport { IStatefulReconnectOptions } from \"./IStatefulReconnectOptions\";\r\nexport { HubConnection, HubConnectionState } from \"./HubConnection\";\r\nexport { HubConnectionBuilder } from \"./HubConnectionBuilder\";\r\nexport { AckMessage, SequenceMessage, MessageType, MessageHeaders, HubMessage, HubMessageBase,\r\n    HubInvocationMessage, InvocationMessage, StreamInvocationMessage, StreamItemMessage, CompletionMessage,\r\n    PingMessage, CloseMessage, CancelInvocationMessage, IHubProtocol } from \"./IHubProtocol\";\r\nexport { ILogger, LogLevel } from \"./ILogger\";\r\nexport { HttpTransportType, TransferFormat, ITransport } from \"./ITransport\";\r\nexport { IStreamSubscriber, IStreamResult, ISubscription } from \"./Stream\";\r\nexport { NullLogger } from \"./Loggers\";\r\nexport { JsonHubProtocol } from \"./JsonHubProtocol\";\r\nexport { Subject } from \"./Subject\";\r\nexport { IRetryPolicy, RetryContext } from \"./IRetryPolicy\";\r\nexport { VERSION } from \"./Utils\";\r\n"]}