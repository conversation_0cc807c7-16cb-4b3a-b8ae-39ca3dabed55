﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using RIB.PRMetrics.Domain.Entities.Contributions.HierarchyQuery;

namespace RIB.PRMetrics.Application.Interface.Persistence
{
    /// <summary>
    /// Represents the repository interface for querying pull request contributions in the context of a hierarchy.
    /// This interface defines the necessary methods for interacting with the persistence layer
    /// for retrieving contributions hierarchy data related to pull requests.
    /// </summary>
    public interface IContributionsHierarchyQueryRepository : IGenericRepository<Object>
    {
        /// <summary>
        /// Asynchronously retrieves the contributions hierarchy query data for a given Git pull request.
        /// This method is used to fetch detailed contributions data based on a pull request, repository, and project context.
        /// </summary>
        /// <param name="PullRequestId">The unique identifier for the pull request.</param>
        /// <param name="RepositoryId">The identifier for the repository to which the pull request belongs.</param>
        /// <param name="ProjectId">The identifier for the project associated with the pull request.</param>
        /// <param name="PATToken">The Personal Access Token (PAT) for authentication to access the data.</param>
        /// <returns>A Task that represents the asynchronous operation, containing the <see cref="QueryResponse"/> with the query results.</returns>
        Task<QueryResponse> GetGitPullRequestContributionsHierarchyQueryAsync(string PullRequestId, string RepositoryId, string ProjectId, string PATToken);
    }
}
