﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using FluentValidation;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequestBuildTimeLine.Queries.GetGitPullRequestBuildTimeLine
{
    /// <summary>
    /// Validator for <see cref="GetGitPullRequestBuildTimeLineQuery"/>.
    /// Ensures that all required fields for retrieving the build timeline are provided and valid.
    /// </summary>
    public class GetGitPullRequestBuildTimeLineValidator : AbstractValidator<GetGitPullRequestBuildTimeLineQuery>
    {
        public GetGitPullRequestBuildTimeLineValidator()
        {
            // Validates that BuildId is not null or empty
            RuleFor(x => x.BuildId)
               .NotEmpty().WithMessage("Build Id must not be empty.")
               .NotNull().WithMessage("Build Id is required.");

            // Validates that ProjectId is not null or empty
            RuleFor(x => x.ProjectId)
               .NotEmpty().WithMessage("Project Id must not be empty.")
               .NotNull().WithMessage("Project Id is required.");

            // Validates that PATToken is not null or empty
            RuleFor(x => x.PATToken)
               .NotEmpty().WithMessage("PAT Token must not be empty.")
               .NotNull().WithMessage("PAT Token is required.");
        }
    }
}
