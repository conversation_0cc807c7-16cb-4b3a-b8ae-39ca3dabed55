﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using Microsoft.AspNetCore.Mvc;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;
using RIB.PRMetrics.Domain.Entities;
using System.ComponentModel.DataAnnotations;

namespace RIB.PRMetrics.Application.UseCases.GitPullRequests.Queries.GetGitPullRequestStatusCount
{
    /// <summary>
    /// Query to retrieve the count of Git pull requests filtered by status, project, and repository.
    /// Inherits from <see cref="GitCommon"/> which contains common Git-related properties.
    /// </summary>
    public class GetGitPullRequestStatusCountQuery : GitCommon, IRequest<BaseReponseGeneric<GitPullRequestStatusCountDto>>
    {
        /// <summary>
        /// The status of the pull requests to filter (e.g., active, completed, abandoned).
        /// This field is required.
        /// </summary>
        [Required(ErrorMessage = "PR Status is required.")]
        [FromQuery(Name = "prStatus")]
        public string Status { get; set; }
    }
}
