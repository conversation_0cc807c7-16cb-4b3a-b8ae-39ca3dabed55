﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using AutoMapper;
using MediatR;
using RIB.PRMetrics.Application.Dto;
using RIB.PRMetrics.Application.Interface.Persistence;
using RIB.PRMetrics.Application.UseCases.Commons.Bases;


namespace RIB.PRMetrics.Application.UseCases.GitPullRequestThreads.Queries.GetGitPullRequestThreads
{
    /// <summary>
    /// Handler for processing the GetGitPullRequestThreadsQuery and returning a response with the pull request threads.
    /// Implements IRequestHandler to handle the query and map the result to the response DTO.
    /// </summary>
    public class GetGitPullRequestThreadsHandler : IRequestHandler<GetGitPullRequestThreadsQuery, BaseReponseGeneric<GitPullRequestThreadsResponseDto>>
    {
        private readonly IUnitOfWork _unitOfWork; // Interface for managing data repositories and transactions
        private readonly IMapper _mapper; // AutoMapper instance for mapping entities to DTOs

        /// <summary>
        /// Initializes a new instance of the handler with the required dependencies.
        /// </summary>
        /// <param name="unitOfWork">Unit of work that manages repository operations.</param>
        /// <param name="mapper">AutoMapper instance used to map domain entities to DTOs.</param>
        public GetGitPullRequestThreadsHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        /// <summary>
        /// Handles the incoming query to retrieve Git pull request threads by ID.
        /// This method fetches the data from the repository and maps it to the response DTO.
        /// </summary>
        /// <param name="request">The query containing pull request data, including the pull request ID.</param>
        /// <param name="cancellationToken">Cancellation token to cancel the request if needed.</param>
        /// <returns>A Task that represents the asynchronous operation, containing a base response with the pull request thread data.</returns>
        public async Task<BaseReponseGeneric<GitPullRequestThreadsResponseDto>> Handle(GetGitPullRequestThreadsQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseReponseGeneric<GitPullRequestThreadsResponseDto>();

            try
            {
                // Fetch pull request threads using the unit of work and repository
                var gitPullRequestThreads = await _unitOfWork.GitPullRequestThreadsRepository.GetGitPullRequestThreadsAsync(request, request.PullRequestId,request.PRAuthorId);

                // Check if threads were found and map the result to the response DTO
                if (gitPullRequestThreads is not null)
                {
                    response.Data = _mapper.Map<GitPullRequestThreadsResponseDto>(gitPullRequestThreads);
                    response.Succcess = true; // Set success flag
                    response.Message = "Git Pull Request Threads fetched successfully."; // Success message
                }
                else
                {
                    response.Succcess = false; // Set failure flag
                    response.Message = "Pull request Threads not found."; // Failure message
                }
            }
            catch (Exception ex)
            {
                // In case of an error, set success to false and capture the exception message
                response.Succcess = false;
                response.Message = $"Error occurred: {ex.Message}";
            }

            return response;
        }
    }
}




