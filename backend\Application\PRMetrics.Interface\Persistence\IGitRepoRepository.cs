﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */


using   RIB.PRMetrics.Domain.Entities;

namespace  RIB.PRMetrics.Application.Interface.Persistence
{
    /// <summary>
    /// Defines the contract for repository operations related to Git repositories.
    /// This interface extends the generic repository interface to provide repository-specific functionality.
    /// </summary>
    public interface IGitRepoRepository : IGenericRepository<GitRepository>
    {
        /// <summary>
        /// Retrieves a list of Git repositories within a specific project.
        /// </summary>
        /// <param name="project">The name or ID of the project to filter the repositories.</param>
        /// <param name="token">The Personal Access Token (PAT) used to authenticate with Azure DevOps.</param>
        /// <returns>A list of Git repositories within the specified project.</returns>
        Task<List<GitRepository>> GetGitRepositoriesAsync(string project, string token);

        /// <summary>
        /// Retrieves detailed information about a specific Git repository.
        /// </summary>
        /// <param name="repo">An object containing the necessary repository and authentication details.</param>
        /// <returns>A detailed Git repository object.</returns>
        Task<GitRepository> GetGitRepositoriesDetailsAsync(GitCommon repo);
    }
}
