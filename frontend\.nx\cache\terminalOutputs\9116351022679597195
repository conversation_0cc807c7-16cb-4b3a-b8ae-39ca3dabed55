[33m❯[39m Building...
[32m✔[39m Building...
[37mApplication bundle generation failed. [15.728 seconds][39m
[37m[39m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1m7 repetitive deprecation warnings omitted.[39m[22m
[1m[33mRun in verbose mode to see all warnings.[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m  null[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/header/header.component.scss:2:8:[39m[22m
[1m[33m[37m      2 │ @import [32m[37m'../../styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pat-login/pat-login.component.scss:2:8:[39m[22m
[1m[33m[37m      2 │ @import [32m[37m'../../styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mTS-998113: HeaderComponent is not used within the template of PatLoginComponent[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pat-login/pat-login.component.ts:37:4:[39m[22m
[1m[33m[37m      37 │     [32mHeaderComponent[37m,[39m[22m
[1m[33m         ╵     [32m~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-data/pr-data.component.scss:2:8:[39m[22m
[1m[33m[37m      2 │ @import [32m[37m'../../styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8107: The left side of this optional chain operation does not include 'null' or 'undefined' in its type, therefore the '?.' operator can be replaced with the '.' operator.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-de[39m[22m[1m[33mtails.component.html:66:70:[39m[22m
[1m[33m[37m      66 │ ...lass="author-name">{{ pullRequest.createdBy?.[32mdisplayName[37m }}</span>[39m[22m
[1m[33m         ╵                                                 [32m~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  Error occurs in the template of component PrDetailsComponent.[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.ts:52:15:[39m[22m
[1m[33m[37m      52 │   templateUrl: [32m'./pr-details.component.html'[37m,[39m[22m
[1m[33m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mNG8107: The left side of this optional chain operation does not include 'null' or 'undefined' in its type, therefore the '?.' operator can be replaced with the '.' operator.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.html:256:78:[39m[22m
[1m[33m[37m      256 │ ...n class="comment-author">{{ comment.author?.[32mdisplayName[37m }}</span>[39m[22m
[1m[33m          ╵                                                [32m~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  Error occurs in the template of component PrDetailsComponent.[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.ts:52:15:[39m[22m
[1m[33m[37m      52 │   templateUrl: [32m'./pr-details.component.html'[37m,[39m[22m
[1m[33m         ╵                [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:2:8:[39m[22m
[1m[33m[37m      2 │ @import [32m[37m'../../styles.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:3:8:[39m[22m
[1m[33m[37m      3 │ @import [32m[37m'../../styles/components.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and[39m[22m[1m[33m automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:4:8:[39m[22m
[1m[33m[37m      4 │ @import [32m[37m'../../styles/pr-components.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:5:8:[39m[22m
[1m[33m[37m      5 │ @import [32m[37m'../../styles/pr-variables.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:252:68:[39m[22m
[1m[33m[37m      252 │ ...gradient(135deg, $primary-color, [32m[37mdarken($primary-color, 10%)); }[39m[22m
[1m[33m          ╵                                     [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Global built-in functions are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  Use color.adjust instead.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:252:68:[39m[22m
[1m[33m[37m      252 │ ...gradient(135deg, $primary-color, [32m[37mdarken($primary-color, 10%)); }[39m[22m
[1m[33m          ╵                                     [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  darken() is deprecated. Suggestions:[39m[22m
[1m[33m  [39m[22m
[1m[33m  color.scale($color, $lightness: -28.4916201117%)[39m[22m
[1m[33m  color.adjust($color, $lightness: -10%)[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info: [4mhttps://sass-lang.com/d/color-functions[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1[39m[22m[1m[33mm[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:253:68:[39m[22m
[1m[33m[37m      253 │ ...r-gradient(135deg, $active-color, [32m[37mdarken($active-color, 10%)); }[39m[22m
[1m[33m          ╵                                      [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Global built-in functions are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  Use color.adjust instead.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:253:68:[39m[22m
[1m[33m[37m      253 │ ...r-gradient(135deg, $active-color, [32m[37mdarken($active-color, 10%)); }[39m[22m
[1m[33m          ╵                                      [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  darken() is deprecated. Suggestions:[39m[22m
[1m[33m  [39m[22m
[1m[33m  color.scale($color, $lightness: -20%)[39m[22m
[1m[33m  color.adjust($color, $lightness: -10%)[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info: [4mhttps://sass-lang.com/d/color-functions[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:254:69:[39m[22m
[1m[33m[37m      254 │ ...ear-gradient(135deg, $fixed-color, [32m[37mdarken($fixed-color, 10%)); }[39m[22m
[1m[33m          ╵                                       [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Global built-in functions are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  Use color.adjust instead.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:254:69:[39m[22m
[1m[33m[37m      254 │ ...ear-gradient(135deg, $fixed-color, [32m[37mdarken($fixed-color, 10%)); }[39m[22m
[1m[33m          ╵                                       [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  darken() is deprecated. Suggestions:[39m[22m
[1m[33m  [39m[22m
[1m[33m  color.scale($color, $lightness: -20.3187250996%)[39m[22m
[1m[33m  color.adjust($color, $lightness: -10%)[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info: [4mhttps://sass-lang.com/d/color-functions[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[3[39m[22m[1m[33m3m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:415:24:[39m[22m
[1m[33m[37m      415 │       background-color: [32m[37mmap-get($thread-status-colors, active);[39m[22m
[1m[33m          ╵                         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Global built-in functions are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  Use map.get instead.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:416:13:[39m[22m
[1m[33m[37m      416 │       color: [32m[37mmap-get($thread-status-text-colors, active);[39m[22m
[1m[33m          ╵              [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Global built-in functions are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  Use map.get instead.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:632:4:[39m[22m
[1m[33m[37m      632 │     [32m[37mgrid-template-columns: repeat(auto-fill, minmax(350px, 1fr));[39m[22m
[1m[33m          ╵     [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass's behavior for declarations that appear after nested[39m[22m
[1m[33m  rules will be changing to match the behavior specified by CSS in an upcoming[39m[22m
[1m[33m  version. To keep the existing behavior, move the declaration above the nested[39m[22m
[1m[33m  rule. To opt into the new behavior, wrap the declaration in `& {}`.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info: [4mhttps://sass-lang.com/d/mixed-decls[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.scss:824:4:[39m[22m
[1m[33m[37m      824 │     [32m[37mgrid-template-columns: repeat(auto-fill, minmax(200px, 1fr));[39m[22m
[1m[33m          ╵     [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass's behavior for declarations that appear after nested[39m[22m
[1m[33m  rules will be changing to match the behavior spec[39m[22m[1m[33mified by CSS in an upcoming[39m[22m
[1m[33m  version. To keep the existing behavior, move the declaration above the nested[39m[22m
[1m[33m  rule. To opt into the new behavior, wrap the declaration in `& {}`.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info: [4mhttps://sass-lang.com/d/mixed-decls[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDuplicate member "goBack" in class body[0m [duplicate-class-member][39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.ts:1333:4:[39m[22m
[1m[33m[37m      1333 │     [32mgoBack[37m() {[39m[22m
[1m[33m           ╵     [32m~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m  The original member "goBack" is here:[39m[22m
[1m[33m[39m[22m
[1m[33m    src/components/pr-details/pr-details.component.ts:1238:4:[39m[22m
[1m[33m[37m      1238 │     [32mgoBack[37m() {[39m[22m
[1m[33m           ╵     [32m~~~~~~[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m[33m▲ [43;33m[[43;30mWARNING[43;33m][0m [1mDeprecation[0m [1m[35m[plugin angular-sass][0m[39m[22m
[1m[33m[39m[22m
[1m[33m    src/styles/pr-components.scss:4:8:[39m[22m
[1m[33m[37m      4 │ @import [32m[37m'./components.scss';[39m[22m
[1m[33m        ╵         [32m^[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[33m  Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.[39m[22m
[1m[33m  [39m[22m
[1m[33m  More info and automated migrator: [4mhttps://sass-lang.com/d/import[0m[39m[22m
[1m[33m[39m[22m
[1m[33m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2393: Duplicate function implementation.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/components/pr-details/pr-details.component.ts:262:2:[39m[22m
[1m[31m[37m      262 │   [32mgoBack[37m(): void {[39m[22m
[1m[31m          ╵   [32m~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
[1m[31m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mTS2393: Duplicate function implementation.[0m [1m[35m[plugin angular-compiler][0m[39m[22m
[1m[31m[39m[22m
[1m[31m    src/components/pr-details/pr-details.component.ts:384:2:[39m[22m
[1m[31m[37m      384 │   [32mgoBack[37m(): void {[39m[22m
[1m[31m          ╵   [32m~~~~~~[0m[39m[22m
[1m[31m[39m[22m
[1m[31m[39m[22m
