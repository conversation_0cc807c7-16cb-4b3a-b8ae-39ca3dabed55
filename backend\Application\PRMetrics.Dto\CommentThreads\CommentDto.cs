﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto.CommentThreads
{
    /// <summary>
    /// Data Transfer Object (DTO) for representing a comment in a thread.
    /// </summary>
    public class CommentDto
    {
        /// <summary>
        /// Gets or sets the unique identifier for the comment.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the parent comment, if any.
        /// </summary>
        public int ParentCommentId { get; set; }

        /// <summary>
        /// Gets or sets the content of the comment.
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the comment was published.
        /// </summary>
        public DateTime PublishedDate { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the comment was last updated.
        /// </summary>
        public DateTime LastUpdatedDate { get; set; }

        /// <summary>
        /// Gets or sets the author of the comment.
        /// </summary>
        public UserDto Author { get; set; }

        /// <summary>
        /// Gets or sets the type of the thread this comment belongs to (e.g., reviewer or review response).
        /// </summary>
        public string ThreadType { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the comment was response updated.
        /// </summary>
        public DateTime ResponseUpdatedDate { get; set; }
    }
}

