import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideRouter, withRouterConfig, withPreloading, PreloadAllModules } from '@angular/router';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { provideAnimations } from '@angular/platform-browser/animations';
import { appRoutes } from './app.routes';
import { provideCharts, withDefaultRegisterables } from 'ng2-charts';
import { ConfigService } from '../services/config.service';
import { PerformanceService } from '../services/performance.service';
import { UserService } from '../services/user.service';
import { HttpCacheInterceptor } from '../services/http-cache.interceptor';

export const appConfig: ApplicationConfig = {
  providers: [
    // Optimized zone change detection
    provideZoneChangeDetection({
      eventCoalescing: true,
      runCoalescing: true
    }),

    // Router with preloading strategy
    provideRouter(appRoutes,
      withRouterConfig({
        paramsInheritanceStrategy: 'always'
      }),
      withPreloading(PreloadAllModules)
    ),

    // HTTP client with caching interceptor
    provideHttpClient(
      withInterceptors([
        (req, next) => new HttpCacheInterceptor().intercept(req, { handle: next })
      ])
    ),

    provideAnimations(),
    provideCharts(withDefaultRegisterables()),

    // Services
    ConfigService,
    PerformanceService,
    UserService
  ],
};
