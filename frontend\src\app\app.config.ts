import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideRouter, withRouterConfig } from '@angular/router';
import { provideHttpClient } from '@angular/common/http';
import { provideAnimations } from '@angular/platform-browser/animations';
import { appRoutes } from './app.routes';
import { provideCharts, withDefaultRegisterables } from 'ng2-charts';
import { ConfigService } from '../services/config.service';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(appRoutes, withRouterConfig({
      paramsInheritanceStrategy: 'always'
    })),
    provideHttpClient(),
    provideAnimations(),
    provideCharts(withDefaultRegisterables()),
    ConfigService // Explicitly provide ConfigService
  ],
};
