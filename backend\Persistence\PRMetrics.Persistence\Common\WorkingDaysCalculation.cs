﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Persistence.Commont
{
    /// <summary>
    /// Provides utility methods for calculating working hours, formatting working time, and converting UTC time to IST.
    /// </summary>
    public class WorkingDaysCalculation
    {
        /// <summary>
        /// Calculates the total working hours between two DateTime values, excluding weekends.
        /// The working day is defined as 9:30 AM to 6:30 PM.
        /// </summary>
        /// <param name="start">The start time of the period.</param>
        /// <param name="end">The end time of the period.</param>
        /// <returns>A TimeSpan representing the total working hours between start and end.</returns>
        public static TimeSpan CalculateWorkingHours(DateTime start, DateTime end)
        {
            if (end <= start)
                return TimeSpan.Zero; // Return zero if end time is earlier than start time

            TimeSpan total = TimeSpan.Zero;

            // Define daily working hours (9:30 AM to 6:30 PM)
            TimeSpan workStart = new TimeSpan(9, 30, 0); // 9:30 AM
            TimeSpan workEnd = new TimeSpan(18, 30, 0);  // 6:30 PM

            // Normalize to the date (without time) to start from the beginning of the day
            DateTime current = start.Date;

            // Loop through each day between start and end
            while (current <= end.Date)
            {
                // Skip weekends (Saturday and Sunday)
                if (current.DayOfWeek != DayOfWeek.Saturday && current.DayOfWeek != DayOfWeek.Sunday)
                {
                    // Define the start and end times for the workday
                    DateTime workDayStart = current.Add(workStart);
                    DateTime workDayEnd = current.Add(workEnd);

                    // Determine the actual start and end times for the working period on this day
                    DateTime actualStart = (start > workDayStart) ? start : workDayStart;
                    DateTime actualEnd = (end < workDayEnd) ? end : workDayEnd;

                    // If the actual start time is before the actual end time, calculate the time difference
                    if (actualEnd > actualStart)
                    {
                        total += actualEnd - actualStart;
                    }
                    else
                    {
                        
                        total += end - start;
                    }
                }

                // Move to the next day
                current = current.AddDays(1);
            }

            return total;
        }

        /// <summary>
        /// Formats the working time (in TimeSpan) into a human-readable string (e.g., "2 days 4 hours 30 minutes").
        /// </summary>
        /// <param name="time">The time to format.</param>
        /// <returns>A string representing the working time in a readable format.</returns>
        public static string FormatWorkingTime(TimeSpan time)
        {
            int totalMinutes = (int)time.TotalMinutes;
            int days = totalMinutes / (60 * 8); // Calculate number of full working days (assuming 8-hour working day)
            totalMinutes %= (60 * 8);           // Calculate remaining minutes after full working days
            int hours = totalMinutes / 60;     // Calculate remaining hours
            int minutes = totalMinutes % 60;  // Calculate remaining minutes

            var parts = new List<string>();

            // If there are full days, add them to the parts list
            if (days > 0)
                parts.Add($"{days} day{(days > 1 ? "s" : "")}");

            // If there are remaining hours, add them to the parts list
            if (hours > 0)
                parts.Add($"{hours} hour{(hours > 1 ? "s" : "")}");

            // If there are remaining minutes or no days/hours, add minutes to the parts list
            if (minutes > 0 || parts.Count == 0)
                parts.Add($"{minutes} minute{(minutes > 1 ? "s" : "")}");

            // Join the parts together to create the final formatted string
            return string.Join(" ", parts);
        }

        /// <summary>
        /// Converts a given UTC DateTime to India Standard Time (IST).
        /// </summary>
        /// <param name="utcTime">The UTC time to convert.</param>
        /// <returns>A DateTime representing the given UTC time converted to IST.</returns>
        public static DateTime ConvertIST(DateTime utcTime)
        {
            // Retrieve IST time zone information
            TimeZoneInfo istZone = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");
            // Convert the given UTC time to IST
            DateTime istTime = TimeZoneInfo.ConvertTimeFromUtc(utcTime, istZone);

            return istTime;
        }
    }
}
