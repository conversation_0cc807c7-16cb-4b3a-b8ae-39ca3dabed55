﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace  RIB.PRMetrics.Application.UseCases.Commons.Bases
{
    /// <summary>
    /// Represents an error object with relevant information about the error's property and message.
    /// </summary>
    public class BaseError
    {
        /// <summary>
        /// Gets or sets the property name or field associated with the error.
        /// </summary>
        public string? PropertyMessage { get; set; }

        /// <summary>
        /// Gets or sets the detailed error message describing the issue.
        /// </summary>
        public string? ErrorMessage { get; set; }
    }
}
