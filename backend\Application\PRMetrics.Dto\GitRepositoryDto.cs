﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
*/

namespace  RIB.PRMetrics.Application.Dto
{
    /// <summary>
    /// Represents a Git repository in Azure DevOps.
    /// </summary>
    public class GitRepositoryDto
    {
        /// <summary>
        /// The unique identifier of the Git repository.
        /// </summary>
        
        public string Id { get; set; }

        /// <summary>
        /// The name of the Git repository.
        /// </summary>
        
        public string Name { get; set; }

        /// <summary>
        /// The project to which the Git repository belongs.
        /// </summary>
        
        public GitProjectDto Project { get; set; }

        /// <summary>
        /// The URL reference to the Git repository.
        /// </summary>
        
        public string Url { get; set; }

        /// <summary>
        /// The default branch of the Git repository (e.g., master or main).
        /// </summary>
        
        public string DefaultBranch { get; set; }

        /// <summary>
        /// The SSH URL used to clone the repository.
        /// </summary>
        
        public string SshUrl { get; set; }

        /// <summary>
        /// The web URL that links to the repository’s page in the Azure DevOps portal.
        /// </summary>
        
        public string WebUrl { get; set; }

        /// <summary>
        /// Indicates whether the repository is disabled (inactive).
        /// </summary>
        
        public bool IsDisabled { get; set; }
    }
}
