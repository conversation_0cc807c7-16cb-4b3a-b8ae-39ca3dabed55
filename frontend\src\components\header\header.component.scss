// Import global styles
@import '../../styles.scss';

// Mixins
@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Header styling */
.app-header {
  z-index: 1000;
  background: $primary-color;
  color: white;
  padding: 16px 24px;
  @include flex-between;
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-logo {
  height: 32px;
  width: auto;
}

.header-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

// User info display
.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-right: 8px;

  .user-name {
    font-size: 0.9rem;
    font-weight: 500;
    color: white;
    line-height: 1.2;
  }

  .user-email {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.2;
  }
}

// Menu user info styling
.menu-user-info {
  padding: 12px 16px !important;
  cursor: default !important;

  .menu-user-details {
    .menu-user-name {
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .menu-user-email {
      font-size: 0.85rem;
      color: #666;
    }
  }
}

.user-profile-button {
  color: white;
  transform: scale(1.2); /* Make the icon slightly larger */
  transition: transform 0.2s ease;

  mat-icon {
    font-size: 28px;
    height: 28px;
    width: 28px;
  }

  &:hover {
    transform: scale(1.3); /* Grow on hover */
  }
}

/* Sidenav styling */
.sidenav-container {
  flex-grow: 1;
  height: calc(100% - 64px); /* subtract header height */
  position: relative;
}

.sidenav {
  z-index: 100000;
  width: 250px;
  background-color: white;
  box-shadow: 3px 0 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, width 0.3s ease, opacity 0.3s ease;
}

mat-sidenav-content {
  transition: margin-left 0.3s ease;
  width: 100%;
}

.sidenav-header {
  padding: 16px;
  background-color: #f5f5f5;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h2 {
    margin: 0;
    font-weight: 500;
    color: #333;
  }
  
  .close-sidenav-button {
    width: 30px;
    height: 30px;
    line-height: 30px;
    
    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      line-height: 20px;
    }
  }
}

.content-container {
  position: relative;
  height: 100%;
}

.menu-button {
  margin-right: 8px;
  color: white;
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.1);
  }
}

mat-nav-list {
  padding-top: 8px;
  
  a {
    display: flex;
    align-items: center;
    margin: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    
    mat-icon {
      margin-right: 16px;
      color: #757575;
    }
    
    &.active {
      background-color: rgba(63, 81, 181, 0.1);
      color: #3f51b5;
      font-weight: 500;
      
      mat-icon {
        color: #3f51b5;
      }
    }
    
    &:hover:not(.active) {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-header {
    flex-direction: row; /* Keep as row for mobile */
    justify-content: space-between;
    padding: 12px 16px;
  }

  .header-title {
    font-size: 1.2rem;
  }

  .user-info {
    display: none; // Hide user text on mobile to save space
  }

  .user-profile-button {
    /* Adjust size for mobile */
    transform: scale(1.1);

    &:hover {
      transform: scale(1.2);
    }
  }

  .sidenav {
    width: 200px;
  }
}