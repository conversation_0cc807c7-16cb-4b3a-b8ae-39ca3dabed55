﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using MediatR;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Text.Json;

namespace  RIB.PRMetrics.Application.UseCases.Commons.Behaviours
{
    /// <summary>
    /// A performance monitoring behavior for MediatR requests that logs the time taken by a request to process.
    /// It implements the <see cref="IPipelineBehavior{TRequest,TResponse}"/> interface.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request.</typeparam>
    /// <typeparam name="TResponse">The type of the response.</typeparam>
    public class PerformanceBehaviour<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse> where TRequest : IRequest<TResponse>
    {
        private readonly Stopwatch _timer; // A stopwatch used to measure the elapsed time of request processing.
        private readonly ILogger<TRequest> _logger; // Logger to log performance warnings.

        /// <summary>
        /// Initializes a new instance of the <see cref="PerformanceBehaviour{TRequest,TResponse}"/> class.
        /// </summary>
        /// <param name="logger">An instance of <see cref="ILogger{TRequest}"/> used to log performance warnings.</param>
        public PerformanceBehaviour(ILogger<TRequest> logger)
        {
            _timer = new Stopwatch() ?? throw new ArgumentNullException(nameof(Stopwatch));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Measures the time taken by a request to process and logs a warning if it exceeds a threshold.
        /// </summary>
        /// <param name="request">The request being processed.</param>
        /// <param name="next">A delegate to continue the request pipeline.</param>
        /// <param name="cancellationToken">A token used to propagate notification of request cancellation.</param>
        /// <returns>The response generated by the request handler.</returns>
        public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
        {
            _timer.Start(); // Start the stopwatch before processing the request.

            var response = await next(); // Proceed to the next handler in the pipeline.

            _timer.Stop(); // Stop the stopwatch after the request has been handled.

            var elapsedMilliseconds = _timer.ElapsedMilliseconds; // Get the time taken in milliseconds.

            // Log a warning if the request processing time exceeds 10 milliseconds.
            if (elapsedMilliseconds > 10)
            {
                var requestName = typeof(TRequest).Name; // Get the name of the request type.
                _logger.LogWarning("Long Running: {name} ({elapsedMilliseconds} milliseconds) {@request}",
                    requestName,
                    elapsedMilliseconds,
                    JsonSerializer.Serialize(request)); // Log the elapsed time and the serialized request.
            }

            return response; // Return the response after processing.
        }
    }
}

