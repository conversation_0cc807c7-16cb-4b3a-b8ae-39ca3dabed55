<!-- Card header content with PR counts -->
<!-- [class.selected]="currentStatusFilter === 'active'"
 [class.selected]="currentStatusFilter === 'completed'"
 [class.selected]="currentStatusFilter === 'abandoned'" -->
<!-- (click)="onActiveClick()"> -->
<!-- (click)="onCompletedClick()" -->
<!-- (click)="onAbandonedClick()" -->
<div class="card-header-content">
  <div>
    <mat-card-title>{{ title }}</mat-card-title>
    <mat-card-subtitle>{{ subtitle }}</mat-card-subtitle>
  </div>
  <!-- PR Counts Block -->
  <div class="pr-counts-block">
    <div class="pr-count-item active">
      <mat-icon>play_circle_filled</mat-icon>
      <span>
        <strong>Active PRs:</strong>
        <ng-container *ngIf="loadingActiveCount; else activeCountDisplay">
          <span class="loading-text">Loading...</span>
        </ng-container>
        <ng-template #activeCountDisplay>
          {{ totalCount | number }}</ng-template
        >
      </span>
    </div>
    <div class="pr-count-item completed">
      <mat-icon>check_circle</mat-icon>
      <span>
        <strong>Completed PRs:</strong>
        <ng-container *ngIf="loadingCompletedCount; else completedCountDisplay">
          <span class="loading-text">Loading...</span>
        </ng-container>
        <ng-template #completedCountDisplay>
          {{ completedCount | number }}</ng-template
        >
      </span>
    </div>
    <div class="pr-count-item abandoned">
      <mat-icon>cancel</mat-icon>
      <span>
        <strong>Abandoned PRs:</strong>
        <ng-container *ngIf="loadingAbandonedCount; else abandonedCountDisplay">
          <span class="loading-text">Loading...</span>
        </ng-container>
        <ng-template #abandonedCountDisplay>
          {{ abandonedCount | number }}</ng-template
        >
      </span>
    </div>
  </div>
</div>
