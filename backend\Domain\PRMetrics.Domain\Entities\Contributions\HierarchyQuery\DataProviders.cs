﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using Newtonsoft.Json;

namespace RIB.PRMetrics.Domain.Entities.Contributions.HierarchyQuery
{
    /// <summary>
    /// Represents a collection of data providers for pull request-related data.
    /// </summary>
    public class DataProviders
    {
        /// <summary>
        /// Gets or sets the pull request detail data provider.
        /// This is a specific data provider related to pull request details.
        /// </summary>
        [JsonProperty("ms.vss-code-web.pr-detail-data-provider")] // Maps the property to the "ms.vss-code-web.pr-detail-data-provider" JSON key
        public PullRequestDataProvider prDetailDataProvider { get; set; }
    }
}
