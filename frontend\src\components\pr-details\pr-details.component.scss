// Import global and component styles
@import '../../styles.scss';
@import '../../styles/components.scss';
@import '../../styles/pr-components.scss';
@import '../../styles/pr-variables.scss';

// Core container styles
.pr-details-container {
  @include container-base;
  max-width: 1200px;
}

// Navigation and loading states
.back-button {
  margin-bottom: 16px;
  color: $primary-color;
  
  mat-icon {
    margin-right: 4px;
    vertical-align: middle;
  }
}

.loading-container, .error-container {
  @include no-content;
  
  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
  }
  
  p { font-size: 18px; }
}

.error-container mat-icon { color: $closed-color; }

// PR content layout
.pr-content {
  display: flex;
  flex-direction: column;
  gap: $card-spacing;
}

.pr-header-card, .pr-details-card {
  @include card-base;
}

// PR title and ID styles
.pr-title-container {
  width: 100%;
}

.pr-id-status {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.pr-id {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-light);
}

.pr-title {
  font-size: 24px;
  font-weight: 500;
  margin: 0;
  color: var(--text-dark);
}

// Status badges
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: $badge-border-radius;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  
  .status-dot {
    height: 8px;
    width: 8px;
    border-radius: 50%;
    margin-right: 6px;
  }
  
  &.status-active { @include status-badge(#0078d4); }
  &.status-completed { @include status-badge(#107c10); }
  &.status-abandoned { @include status-badge(#d83b01); }
  &.status-draft { @include status-badge(#5c2d91); }
}

.draft-badge {
  background-color: rgba(92, 45, 145, 0.1);
  color: #5c2d91;
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: $badge-border-radius;
  text-transform: uppercase;
}

// PR metadata section
.pr-metadata {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin: 20px 0;
}

// Author section
.pr-author-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
  width: 100%;
  
  @include responsive-mobile {
    flex-direction: column;
    align-items: flex-start;
  }
}

.pr-author {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .avatar-container {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    border: 2px solid white;
  }
  
  .author-avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .author-info {
    display: flex;
    flex-direction: column;
    
    .author-name {
      font-weight: 600;
      font-size: 16px;
      color: #333;
    }
    
    .creation-date {
      font-size: 12px;
      color: #666;
    }
  }
}

// Comment stats
.comment-stats {
  display: flex;
  gap: 16px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  
  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    transition: transform 0.2s ease;
    position: relative;
    padding: 8px 12px;
    border-radius: 6px;
    
    &:hover {
      transform: translateY(-3px);
      background-color: #f0f0f0;
      
      .stat-badge {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }
      
      .stat-label {
        color: #333;
      }
    }
    
    .stat-badge {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 42px;
      height: 42px;
      border-radius: 50%;
      font-weight: 600;
      font-size: 16px;
      color: white;
      transition: all 0.2s ease;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      
      &.total { background: linear-gradient(135deg, $primary-color, darken($primary-color, 10%)); }
      &.active { background: linear-gradient(135deg, $active-color, darken($active-color, 10%)); }
      &.resolved { background: linear-gradient(135deg, $fixed-color, darken($fixed-color, 10%)); }
      
      .count {
        position: relative;
        z-index: 2;
      }
    }
    
    .stat-label {
      font-size: 12px;
      color: #666;
      font-weight: 500;
      transition: color 0.2s ease;
    }
  }
}

// Branch information
.pr-branches {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  
  .branch {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
    
    .branch-label {
      font-size: 12px;
      color: var(--text-light);
    }
    
    .branch-info {
      display: flex;
      align-items: center;
      gap: 8px;
      font-family: monospace;
      font-size: 14px;
      
      .branch-icon {
        color: $primary-color;
        font-size: 18px;
        height: 18px;
        width: 18px;
      }
    }
  }
  
  .arrow-icon {
    color: var(--text-light);
  }
  
  @include responsive-mobile {
    flex-direction: column;
    align-items: flex-start;
    
    .arrow-icon {
      transform: rotate(90deg);
      margin-left: 24px;
    }
  }
}

// Merge status
.pr-merge-status {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .merge-label {
    color: var(--text-light);
    font-size: 14px;
  }
  
  .merge-value {
    font-weight: 500;
  }
}

// General elements
mat-divider {
  margin: 16px 0;
}

// Description section
.pr-description {
  h3 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
  }
  
  .description-text {
    white-space: pre-line;
    line-height: 1.5;
  }
}

.pr-no-description {
  padding: 16px;
  text-align: center;
  color: var(--text-light);
  font-style: italic;
}

// Tab content
.tab-content {
  padding: 24px;
}

// Empty state
.no-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  color: var(--text-light);
  text-align: center;
  
  mat-icon {
    font-size: 36px;
    height: 36px;
    width: 36px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
}

// Comment threads
.comment-threads {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.comment-thread {
  @include comment-container;
}

.thread-header {
  padding: 12px 16px;
  background-color: rgba(0, 0, 0, 0.02);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 12px;
}  
  .thread-status {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    
    &.status-active {
      background-color: map-get($thread-status-colors, active);
      color: map-get($thread-status-text-colors, active);
    }
    
    &.status-closed {
      background-color: map-get($thread-status-colors, closed);
      color: map-get($thread-status-text-colors, closed);
    }
  }

.thread-id {
  color: var(--text-light);
  font-size: 14px;
}

// Thread context
.thread-context {
  padding: 12px 16px;
  background-color: rgba(0, 0, 0, 0.01);
  border-bottom: 1px solid var(--border-color);
  
  .file-path {
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: monospace;
    margin-bottom: 4px;
    
    mat-icon {
      font-size: 16px;
      height: 16px;
      width: 16px;
      color: var(--text-light);
    }
  }
  
  .code-context {
    font-family: monospace;
    font-size: 12px;
    color: var(--text-light);
    margin-left: 24px;
  }
}

// Thread comments
.thread-comments {
  padding: 16px;
}

.comment {
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .comment-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
  }
  
  .comment-content {
    @include comment-content;
  }
}

// Work items and reviewers
.work-items, .reviewers {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.work-item {
  padding: 16px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
  
  .work-item-id {
    color: $primary-color;
    font-weight: 500;
  }
}

.reviewer {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  
  .reviewer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .reviewer-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    
    .reviewer-name {
      font-weight: 500;
    }
    
    .reviewer-vote {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 14px;
        &.approved { color: map-get($vote-colors, approved); }
      &.approved-with-suggestions { color: map-get($vote-colors, approved-with-suggestions); }
      &.no-vote { color: map-get($vote-colors, no-vote); }
      &.waiting { color: map-get($vote-colors, waiting); }
      &.rejected { color: map-get($vote-colors, rejected); }
      
      mat-icon {
        font-size: 16px;
        height: 16px;
        width: 16px;
      }
    }
  }
}

// Comments summary card
.comments-summary-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  
  @include card-header-content;
  
  .comment-stats {
    @include stats-container;
    justify-content: space-around;
    
    @media (max-width: 600px) {
      flex-direction: column;
      align-items: center;
    }
    
    .stat-item {
      @include stats-item;
      
      .stat-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #666;
      }
    }
  }
  
  .reviewer-stats {
    h3 {
      font-size: 16px;
      margin-top: 0;
      margin-bottom: 16px;
      color: #333;
    }
    
    .reviewer-list {
      @include card-grid;
      
      .reviewer-item {
        background-color: #f5f7fa;
        padding: 12px;
        border-radius: 8px;
        
        .reviewer-name {
          font-weight: 600;
          margin-bottom: 8px;
        }
        
        .reviewer-counts {
          display: flex;
          gap: 12px;
          font-size: 12px;
          
          .active-count { color: $active-color; }
          .resolved-count { color: #4caf50; }
        }
      }
    }
  }
}

// Thread sections and cards
.thread-section {
  margin-bottom: 32px;
  
  .section-title {
    @include section-title;
    font-size: 18px;
  }
  
  .thread-cards {
    @include card-grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
  
  .thread-card {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    }
    
    @include card-header-content;
    
    .thread-header-content {
      display: flex;
      align-items: center;
      gap: 12px;
      width: 100%;
    }
    
    .thread-status {
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
    }
    
    .thread-id {
      font-size: 14px;
      color: #666;
    }
  }
}

// Timeline for comments
.thread-timeline {
  @include thread-timeline;
  
  .timeline-content {
    .comment-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }
    
    .comment-content {
      @include comment-content;
    }
    
    .comment-author {
      font-weight: 500;
      margin-right: 2px;
    }
    
    .comment-date {
      color: var(--text-light);
      font-size: 12px;
    }
  }
}

// Thread metrics
.thread-metrics {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #eee;
  
  .metric {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #666;
    
    mat-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
    }
  }
}

// Metrics styles (draft and review cycle)
.draft-metrics, .review-cycle-metrics {
  @include metrics-container;

  .section-title {
    @include section-title;
  }

  .metrics-container {
    .metric-group {
      margin-bottom: 12px;
    }

    .metric {
      margin-bottom: 8px;

      .metric-label {
        font-weight: 500;
        margin-right: 8px;
        color: #5a5a5a;
      }

      .metric-value {
        color: #333;
        
        &.cycle-count {
          font-size: 20px;
          color: $primary-color;
        }
      }
    }

    .metric-chart {
      margin-top: 12px;
      
      .chart-bar {
        @include chart-bar;
      }
    }
    
    .cycle-info {
      margin-top: 8px;
      
      .cycle-indicator {
        display: flex;
        align-items: center;
        gap: 4px;
        
        .cycle-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background-color: rgba($primary-color, 0.1);
          
          mat-icon {
            color: $primary-color;
            font-size: 18px;
            height: 18px;
            width: 18px;
            line-height: 18px;
          }
        }
        
        &.cycles-0 .cycle-icon {
          background-color: rgba($wontfix-color, 0.1);
          mat-icon { color: $wontfix-color; }
        }
        
        &.cycles-1 .cycle-icon {
          background-color: rgba($fixed-color, 0.1);
          mat-icon { color: $fixed-color; }
        }
        
        &.cycles-2 .cycle-icon {
          background-color: rgba($active-color, 0.1);
          mat-icon { color: $active-color; }
        }
        
        &.cycles-3-plus .cycle-icon {
          background-color: rgba($closed-color, 0.1);
          mat-icon { color: $closed-color; }
        }
        
        span {
          font-size: 14px;
          color: var(--text-medium);
        }
      }
    }
  }
}

// Reviewer activity section
.reviewer-activity {
  margin-bottom: 32px;
  
  .section-title {
    @include section-title;
    font-size: 18px;
  }
  
  .reviewer-list {
    @include card-grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    
    .reviewer-item {
      background-color: #f5f7fa;
      padding: 12px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }
      
      .reviewer-name {
        font-weight: 600;
        margin-bottom: 8px;
      }
      
      .reviewer-counts {
        display: flex;
        gap: 12px;
        font-size: 12px;
        
        .active-count {
          color: $active-color;
          font-weight: 500;
        }
        
        .resolved-count {
          color: $fixed-color;
          font-weight: 500;
        }
      }
    }
  }
}

// Comment summary section
.comment-summary {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #eee;
  
  .summary-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
  }
  
  .summary-stats {
    @include stats-container;
    
    .summary-item {
      @include stats-item;
      
      .summary-count {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 4px;
        
        &.total { color: $primary-color; }
        &.active { color: $active-color; }
        &.resolved { color: $fixed-color; }
      }
      
      .summary-label {
        font-size: 14px;
        color: #666;
      }
    }
  }
}