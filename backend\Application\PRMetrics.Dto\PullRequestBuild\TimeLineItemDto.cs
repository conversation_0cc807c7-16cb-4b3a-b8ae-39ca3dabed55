﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace RIB.PRMetrics.Application.Dto.PullRequestBuild
{
    /// <summary>
    /// Represents a single item in the build timeline for a pull request.
    /// This DTO holds various details about the build step, including timestamps, state, results, and associated logs or issues.
    /// </summary>
    public class TimeLineItemDto
    {
        /// <summary>
        /// Gets or sets the unique identifier for the timeline item.
        /// This ID is used to uniquely identify each step or entry in the build timeline.
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the parent ID for the timeline item.
        /// This property links the timeline item to its parent step, if applicable, creating a hierarchical structure.
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// Gets or sets the type of the timeline item.
        /// This property defines what kind of event or build step this item represents (e.g., build, test, deploy).
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Gets or sets the name of the timeline item.
        /// This property gives a human-readable label for the specific step in the timeline (e.g., "Build Step 1").
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the start time of the timeline item.
        /// This is the timestamp when the specific build step started.
        /// </summary>
        public string StartTime { get; set; }

        /// <summary>
        /// Gets or sets the finish time of the timeline item.
        /// This is the timestamp when the build step completed.
        /// </summary>
        public string FinishTime { get; set; }

        /// <summary>
        /// Gets or sets the state of the timeline item.
        /// This property represents the current state of the build step (e.g., "In Progress", "Completed", "Failed").
        /// </summary>
        public string state { get; set; }

        /// <summary>
        /// Gets or sets the result of the timeline item.
        /// This property represents the outcome of the build step (e.g., "Success", "Failure", "Skipped").
        /// </summary>
        public string Result { get; set; }

        /// <summary>
        /// Gets or sets the change ID associated with the timeline item.
        /// This ID is typically used to track the specific change or commit associated with the build step.
        /// </summary>
        public double ChangeId { get; set; }

        /// <summary>
        /// Gets or sets the last modified timestamp for the timeline item.
        /// This timestamp indicates when the timeline entry was last updated or modified.
        /// </summary>
        public string LastModified { get; set; }

        /// <summary>
        /// Gets or sets the error count for the timeline item.
        /// This property represents the number of errors encountered during the build step.
        /// </summary>
        public double ErrorCount { get; set; }

        /// <summary>
        /// Gets or sets the warning count for the timeline item.
        /// This property represents the number of warnings encountered during the build step.
        /// </summary>
        public double WarningCount { get; set; }

        /// <summary>
        /// Gets or sets the log associated with the timeline item.
        /// This property contains a log entry that can provide detailed information about the build step.
        /// </summary>
        public LogDto Log { get; set; }

        /// <summary>
        /// Gets or sets the list of issues associated with the timeline item.
        /// This property represents any issues encountered during the build step (e.g., errors, missing dependencies).
        /// </summary>
        public List<IssuesDto> Issues { get; set; }

        /// <summary>
        /// Gets or sets the attempt number for the timeline item.
        /// This indicates the attempt number for the build step, useful when a step is retried.
        /// </summary>
        public double Attempt { get; set; }

        /// <summary>
        /// Gets or sets the identifier for the timeline item.
        /// This identifier is used to track the specific build step or event in the context of the overall process.
        /// </summary>
        public string Identifier { get; set; }
    }
}
