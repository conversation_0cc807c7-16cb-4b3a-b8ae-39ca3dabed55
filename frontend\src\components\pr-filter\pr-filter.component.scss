/* Filter styling - extracted from pr-data.component.scss for reusability */
.filter-container {
  padding: 20px 24px 0;
  background-color: var(--card-bg-color, #ffffff);
  border-bottom: 1px solid var(--border-color, #e0e0e0);
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: space-between;
  
  /* Responsive behavior for mobile devices */  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    padding: 16px 16px 8px;
    gap: 12px;
  }
}

.filter-container mat-form-field {
  width: 100%;
  max-width: 200px;
  
  @media (max-width: 768px) {
    max-width: none;
  }
}

/* Date range container styling */
.date-range-container {
  display: flex;
  gap: 16px;
  flex: 2;
  align-items: center;
    @media (max-width: 768px) {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
    width: 100%;
    
    mat-form-field {
      flex: 1 1 calc(50% - 8px);
      min-width: 120px;
    }
  }
    @media (max-width: 480px) {
    flex-direction: column;
    
    mat-form-field {
      flex: 1 1 100%;
    }
  }
  
  /* Optimize date pickers for touch */
  ::ng-deep .mat-datepicker-toggle {
    @media (max-width: 768px) {
      .mat-datepicker-toggle-default-icon {
        width: 24px;
        height: 24px;
      }
      
      .mat-mdc-button-touch-target {
        width: 48px;
        height: 48px;
      }
    }
  }
  
  /* Ensure date picker calendar is properly positioned on mobile */
  ::ng-deep .mat-datepicker-content {
    @media (max-width: 768px) {
      margin-top: 4px;
    }
  }
}

/* Clear date button styling */
.clear-date-button {
  height: 36px;
  margin-bottom: 10px;
  background-color: transparent;
  color: var(--primary-color, #0078d4);
  
  &:disabled {
    color: var(--disabled-color, #cccccc);
  }
  
  mat-icon {
    margin-right: 4px;
    font-size: 18px;
  }
  
  @media (max-width: 768px) {
    margin-bottom: 0;
    margin-top: 4px;
    align-self: flex-start;
  }
}

::ng-deep .filter-container .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input {
    margin-left: 10px !important;
}

/* Status filter styling */
.status-filter {
  max-width: 200px;
  flex: 1;
  
  @media (max-width: 768px) {
    max-width: none;
    margin-bottom: 8px;
  }
}

/* Search input styling */
.search-input {
  max-width: 250px;
  flex: 1;
  
  @media (max-width: 768px) {
    max-width: none;
    margin-top: 8px;
    order: 3; /* Move search input to the end on mobile */
  }
}

/* Improved search field appearance */
::ng-deep .filter-container .mat-mdc-form-field {
  .mat-mdc-form-field-icon-suffix {
    display: flex;
    align-items: center;
  }
  
  .mat-mdc-form-field-hint {
    color: var(--primary-color, #0078d4);
    font-style: italic;
  }
  
  /* Improve touch targets on mobile */
  @media (max-width: 768px) {
    .mat-mdc-form-field-infix {
      min-height: 48px;
    }
    
    .mat-mdc-text-field-wrapper {
      padding-bottom: 0;
    }
    
    .mat-mdc-form-field-subscript-wrapper {
      height: 20px;
    }
    
    .mat-mdc-form-field-icon-suffix {
      padding: 0 4px;
    }
    
    .mdc-text-field--outlined {
      padding-left: 8px;
      padding-right: 8px;
    }
  }
}

/* Adjustments for small screens */
@media (max-width: 480px) {
  .filter-container {
    gap: 8px;
    padding: 12px 12px 0;
  }
  
  .date-range-container mat-form-field {
    margin-bottom: 0;
  }
  
  .date-range-container {
    padding-bottom: 8px;
    
    .clear-date-button {
      width: 100%;
      justify-content: center;
      margin-top: 8px;
    }
  }
}
