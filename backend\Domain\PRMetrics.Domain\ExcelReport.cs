﻿

namespace RIB.PRMetrics.Domain
{
    public class ExcelReport
    {
        /// <summary>
        /// Gets or sets the unique identifier for the pull request.
        /// </summary>
        public int PullRequestId { get; set; }

        /// <summary>
        /// Gets or sets the title of the pull request.
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// Gets or sets the status of the pull request (e.g., open, closed, merged).
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Gets or sets the creation date of the pull request.
        /// </summary>
        public DateTime CreationDate { get; set; }

        public int ActiveComment { get; set; }

        public int ResolvedComment { get; set; }

        public int TotalComment { get; set; }

        public string Author { get; set; }

        public string Reviewer { get; set; }

        public string SourceBranch { get; set; }

        public string PipelineStatus { get; set; }

        public string PipelineDetails { get; set; }
    }
}
