# PR Data Component

The PR Data Component displays a table of pull requests with various metrics and features for analyzing Git pull requests.

## Features

### Pull Request Table
The table displays the following columns:
- **PR ID**: Clickable ID that navigates to the PR details page
- **Title**: The title of the pull request
- **Created On**: Date and time when the PR was created
- **PR Comments**: Shows active and total comments with a refresh button
- **Status**: Current status of the pull request (Active, Completed, Abandoned, Draft)
- **Author**: The creator of the pull request
- **Reviewers**: List of reviewers with a clickable interface to view all reviewers
- **Source Branch**: The branch containing the changes
- **Pipeline Status**: Shows the status of the CI/CD pipeline associated with the PR
- **Merge Status**: Indicates if the PR can be merged cleanly or has conflicts

### Pipeline Status Feature
The Pipeline Status column shows the current state of the CI/CD pipeline for each pull request:

- **Success**: Displayed in green when the pipeline has successfully completed
- **Failed/Expired**: Displayed in red when the pipeline has failed or the build has expired
- **In Progress**: Displayed in orange/yellow when the pipeline is currently running
- **Unknown/Not Found**: Displayed in gray when no pipeline data is available

This information is fetched from the ContributionsHierarchyQuery API endpoint, which provides details about the Front-end CI build policy (configurationId 30).

## Implementation Details

### Data Loading
- The component fetches PR data with pagination support
- Comment counts are loaded on-demand or when refreshed
- Pipeline status is fetched for each PR after the initial data is loaded

### Filtering and Sorting
- Client-side filtering is available for quick searches
- Server-side filtering can be used for larger datasets
- Table columns are sortable

### Performance Considerations
- Loading states are shown during data fetching operations
- Separate loading indicators for server-side vs client-side operations
