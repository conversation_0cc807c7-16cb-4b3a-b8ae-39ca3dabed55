﻿/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

namespace  RIB.PRMetrics.Application.UseCases.Commons.Bases
{
    /// <summary>
    /// Represents a base response class that extends the generic response structure.
    /// Inherits from <see cref="BaseReponseGeneric{T}"/> to provide additional functionality or customization if needed.
    /// </summary>
    /// <typeparam name="T">The type of data that will be returned in the response.</typeparam>
    public class BaseResponse<T> : BaseReponseGeneric<T>
    {
        // This class currently acts as an alias to BaseReponseGeneric<T>.
        // You can extend it with additional properties or methods in the future if needed.
    }
}

